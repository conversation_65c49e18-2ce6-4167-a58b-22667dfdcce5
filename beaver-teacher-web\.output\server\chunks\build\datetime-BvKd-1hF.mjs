import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';

dayjs.extend(utc);
dayjs.extend(timezone);
const DEFAULT_TIMEZONE = "Asia/Shanghai";
const getNow = () => {
  return dayjs().tz(DEFAULT_TIMEZONE);
};
const parseDateTime = (date, time) => {
  const [hours, minutes] = time.split(":").map(Number);
  return dayjs.tz(`${date} ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:00`, DEFAULT_TIMEZONE);
};
const formatDate = (input, format = "YYYY-MM-DD") => {
  return dayjs(input).tz(DEFAULT_TIMEZONE).format(format);
};
const formatTime = (input, format = "HH:mm") => {
  return dayjs(input).tz(DEFAULT_TIMEZONE).format(format);
};
const getTodayString = () => {
  return getNow().format("YYYY-MM-DD");
};
const canEnterClass = (scheduleDate, startTime, endTime) => {
  const now_ts = Date.now();
  const courseStart_ts = parseDateTime(scheduleDate, startTime).valueOf();
  const courseEnd_ts = parseDateTime(scheduleDate, endTime).valueOf();
  const entryWindowStart_ts = courseStart_ts - 5 * 60 * 1e3;
  return now_ts >= entryWindowStart_ts && now_ts <= courseEnd_ts;
};
const getCountdownMinutes = (scheduleDate, startTime) => {
  const now = getNow();
  const courseStartTime = parseDateTime(scheduleDate, startTime);
  return courseStartTime.diff(now, "minute");
};
const canBookCourse = (scheduleDate, startTime) => {
  const now = getNow();
  const courseStartTime = parseDateTime(scheduleDate, startTime);
  return now.isBefore(courseStartTime);
};
const canCancelCourse = (scheduleDate, startTime) => {
  const now = getNow();
  const courseStartTime = parseDateTime(scheduleDate, startTime);
  return now.isBefore(courseStartTime.subtract(1, "hour"));
};
const calculateDuration = (startTime, endTime) => {
  const [startHour, startMinute] = startTime.split(":").map(Number);
  const [endHour, endMinute] = endTime.split(":").map(Number);
  return (endHour - startHour) * 60 + (endMinute - startMinute);
};
const generateDateRange = (days = 7, includeToday = true) => {
  const dates = [];
  const startDay = includeToday ? 0 : 1;
  for (let i = startDay; i < days + startDay; i++) {
    const date = getNow().add(i, "day");
    dates.push({
      value: date.format("YYYY-MM-DD"),
      display: date.format("M/D"),
      dayName: date.format("dddd")
    });
  }
  return dates;
};
const getTimeDisplayText = (input) => {
  const date = dayjs(input).tz(DEFAULT_TIMEZONE);
  const now = getNow();
  if (date.isSame(now, "day")) {
    return date.format("HH:mm");
  }
  if (date.isSame(now.subtract(1, "day"), "day")) {
    return "\u6628\u5929";
  }
  return date.format("M/D");
};
const convertUTC8ToLocalTime = (scheduleDate, startTime, endTime) => {
  const startDateTime = parseDateTime(scheduleDate, startTime);
  const endDateTime = parseDateTime(scheduleDate, endTime);
  const localStartTime = startDateTime.local().format("HH:mm");
  const localEndTime = endDateTime.local().format("HH:mm");
  const localDate = startDateTime.local().format("MMM DD");
  return `${localDate} | ${localStartTime} - ${localEndTime} (Local)`;
};

export { canEnterClass as a, canBookCourse as b, convertUTC8ToLocalTime as c, canCancelCourse as d, getCountdownMinutes as e, formatDate as f, generateDateRange as g, getTimeDisplayText as h, formatTime as i, getNow as j, calculateDuration as k, getTodayString as l, parseDateTime as p };
//# sourceMappingURL=datetime-BvKd-1hF.mjs.map
