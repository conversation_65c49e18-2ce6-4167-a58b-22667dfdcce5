import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_4 } from './Pagination-JPpl4AYq.mjs';
import { defineComponent, ref, watch, unref, withCtx, createTextVNode, toDisplayString, isRef, useSSRContext, nextTick } from 'vue';
import { ssrInterpolate, ssrRenderList, ssrRenderAttr, ssrRenderComponent } from 'vue/server-renderer';
import { f as useRouter, K as useAuthStore, c as useToast, B as useI18n, k as useRuntimeConfig } from './server.mjs';
import { s as studentApi } from './student-DtKAviut.mjs';
import { t as trtcApi } from './trtc-YskYwMtP.mjs';
import { c as convertUTC8ToLocalTime, a as canEnterClass, e as getCountdownMinutes, p as parseDateTime } from './datetime-BvKd-1hF.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "booked-courses",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const authStore = useAuthStore();
    const toast = useToast();
    const { t } = useI18n();
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const courses = ref([]);
    const loading = ref(false);
    const enteringClassId = ref(null);
    const currentLocalTime = ref("");
    const currentUTC8Time = ref("");
    const lastUpdateTime = ref("");
    ref(null);
    const forceUpdate = ref(0);
    ref(Date.now());
    const fetchCourses = async () => {
      var _a, _b;
      if (!((_b = (_a = authStore.user) == null ? void 0 : _a.student) == null ? void 0 : _b.id))
        return;
      loading.value = true;
      try {
        const response = await studentApi.getStudentBookedCourses({
          id: authStore.user.student.id,
          startDate: "",
          endDate: "",
          page: currentPage.value,
          pageSize: pageSize.value
        });
        courses.value = response.list;
        total.value = response.totalCount;
      } catch (error) {
        toast.add({
          title: "\u83B7\u53D6\u8BFE\u7A0B\u5217\u8868\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    };
    watch(currentPage, () => {
      fetchCourses();
    });
    const formatDate = (date) => {
      const d = new Date(date);
      const month = d.getMonth() + 1;
      const day = d.getDate();
      return `${month}\u6708${day}\u65E5`;
    };
    const parseDateTime$1 = (date, time) => {
      return parseDateTime(date, time);
    };
    const canEnterClass$1 = (course) => {
      forceUpdate.value;
      return canEnterClass(course.scheduleDate, course.startTime, course.endTime);
    };
    const getEnterTimeText = (course) => {
      forceUpdate.value;
      const diffMinutes = getCountdownMinutes(course.scheduleDate, course.startTime);
      if (diffMinutes > 5) {
        return t("messages.schedule.countdown.canEnterIn5Min");
      } else if (diffMinutes >= -60) {
        return t("messages.schedule.countdown.canEnterNow");
      }
      return "";
    };
    const getClassroomLink = async (course) => {
      var _a;
      if (enteringClassId.value)
        return;
      enteringClassId.value = course.id;
      try {
        const config = useRuntimeConfig();
        toast.add({
          title: t("messages.schedule.toast.preparingClass.title"),
          description: t("messages.schedule.toast.preparingClass.description"),
          color: "blue",
          timeout: 2e3,
          icon: "i-heroicons-arrow-path"
        });
        const { userSig, roomId } = await trtcApi.genSign({ courseId: course.id });
        const classroomUrl = new URL(`${config.public.rtcDomain}/#/class`);
        classroomUrl.searchParams.set("userId", ((_a = authStore.user) == null ? void 0 : _a.id) || "");
        classroomUrl.searchParams.set("roomId", roomId.toString());
        classroomUrl.searchParams.set("courseId", course.id);
        classroomUrl.searchParams.set("userSig", userSig);
        const beginTime = parseDateTime$1(course.scheduleDate, course.startTime).valueOf();
        const endTime = parseDateTime$1(course.scheduleDate, course.endTime).valueOf();
        classroomUrl.searchParams.set("classBeginTime", beginTime.toString());
        classroomUrl.searchParams.set("classEndTime", endTime.toString());
        const newWindow = (void 0).open(classroomUrl.toString(), "_blank");
        if (!newWindow || newWindow.closed) {
          toast.add({
            title: t("messages.schedule.toast.enteringClassSuccess.title"),
            description: t("messages.schedule.toast.enteringClassSuccess.description"),
            color: "green",
            timeout: 2e3,
            icon: "i-heroicons-check-circle"
          });
          await nextTick();
          await new Promise((resolve) => setTimeout(resolve, 500));
          (void 0).location.href = classroomUrl.toString();
          return;
        }
        toast.add({
          title: t("messages.schedule.toast.enteringClassPopup.title"),
          description: t("messages.schedule.toast.enteringClassPopup.description"),
          color: "green",
          timeout: 2e3,
          icon: "i-heroicons-check-circle"
        });
      } catch (error) {
        toast.add({
          title: t("messages.schedule.toast.enterClassError.title"),
          description: t("messages.schedule.toast.enterClassError.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        enteringClassId.value = null;
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_UPagination = __nuxt_component_4;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.bookedCourses.title"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6"><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6"><div class="flex items-center justify-between"><div class="flex items-center gap-6 sm:gap-8"><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.localTime"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentLocalTime))}</span></div><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.utc8Time"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentUTC8Time))}</span></div></div><div class="text-xs text-gray-500">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.lastUpdate"))}\uFF1A${ssrInterpolate(unref(lastUpdateTime))}</div></div></div><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6"><div class="hidden sm:flex items-center justify-between mb-4 pb-3 border-b border-gray-100"><h2 class="text-lg font-semibold">${ssrInterpolate(_ctx.$t("messages.bookedCourses.title"))}</h2></div>`);
      if (unref(loading)) {
        _push(`<div class="grid gap-4"><!--[-->`);
        ssrRenderList(3, (i) => {
          _push(`<div class="animate-pulse"><div class="h-20 bg-gray-100 rounded-lg"></div></div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<div class="space-y-0">`);
        if (unref(courses).length === 0) {
          _push(`<div class="text-center py-10 text-gray-500">${ssrInterpolate(_ctx.$t("messages.bookedCourses.empty"))}</div>`);
        } else {
          _push(`<div><!--[-->`);
          ssrRenderList(unref(courses), (course, index) => {
            _push(`<div class="py-3"><div class="flex justify-between items-start gap-4"><div class="flex items-start gap-4 min-w-0"><div class="w-14 h-14 sm:w-16 sm:h-16 rounded-full overflow-hidden flex-shrink-0"><img${ssrRenderAttr("src", course.teacherAvatarUrl || "/default-avatar.png")}${ssrRenderAttr("alt", course.teacherNickname)} class="w-full h-full object-cover aspect-square"></div><div class="min-w-0"><h3 class="text-base sm:text-lg font-medium mb-1 truncate">${ssrInterpolate(course.teacherNickname)}\u8001\u5E08\u7684${ssrInterpolate(course.classType)}</h3><div class="text-gray-600 text-sm space-y-1"><p>${ssrInterpolate(formatDate(course.scheduleDate))} \xB7 ${ssrInterpolate(course.startTime)} - ${ssrInterpolate(course.endTime)}</p><p class="text-xs text-gray-400">${ssrInterpolate(unref(convertUTC8ToLocalTime)(course.scheduleDate, course.startTime, course.endTime))}</p><p class="text-xs text-gray-400">${ssrInterpolate(getEnterTimeText(course))}</p></div></div></div>`);
            if (canEnterClass$1(course)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "primary",
                variant: "soft",
                size: "sm",
                loading: unref(enteringClassId) === course.id,
                disabled: unref(enteringClassId) === course.id,
                class: "!px-4 h-10 w-[84px] flex items-center justify-center whitespace-nowrap",
                onClick: ($event) => getClassroomLink(course)
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(`${ssrInterpolate(_ctx.$t("messages.schedule.actions.enterClass"))}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString(_ctx.$t("messages.schedule.actions.enterClass")), 1)
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else {
              _push(ssrRenderComponent(_component_UButton, {
                color: "gray",
                variant: "soft",
                size: "sm",
                disabled: "",
                class: "!px-4 h-10 w-[84px] flex items-center justify-center whitespace-nowrap"
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(`${ssrInterpolate(_ctx.$t("messages.schedule.actions.wait"))}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString(_ctx.$t("messages.schedule.actions.wait")), 1)
                    ];
                  }
                }),
                _: 2
              }, _parent));
            }
            _push(`</div>`);
            if (index !== unref(courses).length - 1) {
              _push(`<div class="h-px bg-gray-200 mt-3"></div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div>`);
      }
      if (unref(total) > unref(pageSize)) {
        _push(`<div class="mt-6 flex justify-center" dir="ltr">`);
        _push(ssrRenderComponent(_component_UPagination, {
          modelValue: unref(currentPage),
          "onUpdate:modelValue": ($event) => isRef(currentPage) ? currentPage.value = $event : null,
          total: unref(total),
          "page-size": unref(pageSize),
          ui: { rounded: "rounded-full" }
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/booked-courses.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=booked-courses-BL-4UTLQ.mjs.map
