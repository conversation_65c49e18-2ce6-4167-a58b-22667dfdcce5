import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_2$1 } from './ConfirmationDialog-C6YtqB9_.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_4 } from './Pagination-JPpl4AYq.mjs';
import { defineComponent, computed, ref, watch, unref, isRef, withCtx, createTextVNode, createVNode, toDisplayString, openBlock, createBlock, createCommentVNode, useSSRContext, nextTick } from 'vue';
import { ssrRenderComponent, ssrInterpolate, ssrRenderList } from 'vue/server-renderer';
import { B as useI18n, e as useRoute, f as useRouter, K as useAuthStore, c as useToast, k as useRuntimeConfig } from './server.mjs';
import { s as studentApi } from './student-DtKAviut.mjs';
import { T as TeacherScheduleStatus } from './api.d-D41uQaX7.mjs';
import { t as trtcApi } from './trtc-YskYwMtP.mjs';
import { g as generateDateRange, c as convertUTC8ToLocalTime, a as canEnterClass, f as formatDate, p as parseDateTime, b as canBookCourse, d as canCancelCourse } from './datetime-BvKd-1hF.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './useFormGroup-B3564yef.mjs';
import '@vueuse/core';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const route = useRoute();
    const router = useRouter();
    const authStore = useAuthStore();
    const toast = useToast();
    const teacherId = route.params.id;
    const studentId = computed(() => {
      var _a, _b;
      return (_b = (_a = authStore.user) == null ? void 0 : _a.student) == null ? void 0 : _b.id;
    });
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const courses = ref([]);
    const loading = ref(false);
    const selectedDate = ref(route.query.date || "");
    const weekDates = computed(() => {
      return generateDateRange(7, true);
    });
    const selectDate = (date) => {
      selectedDate.value = date;
      currentPage.value = 1;
      fetchCourses();
    };
    const clearDateFilter = () => {
      selectedDate.value = "";
      currentPage.value = 1;
      fetchCourses();
    };
    const showConfirmDialog = ref(false);
    const confirmDialogTitle = ref("");
    const confirmDialogMessage = ref("");
    const selectedCourse = ref(null);
    const enteringClassId = ref(null);
    const currentLocalTime = ref("");
    const currentUTC8Time = ref("");
    const lastUpdateTime = ref("");
    ref(null);
    ref(0);
    ref(Date.now());
    const showNicknameDialog = ref(false);
    const nickname = ref("");
    const submittingNickname = ref(false);
    const fetchCourses = async () => {
      loading.value = true;
      try {
        const response = await studentApi.getTeacherCourseList({
          teacherId,
          startDate: selectedDate.value,
          endDate: selectedDate.value,
          // 使用相同的日期
          page: currentPage.value,
          pageSize: pageSize.value
        });
        courses.value = response.list || [];
        total.value = response.totalCount || 0;
      } catch (error) {
        toast.add({
          title: "\u83B7\u53D6\u8BFE\u7A0B\u5217\u8868\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    };
    const formatDate$1 = (date) => {
      return formatDate(date, "MM-DD");
    };
    const handleBook = (course) => {
      selectedCourse.value = course;
      confirmDialogTitle.value = "\u786E\u8BA4\u9884\u7EA6";
      confirmDialogMessage.value = `\u786E\u5B9A\u8981\u9884\u7EA6 ${formatDate$1(course.scheduleDate)} ${course.startTime} - ${course.endTime} \u7684\u8BFE\u7A0B\u5417\uFF1F`;
      showConfirmDialog.value = true;
    };
    const confirmBooking = async () => {
      var _a, _b, _c, _d, _e, _f;
      if (!selectedCourse.value)
        return;
      try {
        await studentApi.bookCourse({
          teacherId: selectedCourse.value.teacherId,
          courseId: selectedCourse.value.id
        });
        if (!((_b = (_a = authStore.user) == null ? void 0 : _a.student) == null ? void 0 : _b.nickname) || ((_d = (_c = authStore.user) == null ? void 0 : _c.student) == null ? void 0 : _d.nickname) === "\u672A\u8BBE\u7F6E\u6635\u79F0") {
          await useAuthStore().fetchUserInfo();
          if (((_f = (_e = authStore.user) == null ? void 0 : _e.student) == null ? void 0 : _f.nickname) === "\u672A\u8BBE\u7F6E\u6635\u79F0") {
            showNicknameDialog.value = true;
            return;
          }
        }
        toast.add({
          title: "\u9884\u7EA6\u6210\u529F",
          description: "\u8BFE\u7A0B\u5DF2\u6210\u529F\u9884\u7EA6",
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        fetchCourses();
      } catch (error) {
        toast.add({
          title: "\u9884\u7EA6\u5931\u8D25",
          description: (error == null ? void 0 : error.message) || "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
        if ((error == null ? void 0 : error.code) === 100005) {
          router.push({
            path: "/student/course-cards/buy",
            query: {
              redirect: "/student/book/" + teacherId,
              teacherName: route.query.teacherName,
              type: "1"
              // 一对一课卡
            }
          });
        }
        if ((error == null ? void 0 : error.code) === 100006) {
          router.push({
            path: "/student/course-cards/buy",
            query: {
              redirect: "/student/book/" + teacherId,
              teacherName: route.query.teacherName,
              type: "2"
              // 小组课课卡
            }
          });
        }
      } finally {
        showConfirmDialog.value = false;
        selectedCourse.value = null;
      }
    };
    const handleCancel = (course) => {
      if (!canCancelCourse$1(course)) {
        toast.add({
          title: "\u65E0\u6CD5\u53D6\u6D88\u9884\u7EA6",
          description: "\u8BFE\u7A0B\u5F00\u59CB\u524D1\u5C0F\u65F6\u5185\u65E0\u6CD5\u53D6\u6D88\u9884\u7EA6",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
        return;
      }
      selectedCourse.value = course;
      confirmDialogTitle.value = "\u786E\u8BA4\u53D6\u6D88\u9884\u7EA6";
      confirmDialogMessage.value = `\u786E\u5B9A\u8981\u53D6\u6D88 ${formatDate$1(course.scheduleDate)} ${course.startTime} - ${course.endTime} \u7684\u8BFE\u7A0B\u9884\u7EA6\u5417\uFF1F`;
      showConfirmDialog.value = true;
    };
    const confirmCancellation = async () => {
      if (!selectedCourse.value)
        return;
      try {
        await studentApi.cancelCourse({
          courseId: selectedCourse.value.id
        });
        toast.add({
          title: "\u53D6\u6D88\u6210\u529F",
          description: "\u8BFE\u7A0B\u9884\u7EA6\u5DF2\u53D6\u6D88",
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        fetchCourses();
      } catch (error) {
        toast.add({
          title: "\u53D6\u6D88\u5931\u8D25",
          description: (error == null ? void 0 : error.message) || "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        showConfirmDialog.value = false;
        selectedCourse.value = null;
      }
    };
    const cancelDialog = () => {
      showConfirmDialog.value = false;
      selectedCourse.value = null;
    };
    const canNotEnterClassTips = (course) => {
      toast.add({
        title: "\u65E0\u6CD5\u8FDB\u5165\u8BFE\u5802",
        description: "\u8BFE\u7A0B\u5F00\u59CB\u524D5\u5206\u949F\u5230\u7ED3\u675F\u524D\u53EF\u4EE5\u8FDB\u5165\u8BFE\u5802",
        color: "red",
        timeout: 5e3,
        icon: "i-heroicons-x-circle"
      });
    };
    const handleEnterClass = async (course) => {
      var _a;
      if (enteringClassId.value)
        return;
      enteringClassId.value = course.id;
      try {
        const config = useRuntimeConfig();
        toast.add({
          title: t("messages.schedule.toast.preparingClass.title"),
          description: t("messages.schedule.toast.preparingClass.description"),
          color: "blue",
          timeout: 2e3,
          icon: "i-heroicons-arrow-path"
        });
        const { userSig, roomId } = await trtcApi.genSign({ courseId: course.id });
        const classroomUrl = new URL(`${config.public.rtcDomain}/#/class`);
        classroomUrl.searchParams.set("userId", ((_a = authStore.user) == null ? void 0 : _a.id) || "");
        classroomUrl.searchParams.set("roomId", roomId.toString());
        classroomUrl.searchParams.set("courseId", course.id);
        classroomUrl.searchParams.set("userSig", userSig);
        const beginTime = parseDateTime(course.scheduleDate, course.startTime).valueOf();
        const endTime = parseDateTime(course.scheduleDate, course.endTime).valueOf();
        classroomUrl.searchParams.set("classBeginTime", beginTime.toString());
        classroomUrl.searchParams.set("classEndTime", endTime.toString());
        const newWindow = (void 0).open(classroomUrl.toString(), "_blank");
        if (!newWindow || newWindow.closed) {
          toast.add({
            title: t("messages.schedule.toast.enteringClassSuccess.title"),
            description: t("messages.schedule.toast.enteringClassSuccess.description"),
            color: "green",
            timeout: 2e3,
            icon: "i-heroicons-check-circle"
          });
          await nextTick();
          await new Promise((resolve) => setTimeout(resolve, 500));
          (void 0).location.href = classroomUrl.toString();
          return;
        }
        toast.add({
          title: t("messages.schedule.toast.enteringClassPopup.title"),
          description: t("messages.schedule.toast.enteringClassPopup.description"),
          color: "green",
          timeout: 2e3,
          icon: "i-heroicons-check-circle"
        });
      } catch (error) {
        toast.add({
          title: t("messages.schedule.toast.enterClassError.title"),
          description: t("messages.schedule.toast.enterClassError.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        enteringClassId.value = null;
      }
    };
    const canBookCourse$1 = (course) => {
      return canBookCourse(course.scheduleDate, course.startTime);
    };
    const canCancelCourse$1 = (course) => {
      return canCancelCourse(course.scheduleDate, course.startTime);
    };
    watch(currentPage, () => {
      fetchCourses();
    });
    const getStatusDescription = (course) => {
      let description = "";
      if (course.status === TeacherScheduleStatus.Available) {
        if (canBookCourse$1(course)) {
          description = "(\u53EF\u9884\u7EA6)";
        } else {
          description = "(\u5DF2\u8FC7\u53EF\u9884\u7EA6\u65F6\u95F4)";
        }
      } else if (course.status === TeacherScheduleStatus.Booked && course.studentId === studentId.value) {
        description = canCancelCourse$1(course) ? "(\u63D0\u524D1\u5C0F\u65F6\u53EF\u53D6\u6D88\u9884\u7EA6)" : "(\u63D0\u524D5\u5206\u949F\u53EF\u8FDB\u5165)";
      } else if (course.status === TeacherScheduleStatus.Unavailable) {
        description = "(\u5DF2\u88AB\u4ED6\u4EBA\u9884\u7EA6)";
      } else if (course.status === TeacherScheduleStatus.Started) {
        description = "(\u8BFE\u7A0B\u5DF2\u5F00\u59CB)";
      }
      return description;
    };
    const submitNickname = async () => {
      var _a;
      if (!nickname.value.trim()) {
        toast.add({
          title: "\u8BF7\u8F93\u5165\u6635\u79F0",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
        return;
      }
      submittingNickname.value = true;
      try {
        await studentApi.updateStudentInfo(studentId.value, {
          nickname: nickname.value.trim()
        });
        if ((_a = authStore.user) == null ? void 0 : _a.student) {
          authStore.user.student.nickname = nickname.value.trim();
        }
        showNicknameDialog.value = false;
        toast.add({
          title: "\u6635\u79F0\u8BBE\u7F6E\u6210\u529F",
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        toast.add({
          title: "\u9884\u7EA6\u6210\u529F",
          description: "\u8BFE\u7A0B\u5DF2\u6210\u529F\u9884\u7EA6",
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        fetchCourses();
      } catch (error) {
        toast.add({
          title: "\u6635\u79F0\u8BBE\u7F6E\u5931\u8D25",
          description: (error == null ? void 0 : error.message) || "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        submittingNickname.value = false;
      }
    };
    const cancelNicknameDialog = () => {
      showNicknameDialog.value = false;
      toast.add({
        title: "\u9884\u7EA6\u6210\u529F",
        description: "\u8BFE\u7A0B\u5DF2\u6210\u529F\u9884\u7EA6",
        color: "green",
        timeout: 3e3,
        icon: "i-heroicons-check-circle"
      });
      fetchCourses();
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_ConfirmationDialog = __nuxt_component_2$1;
      const _component_UModal = __nuxt_component_6;
      const _component_UInput = __nuxt_component_9;
      const _component_UIcon = __nuxt_component_0;
      const _component_UPagination = __nuxt_component_4;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><div class="absolute left-2">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        onClick: ($event) => unref(router).back()
      }, null, _parent));
      _push(`</div><h1 class="text-base font-semibold">\u9884\u7EA6 ${ssrInterpolate(unref(route).query.teacherName || "")} \u8001\u5E08\u8BFE\u7A0B</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6">`);
      _push(ssrRenderComponent(_component_ConfirmationDialog, {
        visible: unref(showConfirmDialog),
        title: unref(confirmDialogTitle),
        message: unref(confirmDialogMessage),
        onConfirm: ($event) => {
          var _a;
          return ((_a = unref(selectedCourse)) == null ? void 0 : _a.status) === unref(TeacherScheduleStatus).Available ? confirmBooking() : confirmCancellation();
        },
        onCancel: cancelDialog
      }, null, _parent));
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: unref(showNicknameDialog),
        "onUpdate:modelValue": ($event) => isRef(showNicknameDialog) ? showNicknameDialog.value = $event : null,
        closeOnClickOutside: false,
        preventClose: true
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-4"${_scopeId}><h3 class="text-lg font-medium mb-4"${_scopeId}>\u4E3A\u65B9\u4FBF\u4E0E\u6559\u5E08\u4EA4\u6D41\uFF0C\u8BF7\u8BBE\u7F6E\u82F1\u6587\u540D\uFF1A</h3>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(nickname),
              "onUpdate:modelValue": ($event) => isRef(nickname) ? nickname.value = $event : null,
              placeholder: "\u8BF7\u8F93\u5165\u82F1\u6587\u540D",
              class: "mb-4"
            }, null, _parent2, _scopeId));
            _push2(`<div class="flex justify-end gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UButton, {
              color: "gray",
              variant: "soft",
              onClick: cancelNicknameDialog
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u53D6\u6D88 `);
                } else {
                  return [
                    createTextVNode(" \u53D6\u6D88 ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UButton, {
              color: "primary",
              loading: unref(submittingNickname),
              onClick: submitNickname
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u786E\u5B9A `);
                } else {
                  return [
                    createTextVNode(" \u786E\u5B9A ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "p-4" }, [
                createVNode("h3", { class: "text-lg font-medium mb-4" }, "\u4E3A\u65B9\u4FBF\u4E0E\u6559\u5E08\u4EA4\u6D41\uFF0C\u8BF7\u8BBE\u7F6E\u82F1\u6587\u540D\uFF1A"),
                createVNode(_component_UInput, {
                  modelValue: unref(nickname),
                  "onUpdate:modelValue": ($event) => isRef(nickname) ? nickname.value = $event : null,
                  placeholder: "\u8BF7\u8F93\u5165\u82F1\u6587\u540D",
                  class: "mb-4"
                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                createVNode("div", { class: "flex justify-end gap-2" }, [
                  createVNode(_component_UButton, {
                    color: "gray",
                    variant: "soft",
                    onClick: cancelNicknameDialog
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" \u53D6\u6D88 ")
                    ]),
                    _: 1
                  }),
                  createVNode(_component_UButton, {
                    color: "primary",
                    loading: unref(submittingNickname),
                    onClick: submitNickname
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" \u786E\u5B9A ")
                    ]),
                    _: 1
                  }, 8, ["loading"])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6"><div class="flex items-center justify-between"><div class="flex items-center gap-6 sm:gap-8"><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.localTime"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentLocalTime))}</span></div><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.utc8Time"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentUTC8Time))}</span></div></div><div class="text-xs text-gray-500">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.lastUpdate"))}\uFF1A${ssrInterpolate(unref(lastUpdateTime))}</div></div></div><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6"><div class="hidden sm:flex items-center gap-2 mb-4 pb-3 border-b border-gray-100">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        color: "gray",
        size: "sm",
        onClick: ($event) => unref(router).back()
      }, null, _parent));
      _push(`<h2 class="text-lg font-semibold">\u9884\u7EA6 ${ssrInterpolate(unref(route).query.teacherName || "")} \u8001\u5E08\u8BFE\u7A0B</h2></div><div class="mb-4 sm:mb-6"><div class="grid grid-cols-4 sm:grid-cols-8 gap-2">`);
      _push(ssrRenderComponent(_component_UButton, {
        color: !unref(selectedDate) ? "primary" : "gray",
        variant: !unref(selectedDate) ? "solid" : "soft",
        class: "w-full h-12 sm:h-12 justify-center rounded-lg",
        onClick: clearDateFilter
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex flex-col items-center leading-tight"${_scopeId}><span class="text-[13px]"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.date.filter.all"))}</span><span class="text-[11px]"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.date.filter.noLimit"))}</span></div>`);
          } else {
            return [
              createVNode("div", { class: "flex flex-col items-center leading-tight" }, [
                createVNode("span", { class: "text-[13px]" }, toDisplayString(_ctx.$t("messages.date.filter.all")), 1),
                createVNode("span", { class: "text-[11px]" }, toDisplayString(_ctx.$t("messages.date.filter.noLimit")), 1)
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--[-->`);
      ssrRenderList(unref(weekDates), (date) => {
        _push(ssrRenderComponent(_component_UButton, {
          key: date.value,
          color: unref(selectedDate) === date.value ? "primary" : "gray",
          variant: unref(selectedDate) === date.value ? "solid" : "soft",
          class: "w-full h-12 sm:h-12 justify-center rounded-lg",
          onClick: ($event) => selectDate(date.value)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="flex flex-col items-center leading-tight"${_scopeId}><span class="text-[13px]"${_scopeId}>${ssrInterpolate(date.dayName)}</span><span class="text-[11px]"${_scopeId}>${ssrInterpolate(date.display)}</span></div>`);
            } else {
              return [
                createVNode("div", { class: "flex flex-col items-center leading-tight" }, [
                  createVNode("span", { class: "text-[13px]" }, toDisplayString(date.dayName), 1),
                  createVNode("span", { class: "text-[11px]" }, toDisplayString(date.display), 1)
                ])
              ];
            }
          }),
          _: 2
        }, _parent));
      });
      _push(`<!--]--></div></div>`);
      if (unref(loading)) {
        _push(`<div class="flex justify-center py-12">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin text-3xl text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<div class="space-y-0">`);
        if (unref(courses).length === 0) {
          _push(`<div class="text-center py-10 text-gray-500"> \u6682\u65E0\u53EF\u9884\u7EA6\u7684\u8BFE\u7A0B </div>`);
        } else {
          _push(`<div><!--[-->`);
          ssrRenderList(unref(courses), (course, index) => {
            _push(`<div class="py-3"><div class="flex justify-between items-start gap-4"><div><div class="text-base sm:text-lg font-medium">${ssrInterpolate(formatDate$1(course.scheduleDate))} ${ssrInterpolate(course.startTime)} - ${ssrInterpolate(course.endTime)}</div><div class="text-sm text-gray-500">${ssrInterpolate(unref(convertUTC8ToLocalTime)(course.scheduleDate, course.startTime, course.endTime))}</div><div class="text-sm text-gray-500 mt-1">${ssrInterpolate(course.classType)} `);
            if (course.classType === "\u4E00\u5BF9\u591A\u8BFE\u7A0B") {
              _push(`<span class="text-xs text-gray-500"> (${ssrInterpolate(course.currentStudents || 0)}/${ssrInterpolate(course.maxStudents)}) </span>`);
            } else {
              _push(`<!---->`);
            }
            if (course.remark) {
              _push(`<span class="ml-2">(${ssrInterpolate(course.remark)})</span>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div><div class="text-sm mt-1 flex items-center gap-2"><span class="text-gray-400">${ssrInterpolate(getStatusDescription(course))}</span></div></div><div class="flex items-center gap-4">`);
            if (course.status === unref(TeacherScheduleStatus).Available && canBookCourse$1(course)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "primary",
                variant: "soft",
                size: "sm",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap",
                onClick: ($event) => handleBook(course)
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u9884\u7EA6 `);
                  } else {
                    return [
                      createTextVNode(" \u9884\u7EA6 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else {
              _push(`<!---->`);
            }
            if (course.status === unref(TeacherScheduleStatus).Available && !canBookCourse$1(course)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "gray",
                variant: "soft",
                size: "sm",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap"
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u4E0D\u53EF\u9884\u7EA6 `);
                  } else {
                    return [
                      createTextVNode(" \u4E0D\u53EF\u9884\u7EA6 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else {
              _push(`<!---->`);
            }
            if (course.status === unref(TeacherScheduleStatus).Unavailable) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "gray",
                variant: "soft",
                size: "sm",
                disabled: "",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap"
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u4E0D\u53EF\u9884\u7EA6 `);
                  } else {
                    return [
                      createTextVNode(" \u4E0D\u53EF\u9884\u7EA6 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else if (course.status === unref(TeacherScheduleStatus).Booked && course.studentId === unref(studentId) && canCancelCourse$1(course)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "blue",
                variant: "soft",
                size: "sm",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap",
                onClick: ($event) => handleCancel(course)
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u5DF2\u9884\u7EA6<br${_scopeId}> \u70B9\u51FB\u53D6\u6D88 `);
                  } else {
                    return [
                      createTextVNode(" \u5DF2\u9884\u7EA6"),
                      createVNode("br"),
                      createTextVNode(" \u70B9\u51FB\u53D6\u6D88 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else if ((course.status === unref(TeacherScheduleStatus).Booked || course.status === unref(TeacherScheduleStatus).Started) && course.studentId === unref(studentId) && unref(canEnterClass)(course.scheduleDate, course.startTime, course.endTime)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "blue",
                variant: "soft",
                size: "sm",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap",
                loading: unref(enteringClassId) === course.id,
                disabled: unref(enteringClassId) === course.id,
                onClick: ($event) => handleEnterClass(course)
              }, {
                leading: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    if (unref(enteringClassId) === course.id) {
                      _push2(ssrRenderComponent(_component_UIcon, {
                        name: "i-heroicons-arrow-path",
                        class: "animate-spin"
                      }, null, _parent2, _scopeId));
                    } else {
                      _push2(`<!---->`);
                    }
                  } else {
                    return [
                      unref(enteringClassId) === course.id ? (openBlock(), createBlock(_component_UIcon, {
                        key: 0,
                        name: "i-heroicons-arrow-path",
                        class: "animate-spin"
                      })) : createCommentVNode("", true)
                    ];
                  }
                }),
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u8FDB\u5165\u8BFE\u5802 `);
                  } else {
                    return [
                      createTextVNode(" \u8FDB\u5165\u8BFE\u5802 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else if (course.status === unref(TeacherScheduleStatus).Booked && course.studentId === unref(studentId) && !canCancelCourse$1(course)) {
              _push(ssrRenderComponent(_component_UButton, {
                color: "gray",
                variant: "soft",
                size: "sm",
                class: "!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap",
                onClick: ($event) => canNotEnterClassTips()
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(` \u8FDB\u5165\u8BFE\u5802 `);
                  } else {
                    return [
                      createTextVNode(" \u8FDB\u5165\u8BFE\u5802 ")
                    ];
                  }
                }),
                _: 2
              }, _parent));
            } else {
              _push(`<!---->`);
            }
            _push(`</div></div>`);
            if (index !== unref(courses).length - 1) {
              _push(`<div class="h-px bg-gray-200 mt-3"></div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div>`);
      }
      if (unref(total) > unref(pageSize)) {
        _push(`<div class="mt-6 flex justify-center" dir="ltr">`);
        _push(ssrRenderComponent(_component_UPagination, {
          modelValue: unref(currentPage),
          "onUpdate:modelValue": ($event) => isRef(currentPage) ? currentPage.value = $event : null,
          total: unref(total),
          "page-size": unref(pageSize),
          ui: { rounded: "rounded-full" }
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/book/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-DeNnG8L9.mjs.map
