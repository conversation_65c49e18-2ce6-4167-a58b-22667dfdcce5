import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_7 } from './Card-DSOtZzuw.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { defineComponent, computed, ref, reactive, watch, unref, createSlots, withCtx, isRef, createTextVNode, toDisplayString, createVNode, withModifiers, openBlock, createBlock, createCommentVNode, useSSRContext } from 'vue';
import { ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { f as useRouter, c as useToast, K as useAuthStore, B as useI18n, M as authApi } from './server.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './useFormGroup-B3564yef.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "password",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    const toast = useToast();
    const authStore = useAuthStore();
    const { t } = useI18n();
    const isDesktop = computed(() => (void 0).matchMedia ? (void 0).matchMedia("(min-width: 640px)").matches : true);
    const formData = ref({
      oldPassword: "",
      newPassword: ""
    });
    const confirmPassword = ref("");
    const loading = ref(false);
    const formState = reactive({
      oldPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
    const rules = {
      oldPassword: [
        { required: true, message: t("messages.auth.updatePassword.validation.oldPasswordRequired") }
      ],
      newPassword: [
        { required: true, message: t("messages.auth.updatePassword.validation.newPasswordRequired") },
        { min: 6, message: t("messages.auth.updatePassword.validation.newPasswordLength") },
        { max: 20, message: t("messages.auth.updatePassword.validation.newPasswordMaxLength") },
        {
          pattern: new RegExp(`^[a-zA-Z0-9!@#$%^&*()_+\\-=[\\]{};':"\\\\|,.<>/?]*$`),
          message: t("messages.auth.updatePassword.validation.newPasswordFormat")
        }
      ],
      confirmPassword: [
        { required: true, message: t("messages.auth.updatePassword.validation.confirmPasswordRequired") },
        {
          validator: (value) => value === formData.value.newPassword,
          message: t("messages.auth.updatePassword.validation.passwordMismatch")
        }
      ]
    };
    const validatePassword = (password) => {
      if (!password) {
        return rules.newPassword[0].message;
      }
      if (password.length < 6) {
        return rules.newPassword[1].message;
      }
      if (password.length > 20) {
        return rules.newPassword[2].message;
      }
      if (!rules.newPassword[3].pattern.test(password)) {
        return rules.newPassword[3].message;
      }
      return "";
    };
    watch(() => formData.value.oldPassword, (newVal) => {
      if (!newVal) {
        formState.oldPassword = rules.oldPassword[0].message;
      } else {
        formState.oldPassword = "";
      }
    });
    watch(() => formData.value.newPassword, (newVal) => {
      formState.newPassword = validatePassword(newVal);
      if (confirmPassword.value) {
        formState.confirmPassword = confirmPassword.value !== newVal ? rules.confirmPassword[1].message : "";
      }
    });
    watch(() => confirmPassword.value, (newVal) => {
      if (!newVal) {
        formState.confirmPassword = rules.confirmPassword[0].message;
      } else {
        formState.confirmPassword = newVal !== formData.value.newPassword ? rules.confirmPassword[1].message : "";
      }
    });
    const validateForm = () => {
      let isValid = true;
      if (!formData.value.oldPassword) {
        formState.oldPassword = rules.oldPassword[0].message;
        isValid = false;
      }
      const newPasswordError = validatePassword(formData.value.newPassword);
      if (newPasswordError) {
        formState.newPassword = newPasswordError;
        isValid = false;
      }
      if (!confirmPassword.value) {
        formState.confirmPassword = rules.confirmPassword[0].message;
        isValid = false;
      } else if (confirmPassword.value !== formData.value.newPassword) {
        formState.confirmPassword = rules.confirmPassword[1].message;
        isValid = false;
      }
      if (formData.value.oldPassword && formData.value.oldPassword === formData.value.newPassword) {
        formState.newPassword = t("messages.auth.updatePassword.validation.sameAsOld");
        isValid = false;
      }
      return isValid;
    };
    const handleSubmit = async () => {
      if (!validateForm())
        return;
      try {
        loading.value = true;
        await authApi.updatePassword(formData.value);
        toast.add({
          title: t("messages.auth.updatePassword.toast.success.title"),
          description: t("messages.auth.updatePassword.toast.success.description"),
          color: "green"
        });
        await authStore.logout();
        router.push("/signin");
      } catch (error) {
        console.error("\u4FEE\u6539\u5BC6\u7801\u5931\u8D25:", error);
        toast.add({
          title: t("messages.auth.updatePassword.toast.error.title"),
          description: (error == null ? void 0 : error.message) || t("messages.auth.updatePassword.toast.error.description"),
          color: "red"
        });
      } finally {
        loading.value = false;
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_UCard = __nuxt_component_7;
      const _component_UInput = __nuxt_component_9;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><div class="absolute left-2">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        onClick: ($event) => unref(router).back()
      }, null, _parent));
      _push(`</div><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.title"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-xl mx-auto w-full py-4 sm:py-6">`);
      _push(ssrRenderComponent(_component_UCard, null, createSlots({
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          var _a, _b;
          if (_push2) {
            _push2(`<form class="space-y-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-1"${_scopeId}> e-Mail </label>`);
            _push2(ssrRenderComponent(_component_UInput, {
              type: "email",
              "model-value": (_a = unref(authStore).user) == null ? void 0 : _a.email,
              disabled: true,
              ui: {
                size: {
                  default: "lg"
                },
                base: "cursor-not-allowed !bg-gray-200 dark:!bg-gray-400 !opacity-50",
                color: {
                  gray: {
                    outline: "!ring-2 !ring-gray-400 dark:!ring-gray-400"
                  }
                }
              }
            }, null, _parent2, _scopeId));
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-1"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.form.oldPassword"))} <span class="text-red-500"${_scopeId}>*</span></label>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(formData).oldPassword,
              "onUpdate:modelValue": ($event) => unref(formData).oldPassword = $event,
              type: "password",
              placeholder: _ctx.$t("messages.auth.updatePassword.form.oldPasswordPlaceholder"),
              error: !!unref(formState).oldPassword,
              ui: {
                size: {
                  default: "lg"
                }
              }
            }, null, _parent2, _scopeId));
            if (unref(formState).oldPassword) {
              _push2(`<p class="mt-1 text-sm text-red-500"${_scopeId}>${ssrInterpolate(unref(formState).oldPassword)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-1"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.form.newPassword"))} <span class="text-red-500"${_scopeId}>*</span></label>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(formData).newPassword,
              "onUpdate:modelValue": ($event) => unref(formData).newPassword = $event,
              type: "password",
              placeholder: _ctx.$t("messages.auth.updatePassword.form.newPasswordPlaceholder"),
              error: !!unref(formState).newPassword,
              ui: {
                size: {
                  default: "lg"
                }
              }
            }, null, _parent2, _scopeId));
            if (unref(formState).newPassword) {
              _push2(`<p class="mt-1 text-sm text-red-500"${_scopeId}>${ssrInterpolate(unref(formState).newPassword)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`<p class="mt-1 text-sm text-gray-500"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.form.passwordHint"))}</p></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-1"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.form.confirmPassword"))} <span class="text-red-500"${_scopeId}>*</span></label>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(confirmPassword),
              "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
              type: "password",
              placeholder: _ctx.$t("messages.auth.updatePassword.form.confirmPasswordPlaceholder"),
              error: !!unref(formState).confirmPassword,
              ui: {
                size: {
                  default: "lg"
                }
              }
            }, null, _parent2, _scopeId));
            if (unref(formState).confirmPassword) {
              _push2(`<p class="mt-1 text-sm text-red-500"${_scopeId}>${ssrInterpolate(unref(formState).confirmPassword)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div class="flex justify-end space-x-4"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UButton, {
              to: "/teacher/account",
              variant: "outline",
              color: "gray"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.buttons.cancel"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.buttons.cancel")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UButton, {
              type: "submit",
              color: "primary",
              loading: unref(loading)
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.buttons.confirm"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.buttons.confirm")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div></form>`);
          } else {
            return [
              createVNode("form", {
                onSubmit: withModifiers(handleSubmit, ["prevent"]),
                class: "space-y-6"
              }, [
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-1" }, " e-Mail "),
                  createVNode(_component_UInput, {
                    type: "email",
                    "model-value": (_b = unref(authStore).user) == null ? void 0 : _b.email,
                    disabled: true,
                    ui: {
                      size: {
                        default: "lg"
                      },
                      base: "cursor-not-allowed !bg-gray-200 dark:!bg-gray-400 !opacity-50",
                      color: {
                        gray: {
                          outline: "!ring-2 !ring-gray-400 dark:!ring-gray-400"
                        }
                      }
                    }
                  }, null, 8, ["model-value"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-1" }, [
                    createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.form.oldPassword")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  createVNode(_component_UInput, {
                    modelValue: unref(formData).oldPassword,
                    "onUpdate:modelValue": ($event) => unref(formData).oldPassword = $event,
                    type: "password",
                    placeholder: _ctx.$t("messages.auth.updatePassword.form.oldPasswordPlaceholder"),
                    error: !!unref(formState).oldPassword,
                    ui: {
                      size: {
                        default: "lg"
                      }
                    }
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                  unref(formState).oldPassword ? (openBlock(), createBlock("p", {
                    key: 0,
                    class: "mt-1 text-sm text-red-500"
                  }, toDisplayString(unref(formState).oldPassword), 1)) : createCommentVNode("", true)
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-1" }, [
                    createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.form.newPassword")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  createVNode(_component_UInput, {
                    modelValue: unref(formData).newPassword,
                    "onUpdate:modelValue": ($event) => unref(formData).newPassword = $event,
                    type: "password",
                    placeholder: _ctx.$t("messages.auth.updatePassword.form.newPasswordPlaceholder"),
                    error: !!unref(formState).newPassword,
                    ui: {
                      size: {
                        default: "lg"
                      }
                    }
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                  unref(formState).newPassword ? (openBlock(), createBlock("p", {
                    key: 0,
                    class: "mt-1 text-sm text-red-500"
                  }, toDisplayString(unref(formState).newPassword), 1)) : createCommentVNode("", true),
                  createVNode("p", { class: "mt-1 text-sm text-gray-500" }, toDisplayString(_ctx.$t("messages.auth.updatePassword.form.passwordHint")), 1)
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-1" }, [
                    createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.form.confirmPassword")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  createVNode(_component_UInput, {
                    modelValue: unref(confirmPassword),
                    "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                    type: "password",
                    placeholder: _ctx.$t("messages.auth.updatePassword.form.confirmPasswordPlaceholder"),
                    error: !!unref(formState).confirmPassword,
                    ui: {
                      size: {
                        default: "lg"
                      }
                    }
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                  unref(formState).confirmPassword ? (openBlock(), createBlock("p", {
                    key: 0,
                    class: "mt-1 text-sm text-red-500"
                  }, toDisplayString(unref(formState).confirmPassword), 1)) : createCommentVNode("", true)
                ]),
                createVNode("div", { class: "flex justify-end space-x-4" }, [
                  createVNode(_component_UButton, {
                    to: "/teacher/account",
                    variant: "outline",
                    color: "gray"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.buttons.cancel")), 1)
                    ]),
                    _: 1
                  }),
                  createVNode(_component_UButton, {
                    type: "submit",
                    color: "primary",
                    loading: unref(loading)
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(_ctx.$t("messages.auth.updatePassword.buttons.confirm")), 1)
                    ]),
                    _: 1
                  }, 8, ["loading"])
                ])
              ], 32)
            ];
          }
        }),
        _: 2
      }, [
        unref(isDesktop) ? {
          name: "header",
          fn: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="flex items-center justify-between"${_scopeId}><div class="flex items-center gap-2"${_scopeId}>`);
              _push2(ssrRenderComponent(_component_UButton, {
                icon: "i-heroicons-arrow-left",
                variant: "ghost",
                color: "gray",
                size: "sm",
                onClick: ($event) => unref(router).back()
              }, null, _parent2, _scopeId));
              _push2(`<h2 class="text-lg font-semibold"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.title"))}</h2></div></div>`);
            } else {
              return [
                createVNode("div", { class: "flex items-center justify-between" }, [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode(_component_UButton, {
                      icon: "i-heroicons-arrow-left",
                      variant: "ghost",
                      color: "gray",
                      size: "sm",
                      onClick: ($event) => unref(router).back()
                    }, null, 8, ["onClick"]),
                    createVNode("h2", { class: "text-lg font-semibold" }, toDisplayString(_ctx.$t("messages.auth.updatePassword.title")), 1)
                  ])
                ])
              ];
            }
          }),
          key: "0"
        } : void 0
      ]), _parent));
      _push(`</div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/password.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=password-Xb45X9mb.mjs.map
