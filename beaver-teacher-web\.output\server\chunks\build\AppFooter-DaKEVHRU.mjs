import { useSSRContext, defineComponent, mergeProps } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AppFooter",
  __ssrInlineRender: true,
  setup(__props) {
    useI18n();
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<footer${ssrRenderAttrs(mergeProps({ class: "relative mt-auto bg-gray-50 border-t border-gray-200" }, _attrs))} data-v-4db3f0d4><div class="container mx-auto px-4 py-3" data-v-4db3f0d4><div class="flex flex-col sm:flex-row justify-center items-center gap-2 text-xs text-gray-500" data-v-4db3f0d4><div data-v-4db3f0d4>\xA9 BeaverLingua</div><div data-v-4db3f0d4><a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" class="hover:text-primary transition-colors duration-200"${ssrRenderAttr("title", _ctx.$t("messages.common.footer.icpTitle"))} data-v-4db3f0d4>${ssrInterpolate(_ctx.$t("messages.common.footer.icp"))}</a></div></div></div></footer>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/generalElements/AppFooter.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const __nuxt_component_2 = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-4db3f0d4"]]);

export { __nuxt_component_2 as _ };
//# sourceMappingURL=AppFooter-DaKEVHRU.mjs.map
