import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { useSSRContext, defineComponent, mergeProps, unref, withCtx, createVNode, createTextVNode, toDisplayString } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "static-success",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-gray-50 p-4" }, _attrs))} data-v-f3011b87><div class="max-w-md w-full" data-v-f3011b87><div class="glass bg-white/90 rounded-2xl p-8 text-center mb-4 backdrop-blur-sm border border-white/20" data-v-f3011b87><div class="py-8" data-v-f3011b87>`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-check-circle",
        class: "w-16 h-16 mx-auto text-green-500 pulse-green"
      }, null, _parent));
      _push(`<h1 class="mt-4 text-2xl font-bold text-green-600" data-v-f3011b87>${ssrInterpolate(unref(t)("messages.payment.success.title"))}</h1><p class="mt-2 text-gray-600" data-v-f3011b87>${ssrInterpolate(unref(t)("messages.payment.success.description"))}</p></div></div><div class="space-y-3" data-v-f3011b87>`);
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        size: "lg",
        color: "green",
        class: "font-semibold min-h-[44px]",
        to: "/student/course-cards"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-credit-card",
              class: "mr-2"
            }, null, _parent2, _scopeId));
            _push2(` ${ssrInterpolate(unref(t)("messages.payment.success.buttons.viewCards"))}`);
          } else {
            return [
              createVNode(_component_UIcon, {
                name: "i-heroicons-credit-card",
                class: "mr-2"
              }),
              createTextVNode(" " + toDisplayString(unref(t)("messages.payment.success.buttons.viewCards")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        size: "lg",
        variant: "outline",
        color: "gray",
        class: "font-semibold min-h-[44px]",
        to: "/student/course-cards/buy"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-shopping-cart",
              class: "mr-2"
            }, null, _parent2, _scopeId));
            _push2(` ${ssrInterpolate(unref(t)("messages.payment.success.buttons.continuePurchase"))}`);
          } else {
            return [
              createVNode(_component_UIcon, {
                name: "i-heroicons-shopping-cart",
                class: "mr-2"
              }),
              createTextVNode(" " + toDisplayString(unref(t)("messages.payment.success.buttons.continuePurchase")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/payment/static-success.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const staticSuccess = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-f3011b87"]]);

export { staticSuccess as default };
//# sourceMappingURL=static-success-BelCdtZd.mjs.map
