import { N as request } from './server.mjs';

const studentApi = {
  // 获取学生信息 
  async getStudentInfo(id) {
    return await request({
      method: "GET",
      url: `/api/student/${id}`
    });
  },
  // 获取所有已排课的教师列表
  async getSchedulesTeacherList(params) {
    return await request({
      method: "GET",
      url: "/api/student/schedules/all",
      params
    });
  },
  // 获取教师课程列表
  async getTeacherCourseList(params) {
    return await request({
      method: "GET",
      url: `/api/student/teacher/${params.teacherId}/courses`,
      params
    });
  },
  // 预约课程
  async bookCourse(params) {
    return await request({
      method: "POST",
      url: `/api/student/teacher/${params.teacherId}/courses/${params.courseId}/book`
    });
  },
  // 获取课程卡列表
  async getCourseCardsList(params) {
    return await request({
      method: "GET",
      url: "/api/student/course-cards/list",
      params
    });
  },
  // 获取课程卡价格信息（用于安全支付）
  async getCourseCardPrice(cardId) {
    return await request({
      method: "GET",
      url: `/api/course-cards/${cardId}/price`
    });
  },
  // 获取学生课卡列表
  async getStudentCourseCards(id) {
    return await request({
      method: "GET",
      url: `/api/student/${id}/course-cards`
    });
  },
  //购买课卡,id为课卡id
  // async buyCourseCard(id: string): Promise<any> {
  //   return await request({
  //     method: 'POST',
  //     url: `/api/student/course-cards/${id}/buy`
  //   })
  // },
  // 获取学生预约课程列表
  async getStudentBookedCourses(params) {
    return await request({
      method: "GET",
      url: `/api/student/${params.id}/booked-courses`,
      params
    });
  },
  // 获取学生课卡详细列表
  async getStudentCourseCardDetails(id) {
    return await request({
      method: "GET",
      url: `/api/student/${id}/course-cards/detail`
    });
  },
  //更新学生信息
  async updateStudentInfo(id, params) {
    return await request({
      method: "PUT",
      url: `/api/student/update/${id}`,
      data: params
    });
  },
  // 更新用户同意协议状态
  async updateAgreementStatus(agreeAgreement) {
    return await request({
      method: "POST",
      url: `/api/student/agree-agreement`,
      params: {
        agreeAgreement
      }
    });
  },
  // 取消课程
  async cancelCourse(params) {
    return await request({
      method: "POST",
      url: `/api/student/courses/${params.courseId}/cancel`
    });
  },
  // 获取教师详情
  async getTeacherDetails(teacherId) {
    return await request({
      method: "GET",
      url: `/api/student/teacher/${teacherId}`
    });
  }
};

export { studentApi as s };
//# sourceMappingURL=student-DtKAviut.mjs.map
