import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0$1 } from './nuxt-link-DAFz7xX6.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_2 } from './ConfirmationDialog-C6YtqB9_.mjs';
import { useSSRContext, defineComponent, computed, ref, unref, withCtx, createTextVNode, toDisplayString, createVNode, isRef, openBlock, createBlock, createCommentVNode } from 'vue';
import { ssrInterpolate, ssrRenderAttr, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { K as useAuthStore, f as useRouter, B as useI18n, c as useToast } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './Button-3EsiVOgL.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const authStore = useAuthStore();
    const router = useRouter();
    const { t } = useI18n();
    const user = computed(() => authStore.user);
    const isTeacher = computed(() => {
      var _a;
      return !!((_a = user.value) == null ? void 0 : _a.teacher);
    });
    const teacherInfo = ref(null);
    ref(false);
    const showConfirmDialog = ref(false);
    computed(() => {
      if (isTeacher.value)
        return t("messages.profile.userInfo.teacher");
      return t("messages.profile.userInfo.normalUser");
    });
    computed(() => {
      var _a;
      const status = (_a = user.value) == null ? void 0 : _a.status;
      switch (status) {
        case 1:
          return t("messages.profile.userInfo.status.normal");
        case 0:
          return t("messages.profile.userInfo.status.disabled");
        case 2:
          return t("messages.profile.userInfo.status.pending");
        default:
          return t("messages.profile.userInfo.status.unknown");
      }
    });
    const formatDate = (date) => {
      if (!date)
        return "-";
      return new Date(date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric"
      });
    };
    const showImagePreview = ref(false);
    const previewImageUrl = ref("");
    const handleLogout = async () => {
      const toast = useToast();
      try {
        await authStore.logout();
        toast.add({
          title: t("messages.auth.logout.success.title"),
          description: t("messages.auth.logout.success.description"),
          color: "green",
          timeout: 3e3
        });
        router.push("/");
      } catch (error) {
        toast.add({
          title: t("messages.toast.error.title"),
          description: t("messages.toast.error.description"),
          color: "red",
          timeout: 3e3
        });
      }
    };
    const onConfirm = () => {
      showConfirmDialog.value = false;
      handleLogout();
    };
    const onCancel = () => {
      showConfirmDialog.value = false;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i;
      const _component_UIcon = __nuxt_component_0;
      const _component_UBadge = __nuxt_component_1;
      const _component_NuxtLink = __nuxt_component_0$1;
      const _component_UModal = __nuxt_component_6;
      const _component_ConfirmationDialog = __nuxt_component_2;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center" data-v-ab10c1f8><h1 class="text-base font-semibold" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.menu.account"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area" data-v-ab10c1f8><div class="max-w-4xl mx-auto w-full py-4 sm:py-6" data-v-ab10c1f8><h2 class="hidden sm:block text-lg font-semibold mb-3" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.menu.account"))}</h2><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4" data-v-ab10c1f8><div class="flex items-center" data-v-ab10c1f8><div class="w-16 h-16 rounded-full bg-gray-200 mr-4 overflow-hidden flex-shrink-0" data-v-ab10c1f8>`);
      if ((_b = (_a = unref(user)) == null ? void 0 : _a.teacher) == null ? void 0 : _b.avatarUrl) {
        _push(`<img${ssrRenderAttr("src", (_d = (_c = unref(user)) == null ? void 0 : _c.teacher) == null ? void 0 : _d.avatarUrl)}${ssrRenderAttr("alt", ((_e = unref(user)) == null ? void 0 : _e.username) || "avatar")} class="w-full h-full object-cover" data-v-ab10c1f8>`);
      } else {
        _push(`<div class="w-full h-full bg-gray-200 flex items-center justify-center" data-v-ab10c1f8>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-user-20-solid",
          class: "w-8 h-8 text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      }
      _push(`</div><div class="min-w-0" data-v-ab10c1f8><h3 class="text-xl font-bold truncate" data-v-ab10c1f8>${ssrInterpolate(((_f = unref(user)) == null ? void 0 : _f.username) || "-")}</h3><p class="text-gray-500 truncate" data-v-ab10c1f8>${ssrInterpolate(((_g = unref(user)) == null ? void 0 : _g.email) || "-")}</p><p class="text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.userInfo.registerTime"))}\uFF1A${ssrInterpolate(formatDate((_h = unref(user)) == null ? void 0 : _h.createdTime))}</p></div></div></div>`);
      if (unref(isTeacher) && unref(teacherInfo)) {
        _push(`<div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4" data-v-ab10c1f8><h3 class="text-base font-semibold mb-4" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfo"))}</h3><div class="grid grid-cols-1 sm:grid-cols-2 gap-4" data-v-ab10c1f8><div data-v-ab10c1f8><div class="text-xs text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfos.realName"))}</div><div class="text-sm text-gray-900 break-words" data-v-ab10c1f8>${ssrInterpolate(unref(teacherInfo).realName || "-")}</div></div><div data-v-ab10c1f8><div class="text-xs text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfos.nickname"))}</div><div class="text-sm text-gray-900 break-words" data-v-ab10c1f8>${ssrInterpolate(unref(teacherInfo).nickname || "-")}</div></div><div data-v-ab10c1f8><div class="text-xs text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfos.graduatedSchool"))}</div><div class="text-sm text-gray-900 break-words" data-v-ab10c1f8>${ssrInterpolate(unref(teacherInfo).graduatedSchool || "-")}</div></div><div data-v-ab10c1f8><div class="text-xs text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfos.specialty"))}</div><div class="text-sm text-gray-900 break-words" data-v-ab10c1f8>${ssrInterpolate(unref(teacherInfo).specialty || "-")}</div></div><div data-v-ab10c1f8><div class="text-xs text-gray-500" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.teacherInfos.lastTeachingTime"))}</div><div class="text-sm text-gray-900 break-words" data-v-ab10c1f8>${ssrInterpolate(formatDate(unref(teacherInfo).lastTeachingTime))}</div></div></div>`);
        if ((_i = unref(teacherInfo).tags) == null ? void 0 : _i.length) {
          _push(`<div class="border-t pt-4 mt-4" data-v-ab10c1f8><h4 class="text-sm font-medium mb-3" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.certificates"))}</h4><div class="grid grid-cols-1 md:grid-cols-2 gap-4" data-v-ab10c1f8><!--[-->`);
          ssrRenderList(unref(teacherInfo).tags, (tag) => {
            _push(`<div class="bg-gray-50 p-4 rounded-lg" data-v-ab10c1f8><div class="flex items-start justify-between mb-3" data-v-ab10c1f8><div data-v-ab10c1f8><h5 class="font-medium text-gray-900" data-v-ab10c1f8>${ssrInterpolate(tag.tagName)}</h5><p class="text-sm text-gray-600 mt-1" data-v-ab10c1f8>${ssrInterpolate(tag.tagContent)}</p></div>`);
            _push(ssrRenderComponent(_component_UBadge, {
              color: tag.tagType === "certificate" ? "blue" : "green"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(`${ssrInterpolate(tag.tagType === "certificate" ? _ctx.$t("messages.profile.certificateInfo.certificate") : _ctx.$t("messages.profile.certificateInfo.skill"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(tag.tagType === "certificate" ? _ctx.$t("messages.profile.certificateInfo.certificate") : _ctx.$t("messages.profile.certificateInfo.skill")), 1)
                  ];
                }
              }),
              _: 2
            }, _parent));
            _push(`</div>`);
            if (tag.fileUrl) {
              _push(`<div class="relative aspect-[4/3] rounded-lg overflow-hidden bg-gray-100 max-w-xs mx-auto" data-v-ab10c1f8><img${ssrRenderAttr("src", tag.fileUrl)}${ssrRenderAttr("alt", tag.tagName)} class="w-full h-full object-contain" data-v-ab10c1f8></div>`);
            } else {
              _push(`<!---->`);
            }
            if (tag.remark) {
              _push(`<div class="mt-2 text-sm text-gray-500" data-v-ab10c1f8><p class="font-medium" data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.profile.certificateInfo.remark"))}\uFF1A</p><p data-v-ab10c1f8>${ssrInterpolate(tag.remark)}</p></div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div>`);
          });
          _push(`<!--]--></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="bg-white rounded-xl border border-gray-200 overflow-hidden" data-v-ab10c1f8>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/teacher/password",
        class: "flex items-center justify-between p-4 border-b"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<span data-v-ab10c1f8${_scopeId}>${ssrInterpolate(_ctx.$t("messages.auth.updatePassword.button"))}</span>`);
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-chevron-right",
              class: "w-5 h-5 text-gray-400"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode("span", null, toDisplayString(_ctx.$t("messages.auth.updatePassword.button")), 1),
              createVNode(_component_UIcon, {
                name: "i-heroicons-chevron-right",
                class: "w-5 h-5 text-gray-400"
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/teacher/service",
        class: "flex items-center justify-between p-4 border-b"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<span data-v-ab10c1f8${_scopeId}>${ssrInterpolate(_ctx.$t("messages.common.customerService.button"))}</span>`);
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-chevron-right",
              class: "w-5 h-5 text-gray-400"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode("span", null, toDisplayString(_ctx.$t("messages.common.customerService.button")), 1),
              createVNode(_component_UIcon, {
                name: "i-heroicons-chevron-right",
                class: "w-5 h-5 text-gray-400"
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<button class="flex items-center justify-between p-4 w-full text-left text-red-500 hover:bg-red-50" data-v-ab10c1f8><span data-v-ab10c1f8>${ssrInterpolate(_ctx.$t("messages.auth.logout.button"))}</span>`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-arrow-right-on-rectangle",
        class: "w-5 h-5"
      }, null, _parent));
      _push(`</button></div>`);
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: unref(showImagePreview),
        "onUpdate:modelValue": ($event) => isRef(showImagePreview) ? showImagePreview.value = $event : null,
        ui: { width: "max-w-3xl" }
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            if (unref(previewImageUrl)) {
              _push2(`<img${ssrRenderAttr("src", unref(previewImageUrl))} class="w-full h-auto"${ssrRenderAttr("alt", _ctx.$t("messages.profile.preview"))} data-v-ab10c1f8${_scopeId}>`);
            } else {
              _push2(`<!---->`);
            }
          } else {
            return [
              unref(previewImageUrl) ? (openBlock(), createBlock("img", {
                key: 0,
                src: unref(previewImageUrl),
                class: "w-full h-auto",
                alt: _ctx.$t("messages.profile.preview")
              }, null, 8, ["src", "alt"])) : createCommentVNode("", true)
            ];
          }
        }),
        _: 1
      }, _parent));
      if (unref(showConfirmDialog)) {
        _push(ssrRenderComponent(_component_ConfirmationDialog, {
          title: _ctx.$t("messages.profile.confirmDialog.title"),
          message: _ctx.$t("messages.profile.confirmDialog.message"),
          visible: unref(showConfirmDialog),
          onConfirm,
          onCancel
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/account/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-ab10c1f8"]]);

export { index as default };
//# sourceMappingURL=index-efkCDBn2.mjs.map
