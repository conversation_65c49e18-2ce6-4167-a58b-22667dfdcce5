{"file": "join-BkmH8_n6.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,UAAM,QAAQ;AAKd,UAAM,OAAO;AAKb,UAAM,YAAY,SAAS;AAAA,MACzB,KAAK,MAAM,MAAM;AAAA,MACjB,KAAK,CAAC,UAAU,KAAK,qBAAqB,KAAK;AAAA,IAAA,CAChD;AAGK,UAAA,eAAe,CAAC,SAAiB;AAC7B,cAAA,IAAI,kBAAkB,IAAI;AAExB,gBAAA,QAAQ,2BAA2B,SAAS,MAAM;AAEtD,YAAA,KAAK,aAAa,OAAO,GAAG;AACxB,gBAAA,QAAQ,KAAK,aAAa,OAAO;AACvC,cAAI,OAAO;AAEH,kBAAA,gBAAgB,MAAM,MAAM,GAAG,EAClC,IAAI,CAAA,MAAK,EAAE,KAAA,CAAM,EACjB,OAAO,CAAK,MAAA,CAAC,EACb,IAAI,CAAA,MAAK,GAAG,CAAC,aAAa,EAC1B,KAAK,GAAG;AACN,iBAAA,aAAa,SAAS,aAAa;AAExC,iBAAK,gBAAgB,OAAO;AAAA,UAAA;AAAA,QAC9B;AAAA,MACF,CACD;AAEK,YAAA,YAAY,UAAU,SAAS,MAAM;AAAA,QACzC,UAAU;AAAA;AAAA,UAER;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAK;AAAA,UAAM;AAAA,UAAU;AAAA,UAAM;AAAA,UAAK;AAAA;AAAA,UAEpE;AAAA,UAAK;AAAA,UAAK;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAc;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAO;AAAA,UAAO;AAAA,UAAO;AAAA;AAAA,UAE3E;AAAA,UAAM;AAAA,UAAM;AAAA;AAAA,UAEZ;AAAA,UAAS;AAAA,UAAS;AAAA,UAAS;AAAA,UAAM;AAAA,UAAM;AAAA;AAAA,UAEvC;AAAA,UAAO;AAAA,UAAM;AAAA,UAAO;AAAA,UAAK;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA;AAAA,UAER;AAAA,UAAS;AAAA,UAAS;AAAA,UAAM;AAAA;AAAA,UAExB;AAAA,UAAQ;AAAA,UAAU;AAAA;AAAA,UAElB;AAAA,UAAO;AAAA,UAAO;AAAA,UAAS;AAAA;AAAA,UAEvB;AAAA,UAAW;AAAA,UAAW;AAAA,UAAS;AAAA,UAAU;AAAA;AAAA,UAEzC;AAAA,UAAS;AAAA,UAAQ;AAAA,QACnB;AAAA,QACA,mBAAmB,CAAC,KAAK;AAAA,QACzB,iBAAiB;AAAA,QACjB,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,aAAa,CAAC,UAAU,SAAS,UAAU,SAAS,UAAU,OAAO;AAAA,QACrE,aAAa,CAAC,WAAW,UAAU,WAAW,aAAa;AAAA,QAC3D,cAAc,EAAE,MAAM,KAAK;AAAA,MAAA,CAC5B;AAEO,cAAA,IAAI,mBAAmB,SAAS;AACjC,aAAA;AAAA,IACT;AAGM,UAAA,mBAAmB,SAAS,MAAM;;AACtC,UAAI,GAAC,WAAM,WAAN,mBAAc,SAAgB,QAAA;AAC5B,aAAA,aAAa,MAAM,OAAO,OAAO;AAAA,IAAA,CACzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpED,UAAM,SAAS,UAAU;AACzB,UAAM,SAAS,iBAAiB;AAC1B,UAAA,WAAW,OAAO,OAAO;AACb,iBAAa;AAC/B,UAAM,QAAQ,SAAS;AACjB,UAAA,EAAE,EAAE,IAAI,QAAQ;AAGhB,UAAA,kBAAkB,IAAI,KAAK;AAGjC,UAAM,OAAO,IAAI;AAAA,MACf,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,MAEZ,UAAU;AAAA,IAAA,CACX;AAEK,UAAA,UAAU,IAAI,KAAK;AACnB,UAAA,YAAY,IAAI,CAAC;AACvB,UAAM,cAAc,SAAS,MAAM,UAAU,UAAU,CAAC;AAGlD,UAAA,UAAU,IAAsB,EAAE;AAClC,UAAA,gBAAgB,IAAI,KAAK;AACzB,UAAA,eAAe,IAAI,CAAC;AAepB,UAAA,YAAY,IAAI,KAAK;AACrB,UAAA,eAAe,IAAgC,IAAI;AAEnD,UAAA,uBAAuB,IAAI,KAAK;AAwEhC,UAAA,eAAe,IAAI,EAAE;AACrB,UAAA,iBAAiB,IAAI,KAAK;AAGhC,mBAAe,aAAa;AAC1B,qBAAe,QAAQ;AACnB,UAAA;AACI,cAAA,WAA4B,MAAM,QAAQ,WAAW;AAC9C,qBAAA,QAAQ,yBAAyB,SAAS,KAAK;AACvD,aAAA,MAAM,aAAa,SAAS;AAAA,eAC1B,OAAO;AAAA,MAAA,UAQd;AACA,uBAAe,QAAQ;AAAA,MAAA;AAAA,IACzB;AAIF,mBAAe,uBAAuB;AAChC,UAAA,CAAC,KAAK,MAAM,OAAO;AACrB,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,4BAA4B;AAAA,UACrC,aAAa,EAAE,2CAA2C;AAAA,UAC1D,OAAO;AAAA,QAAA,CACR;AACD;AAAA,MAAA;AAGF,UAAI,CAAC,gBAAgB,KAAK,KAAK,MAAM,KAAK,GAAG;AAC3C,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,4BAA4B;AAAA,UACrC,aAAa,EAAE,0CAA0C;AAAA,UACzD,OAAO;AAAA,QAAA,CACR;AACD;AAAA,MAAA;AAGF,cAAQ,QAAQ;AACZ,UAAA;AACF,cAAM,QAAQ,YAAY,KAAK,MAAM,OAAO,CAAC;AAG7C,kBAAU,QAAQ;AACZ,cAAA,QAAQ,YAAY,MAAM;AACpB,oBAAA;AACN,cAAA,UAAU,UAAU,GAAG;AACzB,0BAAc,KAAK;AAAA,UAAA;AAAA,WAEpB,GAAI;AAEP,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,kCAAkC;AAAA,UAC3C,aAAa,EAAE,wCAAwC;AAAA,UACvD,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,eACM,OAAY;AACnB,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,gCAAgC;AAAA,UACzC,aAAa,MAAM,WAAW,EAAE,sCAAsC;AAAA,UACtE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAClB;AAkGF,aAAS,aAAa;AACpB,aAAO,KAAK,SAAS;AAAA,IAAA;AAGvB,aAAS,kBAAkB;AACzB,aAAO,KAAK,gBAAgB;AAAA,IAAA;AAI9B,aAAS,oBAAoB;AACX,sBAAA,QAAQ,CAAC,gBAAgB;AAEzC,WAAK,QAAQ;AAAA,QACX,OAAO,KAAK,MAAM;AAAA;AAAA,QAClB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,YAAY,KAAK,MAAM;AAAA,QACvB,UAAU;AAAA,MACZ;AAEW,iBAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": [], "sources": ["../../../../src/components/NoticePopup.vue", "../../../../src/pages/join.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { PopupNoticeResponse } from '~/types/popup'\r\nimport DOMPurify from 'dompurify'\r\nimport { computed } from 'vue'\r\n\r\nconst props = defineProps<{\r\n  modelValue: boolean\r\n  notice: PopupNoticeResponse | null\r\n}>()\r\n\r\nconst emit = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n}>()\r\n\r\n// 处理 v-model 绑定\r\nconst isVisible = computed({\r\n  get: () => props.modelValue,\r\n  set: (value) => emit('update:modelValue', value)\r\n})\r\n\r\n// 安全的HTML渲染函数\r\nconst sanitizeHTML = (html: string) => {\r\n  console.log('Original HTML:', html)\r\n  \r\n  DOMPurify.addHook('afterSanitizeAttributes', function(node) {\r\n    // 允许所有style属性\r\n    if (node.hasAttribute('style')) {\r\n      const style = node.getAttribute('style')\r\n      if (style) {\r\n        // 确保样式被正确保留，并添加 !important\r\n        const enhancedStyle = style.split(';')\r\n          .map(s => s.trim())\r\n          .filter(s => s)\r\n          .map(s => `${s} !important`)\r\n          .join(';')\r\n        node.setAttribute('style', enhancedStyle)\r\n        // 移除可能的样式覆盖\r\n        node.removeAttribute('class')\r\n      }\r\n    }\r\n  })\r\n  \r\n  const sanitized = DOMPurify.sanitize(html, {\r\n    ADD_TAGS: [\r\n      // 标题和文本\r\n      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'strong', 'em', 'u', 'span',\r\n      // 格式化\r\n      'b', 'i', 'pre', 'code', 'blockquote', 'mark', 'del', 'ins', 'sub', 'sup', 'small',\r\n      // 列表\r\n      'ol', 'ul', 'li',\r\n      // 表格\r\n      'table', 'thead', 'tbody', 'tr', 'th', 'td',\r\n      // 其他\r\n      'div', 'hr', 'img', 'a', 'font'\r\n    ],\r\n    ADD_ATTR: [\r\n      // 通用属性\r\n      'style', 'class', 'id', 'name',\r\n      // 链接属性\r\n      'href', 'target', 'rel',\r\n      // 图片属性\r\n      'src', 'alt', 'width', 'height',\r\n      // 表格属性\r\n      'colspan', 'rowspan', 'align', 'valign', 'border',\r\n      // 字体属性\r\n      'color', 'size', 'face'\r\n    ],\r\n    ADD_DATA_URI_TAGS: ['img'],\r\n    ALLOW_DATA_ATTR: true,\r\n    ALLOW_UNKNOWN_PROTOCOLS: true,\r\n    ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|xxx):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i,\r\n    FORBID_TAGS: ['script', 'style', 'iframe', 'frame', 'object', 'embed'],\r\n    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],\r\n    USE_PROFILES: { html: true }\r\n  })\r\n  \r\n  console.log('Sanitized HTML:', sanitized)\r\n  return sanitized\r\n}\r\n\r\n// 计算属性：安全的HTML内容\r\nconst safePopupContent = computed(() => {\r\n  if (!props.notice?.content) return ''\r\n  return sanitizeHTML(props.notice.content)\r\n})\r\n</script>\r\n\r\n<template>\r\n  <UModal \r\n    v-model=\"isVisible\" \r\n    :ui=\"{ \r\n      width: 'sm:max-w-lg',\r\n      container: 'flex items-center justify-center min-h-screen p-4'\r\n    }\" \r\n    :prevent-close=\"true\"\r\n  >\r\n    <UCard :ui=\"{ divide: 'divide-y divide-gray-100 dark:divide-gray-800' }\">\r\n      <template #header>\r\n        <div class=\"flex items-center\">\r\n          <h3 class=\"text-base font-semibold leading-6 text-gray-900 dark:text-white\">\r\n            {{ $t('messages.popup.notice.title') }}\r\n          </h3>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"p-4\">\r\n        <div v-html=\"safePopupContent\" class=\"notice-popup-content whitespace-pre-wrap\"></div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"flex justify-end\">\r\n          <UButton\r\n            color=\"gray\"\r\n            variant=\"solid\"\r\n            @click=\"isVisible = false\"\r\n          >\r\n            {{ $t('messages.popup.notice.confirm') }}\r\n          </UButton>\r\n        </div>\r\n      </template>\r\n    </UCard>\r\n  </UModal>\r\n</template>\r\n\r\n<style>\r\n.notice-popup-content :deep(h1) {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.notice-popup-content :deep(p) {\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.notice-popup-content :deep(strong) {\r\n  font-weight: bold;\r\n}\r\n\r\n.notice-popup-content :deep(*) {\r\n  color: inherit !important;\r\n}\r\n\r\n/* 表格样式 */\r\n.notice-popup-content :deep(table) {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  margin: 1rem 0;\r\n}\r\n\r\n.notice-popup-content :deep(th),\r\n.notice-popup-content :deep(td) {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n.notice-popup-content :deep(th) {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 代码块样式 */\r\n.notice-popup-content :deep(pre),\r\n.notice-popup-content :deep(code) {\r\n  background-color: #f5f5f5;\r\n  padding: 0.2em 0.4em;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n.notice-popup-content :deep(pre) {\r\n  padding: 1em;\r\n  overflow-x: auto;\r\n}\r\n\r\n/* 引用块样式 */\r\n.notice-popup-content :deep(blockquote) {\r\n  border-left: 4px solid #ddd;\r\n  margin: 1rem 0;\r\n  padding: 0.5rem 1rem;\r\n  color: #666;\r\n}\r\n\r\n/* 分割线样式 */\r\n.notice-popup-content :deep(hr) {\r\n  border: none;\r\n  border-top: 1px solid #ddd;\r\n  margin: 1rem 0;\r\n}\r\n\r\n/* 其他文本样式 */\r\n.notice-popup-content :deep(mark) {\r\n  background-color: #fff3cd;\r\n  padding: 0.2em;\r\n}\r\n\r\n.notice-popup-content :deep(del) {\r\n  color: #999;\r\n}\r\n\r\n.notice-popup-content :deep(ins) {\r\n  text-decoration: underline;\r\n  background-color: #e6ffe6;\r\n}\r\n\r\n.notice-popup-content :deep(sub),\r\n.notice-popup-content :deep(sup) {\r\n  font-size: 75%;\r\n}\r\n</style> ", "<script setup lang=\"ts\">\r\nimport { useAuthStore } from '~/stores/useAuthStore'\r\nimport { authApi } from '~/api/auth'\r\nimport type { SmsLoginParams, CaptchaResponse, LoginParams } from '~/types/api'\r\nimport { bannerApi } from '~/api/banner'\r\nimport type { BannerResponse } from '~/types/banner'\r\nimport { popupApi } from '~/api/popup'\r\nimport type { PopupNoticeResponse } from '~/types/popup'\r\nimport { generateTerminalFingerprint } from '~/utils/fingerprint'\r\n\r\n// Define page-level configuration\r\ndefinePageMeta({\r\n  layout: false,  // Disable layout completely\r\n  showBottomNav: false\r\n})\r\n\r\nconst router = useRouter()\r\nconst config = useRuntimeConfig()\r\nconst userType = config.public.userType\r\nconst authStore = useAuthStore()\r\nconst toast = useToast()\r\nconst { t } = useI18n()\r\n\r\n// Login method toggle\r\nconst isPasswordLogin = ref(false)\r\n\r\n// Student login form state\r\nconst form = ref({\r\n  phone: '',\r\n  verificationCode: '',\r\n  captcha: '',\r\n  captcha<PERSON>ey: '',\r\n  // Account password login fields\r\n  password: ''\r\n})\r\n\r\nconst loading = ref(false)\r\nconst countdown = ref(0)\r\nconst canSendCode = computed(() => countdown.value === 0)\r\n\r\n// Banner state\r\nconst banners = ref<BannerResponse[]>([])\r\nconst bannerLoading = ref(false)\r\nconst currentSlide = ref(0)\r\nconst defaultDuration = 5000 // 默认轮播时长，单位毫秒\r\n\r\n// 自动轮播\r\nlet autoplayTimer: ReturnType<typeof setTimeout> | null = null\r\n\r\n// 获取当前轮播图的时长\r\nconst getCurrentDuration = () => {\r\n  if (!banners.value.length) return defaultDuration\r\n  const currentBanner = banners.value[currentSlide.value]\r\n  // 确保 duration 在合理范围内（2-30秒）\r\n  return currentBanner?.duration ? Math.min(Math.max(currentBanner.duration, 2000), 30000) : defaultDuration\r\n}\r\n\r\n// 公告相关状态\r\nconst showPopup = ref(false)\r\nconst currentPopup = ref<PopupNoticeResponse | null>(null)\r\n// 学生端忘记密码提示\r\nconst showStudentForgotPwd = ref(false)\r\n\r\nonMounted(async () => {\r\n  await Promise.all([\r\n    authStore.checkAuth(),\r\n    getBanners(),\r\n    getPopupNotice()\r\n  ])\r\n\r\n  // 启动自动轮播\r\n  startAutoplay()\r\n\r\n  if (authStore.isAuthenticated) {\r\n    if (authStore.user?.teacher) {\r\n      router.push('/teacher/schedule')\r\n      return\r\n    }\r\n    if (authStore.user?.student) {\r\n      router.push('/student/dashboard')\r\n      return\r\n    }\r\n  }\r\n\r\n  // Get initial captcha\r\n  if (userType === 'student' || userType === 'all') {\r\n    getCaptcha()\r\n  }\r\n})\r\n\r\nonUnmounted(() => {\r\n  // 清理自动轮播定时器\r\n  if (autoplayTimer) {\r\n    clearTimeout(autoplayTimer)\r\n  }\r\n})\r\n\r\n// 启动自动轮播\r\nfunction startAutoplay() {\r\n  stopAutoplay()\r\n\r\n  const duration = getCurrentDuration()\r\n\r\n  autoplayTimer = setTimeout(() => {\r\n    if (banners.value.length > 0) {\r\n      currentSlide.value = (currentSlide.value + 1) % banners.value.length\r\n      startAutoplay() // 递归调用以使用下一张图片的时长\r\n    }\r\n  }, duration)\r\n}\r\n\r\n// 停止自动轮播\r\nfunction stopAutoplay() {\r\n  if (autoplayTimer) {\r\n    clearTimeout(autoplayTimer)\r\n    autoplayTimer = null\r\n  }\r\n}\r\n\r\n// 手动切换轮播图\r\nfunction changeSlide(index: number) {\r\n  currentSlide.value = index\r\n  startAutoplay() // 重新开始自动轮播，使用新图片的时长\r\n}\r\n\r\nfunction handleBannerImageError(e: Event) {\r\n  const imgElement = e.target as HTMLImageElement\r\n  if (imgElement) {\r\n    imgElement.src = userType === 'teacher' ? '/images/cover/teacher.png' : '/images/cover/student.png'\r\n  }\r\n}\r\n\r\n// 图形验证码相关\r\nconst captchaImage = ref('')\r\nconst captchaLoading = ref(false)\r\n\r\n// 获取图形验证码\r\nasync function getCaptcha() {\r\n  captchaLoading.value = true\r\n  try {\r\n    const response: CaptchaResponse = await authApi.getCaptcha()\r\n    captchaImage.value = `data:image/png;base64,${response.image}`\r\n    form.value.captchaKey = response.key\r\n  } catch (error) {\r\n    /* toast.add({\r\n      title: t('messages.toast.captcha.error.title'),\r\n      description: t('messages.toast.captcha.error.description'),\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    }) */\r\n  } finally {\r\n    captchaLoading.value = false\r\n  }\r\n}\r\n\r\n// 发送验证码\r\nasync function sendVerificationCode() {\r\n  if (!form.value.phone) {\r\n    toast.add({\r\n      title: t('messages.toast.error.title'),\r\n      description: t('messages.signup.validation.phone.required'),\r\n      color: 'red'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!/^1[3-9]\\d{9}$/.test(form.value.phone)) {\r\n    toast.add({\r\n      title: t('messages.toast.error.title'),\r\n      description: t('messages.signup.validation.phone.invalid'),\r\n      color: 'red'\r\n    })\r\n    return\r\n  }\r\n\r\n  loading.value = true\r\n  try {\r\n    await authApi.sendSmsCode(form.value.phone, 1)\r\n\r\n    // 开始倒计时\r\n    countdown.value = 60\r\n    const timer = setInterval(() => {\r\n      countdown.value--\r\n      if (countdown.value === 0) {\r\n        clearInterval(timer)\r\n      }\r\n    }, 1000)\r\n\r\n    toast.add({\r\n      title: t('messages.toast.sms.success.title'),\r\n      description: t('messages.toast.sms.success.description'),\r\n      color: 'green',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n  } catch (error: any) {\r\n    toast.add({\r\n      title: t('messages.toast.sms.error.title'),\r\n      description: error.message || t('messages.toast.sms.error.description'),\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 处理学生登录\r\nasync function handleStudentLogin() {\r\n  if (isPasswordLogin.value) {\r\n    if (!form.value.phone || !form.value.password || !form.value.captcha) {\r\n      toast.add({\r\n        title: t('messages.toast.error.title'),\r\n        description: t('messages.signin.validation.required'),\r\n        color: 'red'\r\n      })\r\n      return\r\n    }\r\n\r\n    if (!/^1[3-9]\\d{9}$/.test(form.value.phone)) {\r\n      toast.add({\r\n        title: t('messages.toast.error.title'),\r\n        description: t('messages.signup.validation.phone.invalid'),\r\n        color: 'red'\r\n      })\r\n      return\r\n    }\r\n\r\n    loading.value = true\r\n    try {\r\n      const loginParams: LoginParams = {\r\n        username: form.value.phone,\r\n        password: form.value.password,\r\n        captcha: form.value.captcha,\r\n        captchaKey: form.value.captchaKey\r\n      }\r\n\r\n      await authStore.login(loginParams)\r\n\r\n      toast.add({\r\n        title: t('messages.signin.toast.success.title'),\r\n        description: t('messages.signin.toast.success.description'),\r\n        color: 'green',\r\n        timeout: 3000,\r\n        icon: 'i-heroicons-check-circle'\r\n      })\r\n\r\n      // Check if user has agreed to terms\r\n      if (!authStore.user?.agreeAgreement) {\r\n        router.push('/student/terms')\r\n      } else {\r\n        router.push('/student/dashboard')\r\n      }\r\n    } catch (error) {\r\n      getCaptcha()\r\n    } finally {\r\n      loading.value = false\r\n    }\r\n  } else {\r\n    if (!form.value.phone || !form.value.verificationCode || !form.value.captcha) {\r\n      toast.add({\r\n        title: t('messages.toast.error.title'),\r\n        description: t('messages.signin.validation.required'),\r\n        color: 'red'\r\n      })\r\n      return\r\n    }\r\n\r\n    loading.value = true\r\n    try {\r\n      const loginParams: SmsLoginParams = {\r\n        phone: form.value.phone,\r\n        code: form.value.verificationCode,\r\n        captcha: form.value.captcha,\r\n        captchaKey: form.value.captchaKey,\r\n        terminalFingerprint: generateTerminalFingerprint()\r\n      }\r\n\r\n      await authStore.smsLogin(loginParams)\r\n\r\n      toast.add({\r\n        title: t('messages.signin.toast.success.title'),\r\n        description: t('messages.signin.toast.success.description'),\r\n        color: 'green',\r\n        timeout: 3000,\r\n        icon: 'i-heroicons-check-circle'\r\n      })\r\n\r\n      // Check if user has agreed to terms\r\n      if (!authStore.user?.agreeAgreement) {\r\n        router.push('/student/terms')\r\n      } else {\r\n        router.push('/student/dashboard')\r\n      }\r\n    } catch (error) {\r\n      getCaptcha()\r\n    } finally {\r\n      loading.value = false\r\n    }\r\n  }\r\n}\r\n\r\nfunction goToSignIn() {\r\n  router.push('/signin')\r\n}\r\n\r\nfunction handleJoinClick() {\r\n  router.push('/teacher/terms')\r\n}\r\n\r\n// Toggle login method\r\nfunction toggleLoginMethod() {\r\n  isPasswordLogin.value = !isPasswordLogin.value\r\n  // Clear form when switching\r\n  form.value = {\r\n    phone: form.value.phone, // Keep phone number when switching\r\n    verificationCode: '',\r\n    captcha: '',\r\n    captchaKey: form.value.captchaKey,\r\n    password: ''\r\n  }\r\n  // Refresh captcha when switching\r\n  getCaptcha()\r\n}\r\n\r\n// 学生端：忘记密码提示\r\nfunction onStudentForgotPasswordClick() {\r\n  showStudentForgotPwd.value = true\r\n}\r\n\r\n// 获取轮播图数据\r\nasync function getBanners() {\r\n  bannerLoading.value = true\r\n  try {\r\n    // 根据用户类型获取对应的轮播图\r\n    const targetType = userType === 'teacher' ? 1 : userType === 'student' ? 2 : 3\r\n    const response = await bannerApi.getBannerList({ targetType })\r\n\r\n    if (response) {\r\n      banners.value = Array.isArray(response) ? response : [response].filter(Boolean)\r\n    } else {\r\n      banners.value = []\r\n    }\r\n  } catch (error: any) {\r\n    banners.value = []\r\n    toast.add({\r\n      title: t('messages.toast.error.title'),\r\n      description: error.message || t('messages.toast.error.description'),\r\n      color: 'red'\r\n    })\r\n  } finally {\r\n    bannerLoading.value = false\r\n  }\r\n}\r\n\r\n// 处理轮播图点击\r\nfunction handleBannerClick(banner: BannerResponse) {\r\n  if (banner.linkUrl) {\r\n    window.open(banner.linkUrl, '_blank')\r\n  }\r\n}\r\n\r\n// 获取公告信息\r\nasync function getPopupNotice() {\r\n  try {\r\n    // 根据用户类型获取对应的公告\r\n    const targetType = userType === 'teacher' ? 2 : userType === 'student' ? 1 : 3\r\n    const response = await popupApi.getPopupList({ targetType })\r\n\r\n    if (response) {\r\n      // 如果是数组，取第一条；如果是单个对象，直接使用\r\n      currentPopup.value = Array.isArray(response) ? response[0] : response\r\n      if (currentPopup.value) {\r\n        showPopup.value = true\r\n      }\r\n    }\r\n  } catch (error: any) {\r\n    toast.add({\r\n      title: t('messages.toast.error.title'),\r\n      description: error.message || t('messages.toast.error.description'),\r\n      color: 'red'\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"min-h-screen bg-gray-50 flex flex-col\">\r\n    <!-- 语言切换组件 -->\r\n    <LanguageToggle />\r\n\r\n    <div class=\"max-w-screen-2xl mx-auto flex-1\">\r\n      <!-- 宣传图 -->\r\n      <div class=\"max-w-[1200px] mx-auto\">\r\n        <div v-if=\"bannerLoading\" class=\"w-full aspect-[16/8] flex items-center justify-center\">\r\n          <UIcon name=\"i-heroicons-arrow-path\" class=\"animate-spin text-4xl text-gray-400\" />\r\n        </div>\r\n\r\n        <template v-else>\r\n          <div v-if=\"banners.length > 0\" class=\"relative w-full aspect-[16/8] overflow-hidden\">\r\n            <div\r\n              class=\"relative w-full h-full\"\r\n              @mouseenter=\"banners.length > 1 && stopAutoplay()\"\r\n              @mouseleave=\"banners.length > 1 && startAutoplay()\"\r\n            >\r\n              <div\r\n                v-for=\"(banner, index) in banners\"\r\n                :key=\"banner.id\"\r\n                class=\"absolute inset-0 transition-opacity duration-500\"\r\n                :class=\"index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'\"\r\n              >\r\n                <div class=\"relative w-full h-full cursor-pointer group\" @click=\"handleBannerClick(banner)\">\r\n                  <img\r\n                    :src=\"banner.imageUrl\"\r\n                    :alt=\"banner.title\"\r\n                    class=\"w-full h-full object-cover\"\r\n                    @error=\"handleBannerImageError\"\r\n                  />\r\n                  <div\r\n                    v-if=\"banner.title\"\r\n                    class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent text-white p-4 transform transition-transform duration-300 group-hover:translate-y-0\"\r\n                  >\r\n                    <h3 class=\"text-lg font-medium\">{{ banner.title }}</h3>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Navigation Buttons -->\r\n            <div v-if=\"banners.length > 1\" class=\"absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4 z-20\">\r\n              <button\r\n                class=\"bg-black/20 hover:bg-black/40 rounded-full p-2 transition-colors backdrop-blur-sm\"\r\n                @click=\"() => {\r\n                  changeSlide((currentSlide - 1 + banners.length) % banners.length)\r\n                }\"\r\n              >\r\n                <UIcon name=\"i-heroicons-chevron-left-20-solid\" class=\"w-6 h-6 text-white\"/>\r\n              </button>\r\n              <button\r\n                class=\"bg-black/20 hover:bg-black/40 rounded-full p-2 transition-colors backdrop-blur-sm\"\r\n                @click=\"() => {\r\n                  changeSlide((currentSlide + 1) % banners.length)\r\n                }\"\r\n              >\r\n                <UIcon name=\"i-heroicons-chevron-right-20-solid\" class=\"w-6 h-6 text-white\"/>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Indicators -->\r\n            <div v-if=\"banners.length > 1\" class=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-20\">\r\n              <button\r\n                v-for=\"(_, index) in banners\"\r\n                :key=\"index\"\r\n                class=\"w-2 h-2 rounded-full transition-colors backdrop-blur-sm\"\r\n                :class=\"currentSlide === index ? 'bg-white' : 'bg-white/50'\"\r\n                @click=\"() => changeSlide(index)\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else class=\"w-full aspect-[16/6]\">\r\n            <img\r\n              :src=\"userType === 'teacher' ? '/images/cover/teacher.png' : '/images/cover/student.png'\"\r\n              :alt=\"userType === 'teacher' ? 'Teacher Cover' : 'Student Cover'\"\r\n              class=\"w-full h-full object-cover\"\r\n            />\r\n          </div>\r\n        </template>\r\n      </div>\r\n      <!-- 空白占位div -->\r\n      <div class=\"h-10\"></div>\r\n\r\n      <!-- Student Login Form -->\r\n      <div class=\"px-4\">\r\n        <div class=\"max-w-md mx-auto\">\r\n          <div v-if=\"userType === 'student' || userType === 'all'\" class=\"bg-white p-8 rounded-xl border border-gray-200 mb-6\">\r\n            <form @submit.prevent=\"handleStudentLogin\" class=\"space-y-4\">\r\n              <!-- Login Method Toggle -->\r\n              <div class=\"flex justify-end mb-4\">\r\n                <UButton\r\n                  variant=\"link\"\r\n                  color=\"gray\"\r\n                  @click=\"toggleLoginMethod\"\r\n                >\r\n                  {{ isPasswordLogin ? t('messages.signin.student.form.switchToSms') : t('messages.signin.student.form.switchToPassword') }}\r\n                </UButton>\r\n              </div>\r\n\r\n              <!-- SMS Login Form -->\r\n              <template v-if=\"!isPasswordLogin\">\r\n                <!-- Phone Input -->\r\n                <div>\r\n                  <UInput\r\n                    v-model=\"form.phone\"\r\n                    type=\"tel\"\r\n                    :placeholder=\"t('messages.signin.student.form.phonePlaceholder')\"\r\n                    class=\"w-full\"\r\n                    size=\"lg\"\r\n                  />\r\n                </div>\r\n\r\n                <!-- Verification Code -->\r\n                <div class=\"flex gap-2\">\r\n                  <UInput\r\n                    v-model=\"form.verificationCode\"\r\n                    :placeholder=\"t('messages.signin.student.form.verificationCodePlaceholder')\"\r\n                    class=\"flex-1\"\r\n                    size=\"lg\"\r\n                  />\r\n                  <UButton\r\n                    :disabled=\"!canSendCode || loading\"\r\n                    @click.prevent=\"sendVerificationCode\"\r\n                    class=\"whitespace-nowrap\"\r\n                    color=\"primary\"\r\n                    size=\"lg\"\r\n                  >\r\n                    {{ canSendCode ? t('messages.signin.student.form.sendCode') : t('messages.signin.student.form.resendCode', { countdown }) }}\r\n                  </UButton>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- Password Login Form -->\r\n              <template v-else>\r\n                <!-- Phone Input -->\r\n                <div>\r\n                  <UInput\r\n                    v-model=\"form.phone\"\r\n                    type=\"tel\"\r\n                    :placeholder=\"t('messages.signin.student.form.phonePlaceholder')\"\r\n                    class=\"w-full\"\r\n                    size=\"lg\"\r\n                  />\r\n                </div>\r\n\r\n                <!-- Password Input -->\r\n                <div>\r\n                  <UInput\r\n                    v-model=\"form.password\"\r\n                    type=\"password\"\r\n                    :placeholder=\"t('messages.signin.student.form.passwordPlaceholder')\"\r\n                    class=\"w-full\"\r\n                    size=\"lg\"\r\n                  />\r\n                </div>\r\n              </template>\r\n\r\n              <!-- Captcha -->\r\n              <div class=\"flex gap-2 items-stretch\">\r\n                <UInput\r\n                  v-model=\"form.captcha\"\r\n                  :placeholder=\"t('messages.signin.student.form.imageCaptchaPlaceholder')\"\r\n                  class=\"flex-1\"\r\n                  size=\"lg\"\r\n                />\r\n                <div\r\n                  class=\"w-32 h-11 flex items-center justify-center border rounded cursor-pointer overflow-hidden\"\r\n                  @click=\"getCaptcha\"\r\n                >\r\n                  <UIcon\r\n                    v-if=\"captchaLoading\"\r\n                    name=\"i-heroicons-arrow-path\"\r\n                    class=\"animate-spin text-2xl\"\r\n                  />\r\n                  <img\r\n                    v-else\r\n                    :src=\"captchaImage\"\r\n                    :alt=\"t('messages.signin.student.form.imageCaptcha')\"\r\n                    class=\"w-full h-full object-cover\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Login Button -->\r\n              <UButton\r\n                type=\"submit\"\r\n                block\r\n                :loading=\"loading\"\r\n                class=\"mt-6\"\r\n                color=\"primary\"\r\n              >\r\n                {{ t('messages.signin.buttons.login') }}\r\n              </UButton>\r\n            </form>\r\n\r\n            <div class=\"text-center text-sm text-gray-500 mt-4\">\r\n              {{ t('messages.signin.student.loginHint') }}\r\n            </div>\r\n\r\n            <div class=\"flex justify-between text-sm text-primary mt-4\">\r\n              <a href=\"#\" class=\"hover:underline\" @click.prevent=\"onStudentForgotPasswordClick\">{{ t('messages.forgotPassword.title') }}</a>\r\n              <a href=\"#\" class=\"hover:underline\">{{ t('messages.join.buttons.terms') }}</a>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Teacher Section -->\r\n          <div v-if=\"userType === 'all' || userType === 'teacher'\" class=\"max-w-md mx-auto space-y-4 w-full\">\r\n            <UButton\r\n              block\r\n              size=\"lg\"\r\n              color=\"gray\"\r\n              variant=\"solid\"\r\n              class=\"whitespace-nowrap flex items-center justify-center\"\r\n              @click=\"handleJoinClick\"\r\n            >\r\n              <UIcon name=\"i-heroicons-user-plus\" class=\"shrink-0 mr-2\"/>\r\n              {{ $t('messages.join.buttons.joinUs') }}\r\n            </UButton>\r\n\r\n            <div class=\"text-center\">\r\n              <p class=\"text-gray-600 mb-2\">{{ $t('messages.join.buttons.hasAccount') }}</p>\r\n              <UButton\r\n                block\r\n                size=\"lg\"\r\n                color=\"white\"\r\n                variant=\"outline\"\r\n                class=\"whitespace-nowrap flex items-center justify-center\"\r\n                @click=\"goToSignIn\"\r\n              >\r\n                <UIcon name=\"i-heroicons-arrow-right-on-rectangle\" class=\"shrink-0 mr-2\"/>\r\n                {{ $t('messages.join.buttons.signIn') }}\r\n              </UButton>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 公告弹窗 -->\r\n      <NoticePopup\r\n        v-model=\"showPopup\"\r\n        :notice=\"currentPopup\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 页脚 -->\r\n    <ClientOnly>\r\n      <AppFooter />\r\n    </ClientOnly>\r\n  </div>\r\n\r\n  <!-- 学生端忘记密码提示弹窗 -->\r\n  <UModal v-model=\"showStudentForgotPwd\">\r\n    <UCard>\r\n      <template #header>\r\n        <div class=\"flex items-center\">\r\n          <h3 class=\"text-base font-semibold leading-6 text-gray-900 dark:text-white\">\r\n            {{ t('messages.signin.studentForgotPassword.title') }}\r\n          </h3>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"p-2 text-gray-700 dark:text-gray-200\">\r\n        {{ t('messages.signin.studentForgotPassword.message') }}\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"flex justify-end\">\r\n          <UButton color=\"primary\" @click=\"showStudentForgotPwd = false\">{{ t('messages.common.confirm') }}</UButton>\r\n        </div>\r\n      </template>\r\n    </UCard>\r\n  </UModal>\r\n</template>\r\n\r\n<style scoped>\r\n.min-h-screen {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n</style>\r\n"], "version": 3}