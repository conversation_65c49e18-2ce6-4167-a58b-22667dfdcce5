{"name": "@fortawesome/vue-fontawesome", "description": "Official Vue component for Font Awesome 6", "version": "3.0.8", "main": "index.js", "files": ["README.md", "LICENSE.txt", "UPGRADING.md", "CHANGELOG.md", "index.d.ts", "index.es.js", "index.js", "src/components/**.js", "src/**.js"], "jest": {"verbose": true, "testEnvironmentOptions": {"customExportConditions": ["node", "node-addons"]}}, "module": "index.es.js", "jsnext:main": "index.es.js", "types": "index.d.ts", "homepage": "https://github.com/FortAwesome/vue-fontawesome", "repository": {"type": "git", "url": "https://github.com/FortAwesome/vue-fontawesome.git"}, "contributors": ["<PERSON> <github.com/parkeyparker>", "<PERSON> <github.com/brandon-mork>", "btaens <github.com/btaens>", "<PERSON> <github.com/david-driscoll>", "<PERSON> <github.com/meteorlxy>", "<PERSON> <https://github.com/micha<PERSON><PERSON><PERSON><PERSON>>", "<PERSON><PERSON> <github.com/otijhuis>", "<PERSON><PERSON> <https://github.com/rigma>", "SirLamer <github.com/SirLamer>", "Tyranteon <github.com/tyranteon>", "<PERSON><PERSON><PERSON> <github.com/viniciuslrangel>", "<PERSON><PERSON>z <github.com/schulz3000>", "<PERSON><PERSON> <github.com/ihmels>", "<PERSON> <https://github.com/talbs>", "<PERSON> <https://github.com/jason<PERSON>ien>", "<PERSON> <https://github.com/mlwilkerson>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "scripts": {"build": "rollup -c rollup.config.js", "dist": "cross-env NODE_ENV=production npm run build", "test": "jest --silent", "prepack": "npm run dist"}, "lint-staged": {"README.md": ["markdown-toc -i", "git add"]}, "peerDependencies": {"@fortawesome/fontawesome-svg-core": "~1 || ~6", "vue": ">= 3.0.0 < 4"}, "devDependencies": {"@babel/core": "^7.18.2", "@babel/plugin-external-helpers": "^7.17.12", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-proposal-json-strings": "^7.17.12", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.18.2", "@fortawesome/fontawesome-svg-core": "~6", "@fortawesome/free-solid-svg-icons": "^6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "@vue/test-utils": "^2.0.0-beta.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^28.1.1", "concurrently": "^7.2.1", "cross-env": "^7.0.3", "humps": "^2.0.1", "husky": "^8.0.1", "install": "^0.13.0", "jest": "^28.1.1", "jest-environment-jsdom": "^28.1.1", "lint-staged": "^13.0.0", "markdown-toc": "^1.2.0", "npm": "^10.2.2", "prettier": "^3.0.3", "rollup": "^2.75.6", "vue": "^3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}