import { O as defineNuxtRouteMiddleware, K as useAuthStore, g as navigateTo } from './server.mjs';
import 'vue';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:util';
import 'node:url';
import 'node:net';
import 'node:fs';
import 'node:path';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const auth = defineNuxtRouteMiddleware((to) => {
  const authStore = useAuthStore();
  const publicRoutes = ["/signin", "/signup", "/forgot-password", "/reset-password"];
  if (!authStore.isAuthenticated && !publicRoutes.includes(to.path)) {
    return navigateTo("/signin");
  }
  if (authStore.isAuthenticated && publicRoutes.includes(to.path)) {
    return navigateTo("/");
  }
});

export { auth as default };
//# sourceMappingURL=auth-CxrGEx7B.mjs.map
