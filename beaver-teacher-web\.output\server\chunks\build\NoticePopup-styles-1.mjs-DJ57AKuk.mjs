const NoticePopup_vue_vue_type_style_index_0_lang = ".notice-popup-content :deep(h1){margin-bottom:1rem}.notice-popup-content :deep(p){margin-bottom:.5rem}.notice-popup-content :deep(strong){font-weight:700}.notice-popup-content :deep(*){color:inherit!important}.notice-popup-content :deep(table){border-collapse:collapse;margin:1rem 0;width:100%}.notice-popup-content :deep(td),.notice-popup-content :deep(th){border:1px solid #ddd;padding:8px;text-align:left}.notice-popup-content :deep(th){background-color:#f5f5f5}.notice-popup-content :deep(code),.notice-popup-content :deep(pre){background-color:#f5f5f5;border-radius:3px;font-family:monospace;padding:.2em .4em}.notice-popup-content :deep(pre){overflow-x:auto;padding:1em}.notice-popup-content :deep(blockquote){border-left:4px solid #ddd;color:#666;margin:1rem 0;padding:.5rem 1rem}.notice-popup-content :deep(hr){border:none;border-top:1px solid #ddd;margin:1rem 0}.notice-popup-content :deep(mark){background-color:#fff3cd;padding:.2em}.notice-popup-content :deep(del){color:#999}.notice-popup-content :deep(ins){background-color:#e6ffe6;text-decoration:underline}.notice-popup-content :deep(sub),.notice-popup-content :deep(sup){font-size:75%}";

export { NoticePopup_vue_vue_type_style_index_0_lang as N };
//# sourceMappingURL=NoticePopup-styles-1.mjs-DJ57AKuk.mjs.map
