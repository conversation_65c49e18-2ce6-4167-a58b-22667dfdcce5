import { _ as __nuxt_component_1 } from './Avatar-Cjyb3Ij_.mjs';
import { _ as __nuxt_component_1$1 } from './Badge-BbAwiPBc.mjs';
import { useSSRContext, defineComponent, computed, mergeProps, withCtx, createTextVNode, toDisplayString, unref, ref } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderClass, ssrRenderAttr, ssrRenderStyle } from 'vue/server-renderer';
import { h as getTimeDisplayText, i as formatTime } from './datetime-BvKd-1hF.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { K as useAuthStore, c as useToast, N as request } from './server.mjs';
import { _ as __nuxt_component_10 } from './Textarea-Cu5vOIr8.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { s as setInterval } from './interval-gl53xdpR.mjs';

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var MessageContentType = /* @__PURE__ */ ((MessageContentType2) => {
  MessageContentType2[MessageContentType2["TEXT"] = 0] = "TEXT";
  MessageContentType2[MessageContentType2["IMAGE"] = 1] = "IMAGE";
  MessageContentType2[MessageContentType2["FILE"] = 2] = "FILE";
  MessageContentType2[MessageContentType2["SYSTEM"] = 3] = "SYSTEM";
  return MessageContentType2;
})(MessageContentType || {});
var SessionStatus = /* @__PURE__ */ ((SessionStatus2) => {
  SessionStatus2[SessionStatus2["CLOSED"] = 0] = "CLOSED";
  SessionStatus2[SessionStatus2["ACTIVE"] = 1] = "ACTIVE";
  return SessionStatus2;
})(SessionStatus || {});
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "ChatSessionItem",
  __ssrInlineRender: true,
  props: {
    session: {},
    active: { type: Boolean }
  },
  emits: ["select"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const formattedLastTime = computed(() => {
      if (!props.session.lastMessageTime)
        return "";
      return getTimeDisplayText(props.session.lastMessageTime);
    });
    const isActive = computed(() => props.session.status === SessionStatus.ACTIVE);
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UAvatar = __nuxt_component_1;
      const _component_UBadge = __nuxt_component_1$1;
      _push(`<div${ssrRenderAttrs(mergeProps({
        class: [
          "p-3 flex cursor-pointer relative rounded-md",
          _ctx.active ? "bg-primary-50" : "hover:bg-gray-50",
          !isActive.value ? "opacity-70" : ""
        ]
      }, _attrs))}><div class="flex-shrink-0 mr-2 relative">`);
      _push(ssrRenderComponent(_component_UAvatar, {
        src: _ctx.session.serviceAvatar || "/default-avatar.png",
        alt: _ctx.session.serviceName,
        size: "lg"
      }, null, _parent));
      if (_ctx.session.unreadCount > 0) {
        _push(ssrRenderComponent(_component_UBadge, {
          color: "red",
          class: ["absolute -top-1 -right-1"]
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.session.unreadCount > 99 ? "99+" : _ctx.session.unreadCount)}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.session.unreadCount > 99 ? "99+" : _ctx.session.unreadCount), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><h3 class="font-medium text-gray-900 truncate">${ssrInterpolate(_ctx.session.serviceName)}</h3><span class="text-xs text-gray-500">${ssrInterpolate(formattedLastTime.value)}</span></div><div class="text-sm text-gray-500 truncate mt-1">${ssrInterpolate(_ctx.session.lastMessage || "\u6682\u65E0\u6D88\u606F")}</div></div>`);
      if (!isActive.value) {
        _push(`<div class="absolute top-0 right-0 p-1 text-xs">`);
        _push(ssrRenderComponent(_component_UBadge, {
          color: "gray",
          size: "xs"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`\u5DF2\u5173\u95ED`);
            } else {
              return [
                createTextVNode("\u5DF2\u5173\u95ED")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/chat/ChatSessionItem.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "ChatMessage",
  __ssrInlineRender: true,
  props: {
    message: {}
  },
  setup(__props) {
    const props = __props;
    const authStore = useAuthStore();
    const isCurrentUserMessage = computed(() => {
      var _a, _b;
      const currentUserId = ((_b = (_a = authStore.user) == null ? void 0 : _a.id) == null ? void 0 : _b.toString()) || "";
      const messageSenderId = props.message.senderId.toString();
      const currentUserIdPrefix = currentUserId.substring(0, 15);
      const messageSenderIdPrefix = messageSenderId.substring(0, 15);
      const result = messageSenderIdPrefix === currentUserIdPrefix;
      console.debug("\u6D88\u606F\u5F52\u5C5E\u5224\u65AD (\u7CBE\u5EA6\u5904\u7406):", {
        messageId: props.message.id,
        senderType: props.message.senderType,
        messageSenderId,
        currentUserId,
        messageSenderIdPrefix,
        currentUserIdPrefix,
        isCurrentUser: result,
        content: props.message.content.substring(0, 15) + (props.message.content.length > 15 ? "..." : "")
      });
      return result;
    });
    const contentType = computed(() => {
      return props.message.contentType;
    });
    const formattedTime = computed(() => {
      return formatTime(props.message.createdTime);
    });
    const displayName = computed(() => {
      if (isCurrentUserMessage.value) {
        return "\u6211";
      } else {
        return props.message.senderName || "\u5BA2\u670D";
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UAvatar = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "message-wrapper" }, _attrs))}>`);
      if (!isCurrentUserMessage.value) {
        _push(`<div class="text-xs text-gray-500 mb-1 ml-10">${ssrInterpolate(displayName.value)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="${ssrRenderClass([
        "message-container mb-1",
        { "flex justify-end": isCurrentUserMessage.value }
      ])}">`);
      if (!isCurrentUserMessage.value) {
        _push(`<div class="flex-shrink-0 mr-2">`);
        _push(ssrRenderComponent(_component_UAvatar, {
          src: _ctx.message.senderAvatar || "/default-avatar.png",
          alt: _ctx.message.senderName,
          size: "sm"
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="${ssrRenderClass([
        "max-w-[70%] mb-0",
        isCurrentUserMessage.value ? "bg-primary-50 text-primary-700" : "bg-gray-100 text-gray-800",
        "rounded-lg px-3 py-2 break-words"
      ])}">`);
      if (contentType.value === unref(MessageContentType).TEXT) {
        _push(`<div>${ssrInterpolate(_ctx.message.content)}</div>`);
      } else if (contentType.value === unref(MessageContentType).IMAGE) {
        _push(`<div class="message-image"><img${ssrRenderAttr("src", _ctx.message.content)} class="max-w-full rounded-md" style="${ssrRenderStyle({ "max-height": "200px" })}"></div>`);
      } else if (contentType.value === unref(MessageContentType).FILE) {
        _push(`<div class="message-file"><a${ssrRenderAttr("href", _ctx.message.content)} target="_blank" class="flex items-center">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-document",
          class: "mr-1"
        }, null, _parent));
        _push(`<span>\u6587\u4EF6\u4E0B\u8F7D</span></a></div>`);
      } else if (contentType.value === unref(MessageContentType).SYSTEM) {
        _push(`<div class="text-gray-500 italic">${ssrInterpolate(_ctx.message.content)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
      if (isCurrentUserMessage.value) {
        _push(`<div class="flex-shrink-0 ml-2">`);
        _push(ssrRenderComponent(_component_UAvatar, {
          src: _ctx.message.senderAvatar || "/default-avatar.png",
          alt: displayName.value,
          size: "sm"
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="${ssrRenderClass([
        "text-xs text-gray-400 mb-4",
        { "text-right": isCurrentUserMessage.value, "text-left ml-10": !isCurrentUserMessage.value }
      ])}">${ssrInterpolate(formattedTime.value)} `);
      if (isCurrentUserMessage.value) {
        _push(`<span class="ml-1">`);
        if (_ctx.message.isRead === 1) {
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-check-circle",
            class: "text-primary-400"
          }, null, _parent));
        } else {
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-check",
            class: "text-gray-400"
          }, null, _parent));
        }
        _push(`</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/chat/ChatMessage.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ChatInputBox",
  __ssrInlineRender: true,
  props: {
    sessionId: {},
    disabled: { type: Boolean }
  },
  emits: ["send", "typing"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    useToast();
    const message = ref("");
    const isUploading = ref(false);
    const sendMessage = () => {
      if (!message.value.trim() || props.disabled)
        return;
      emit("send", message.value.trim(), MessageContentType.TEXT);
      message.value = "";
    };
    const onKeyDown = (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      } else {
        emit("typing");
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UTextarea = __nuxt_component_10;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "border-t p-3" }, _attrs))}>`);
      {
        _push(`<!---->`);
      }
      _push(`<div class="flex items-end">`);
      _push(ssrRenderComponent(_component_UTextarea, {
        modelValue: message.value,
        "onUpdate:modelValue": ($event) => message.value = $event,
        class: "flex-1 mr-2",
        placeholder: _ctx.disabled ? "\u4F1A\u8BDD\u5DF2\u5173\u95ED" : "\u8BF7\u8F93\u5165\u6D88\u606F...",
        rows: 3,
        "auto-height": true,
        "min-height": 70,
        "max-height": 150,
        disabled: _ctx.disabled,
        onKeydown: onKeyDown
      }, null, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        color: "primary",
        disabled: !message.value.trim() || _ctx.disabled,
        loading: isUploading.value,
        onClick: sendMessage
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u53D1\u9001 `);
          } else {
            return [
              createTextVNode(" \u53D1\u9001 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/chat/ChatInputBox.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const customerApi = {
  // 会话管理API
  // 创建会话
  async createSession(data) {
    return await request({
      method: "POST",
      url: "/api/customer/sessions",
      data
    });
  },
  // 获取会话详情
  async getSessionDetail(sessionId) {
    return await request({
      method: "GET",
      url: `/api/customer/sessions/${sessionId}`
    });
  },
  // 获取我的会话列表
  async getMySessionList() {
    return await request({
      method: "GET",
      url: "/api/customer/sessions/my"
    });
  },
  // 消息管理API
  // 发送消息
  async sendMessage(data) {
    return await request({
      method: "POST",
      url: "/api/customer/messages",
      data
    });
  },
  // 获取会话消息列表
  async getSessionMessages(sessionId) {
    return await request({
      method: "GET",
      url: `/api/customer/sessions/${sessionId}/messages`
    });
  },
  // 分页获取会话消息
  async getSessionMessagesPage(sessionId, page, pageSize) {
    return await request({
      method: "GET",
      url: `/api/customer/sessions/${sessionId}/messages/page`,
      params: { page, pageSize }
    });
  },
  // 标记消息已读
  async markMessageRead(messageId) {
    return await request({
      method: "PUT",
      url: `/api/customer/messages/${messageId}/read`
    });
  },
  // 标记会话所有消息已读
  async markSessionAllRead(sessionId) {
    return await request({
      method: "PUT",
      url: `/api/customer/sessions/${sessionId}/read`
    });
  },
  // 获取未读消息数
  async getUnreadCount() {
    return await request({
      method: "GET",
      url: "/api/customer/messages/unread/count"
    });
  }
};
var WebSocketStatus = /* @__PURE__ */ ((WebSocketStatus2) => {
  WebSocketStatus2[WebSocketStatus2["CONNECTING"] = 0] = "CONNECTING";
  WebSocketStatus2[WebSocketStatus2["OPEN"] = 1] = "OPEN";
  WebSocketStatus2[WebSocketStatus2["CLOSING"] = 2] = "CLOSING";
  WebSocketStatus2[WebSocketStatus2["CLOSED"] = 3] = "CLOSED";
  WebSocketStatus2[WebSocketStatus2["RECONNECTING"] = 4] = "RECONNECTING";
  WebSocketStatus2[WebSocketStatus2["ERROR"] = 5] = "ERROR";
  return WebSocketStatus2;
})(WebSocketStatus || {});
class ChatWebSocketService {
  constructor() {
    __publicField(this, "socket", null);
    __publicField(this, "reconnectInterval", 3e3);
    __publicField(this, "reconnectAttempts", 0);
    __publicField(this, "maxReconnectAttempts", 5);
    __publicField(this, "pingInterval", null);
    __publicField(this, "pingTimer", null);
    __publicField(this, "url", "");
    __publicField(this, "token", "");
    __publicField(this, "userId", "");
    __publicField(this, "status", ref(
      3
      /* CLOSED */
    ));
    __publicField(this, "messageCallback", null);
    __publicField(this, "statusChangeCallback", null);
  }
  // 初始化WebSocket连接
  connect(token, userId) {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log("WebSocket already connected or connecting");
      return;
    }
    this.token = token;
    this.userId = userId;
    this.url = `/beaver/websocket/chat?token=${token}&userId=${userId}&userType=user`;
    this.status.value = 0;
    this.notifyStatusChange();
    try {
      this.socket = new WebSocket(this.buildUrl());
      this.setupSocketListeners();
    } catch (error) {
      console.error("WebSocket connection error:", error);
      this.status.value = 5;
      this.notifyStatusChange();
      this.scheduleReconnect();
    }
  }
  // 断开WebSocket连接
  disconnect() {
    this.clearPingTimer();
    if (this.socket) {
      try {
        this.socket.close();
      } catch (error) {
        console.error("Error closing WebSocket:", error);
      }
      this.socket = null;
      this.status.value = 3;
      this.notifyStatusChange();
    }
  }
  // 设置消息接收回调
  onMessage(callback) {
    this.messageCallback = callback;
  }
  // 设置状态变更回调
  onStatusChange(callback) {
    this.statusChangeCallback = callback;
  }
  // 构建WebSocket URL
  buildUrl() {
    {
      console.log("WebSocket server not configured");
      const protocol = (void 0).location.protocol === "https:" ? "wss:" : "ws:";
      const host = (void 0).location.host;
      return `${protocol}//${host}${this.url}`;
    }
  }
  // 设置Socket事件监听器
  setupSocketListeners() {
    if (!this.socket)
      return;
    this.socket.onopen = () => {
      console.log("WebSocket connected");
      this.status.value = 1;
      this.notifyStatusChange();
      this.reconnectAttempts = 0;
      this.startPingTimer();
    };
    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (this.messageCallback) {
          this.messageCallback(message);
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };
    this.socket.onclose = (event) => {
      console.log("WebSocket closed:", event);
      this.clearPingTimer();
      this.status.value = 3;
      this.notifyStatusChange();
      this.scheduleReconnect();
    };
    this.socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      this.status.value = 5;
      this.notifyStatusChange();
      if (this.socket) {
        this.socket.close();
      }
    };
  }
  // 计划重新连接
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("Max reconnect attempts reached");
      return;
    }
    this.reconnectAttempts++;
    this.status.value = 4;
    this.notifyStatusChange();
    setTimeout(() => {
      console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
      this.connect(this.token, this.userId);
    }, this.reconnectInterval);
  }
  // 开始心跳定时器
  startPingTimer() {
    this.clearPingTimer();
    this.pingInterval = 3e4;
    this.pingTimer = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify({ type: "ping" }));
      }
    }, this.pingInterval);
  }
  // 清除心跳定时器
  clearPingTimer() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }
  // 通知状态变化
  notifyStatusChange() {
    if (this.statusChangeCallback) {
      this.statusChangeCallback(this.status.value);
    }
  }
}
const chatWebSocketService = new ChatWebSocketService();

export { MessageContentType as M, SessionStatus as S, WebSocketStatus as W, _sfc_main$2 as _, customerApi as a, _sfc_main$1 as b, chatWebSocketService as c, _sfc_main as d };
//# sourceMappingURL=websocket-0ZWWGGvI.mjs.map
