{"version": 3, "file": "index-CAFChgdn.mjs", "sources": ["../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/utils/meta.js", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/utils/index.js", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/image.js", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/providers/ipx.js", "../../../../virtual:nuxt:D:/tongdao/beaver/beaver-teacher-web/.nuxt/image-options.mjs", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/composables.js", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/components/_base.js", "../../../../node_modules/.pnpm/@nuxt+image@1.8.1_ioredis@5.4.1_magicast@0.3.5_rollup@4.24.2/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue", "../../../../src/components/generalElements/HeroBanner.vue"], "sourcesContent": null, "names": ["getImage", "attrs", "placeholder"], "mappings": "", "x_google_ignoreList": [0, 1, 2, 3, 5, 6, 7]}