import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { useSSRContext, defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, toDisplayString } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { f as useRouter, c as useToast, B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "terms",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    const termsContent = ref("");
    useToast();
    const { t } = useI18n();
    function handleBack() {
      router.back();
    }
    function handleAgree() {
      router.push("/signup");
    }
    function handleDecline() {
      router.push("/join");
    }
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      const _component_UButton = __nuxt_component_2;
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "terms-container" }, _attrs))} data-v-dd74f684><div class="header fixed top-0 left-0 right-0 bg-white z-10 px-4 py-3 border-b" data-v-dd74f684><div class="max-w-3xl mx-auto" data-v-dd74f684><div class="flex items-center" data-v-dd74f684>`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        color: "gray",
        variant: "ghost",
        onClick: handleBack
      }, null, _parent));
      _push(`<h1 class="text-xl font-bold ml-2" data-v-dd74f684>${ssrInterpolate(unref(t)("messages.terms.teacher.title"))}</h1></div></div></div><div class="content-area max-w-3xl mx-auto px-4" data-v-dd74f684><div class="bg-white rounded-lg shadow-md p-6 mt-16 mb-32" data-v-dd74f684>`);
      if (unref(termsContent)) {
        _push(`<div class="prose max-w-none" data-v-dd74f684>${(_a = unref(termsContent)) != null ? _a : ""}</div>`);
      } else {
        _push(`<div class="text-center py-4" data-v-dd74f684>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin"
        }, null, _parent));
        _push(`<p class="mt-2" data-v-dd74f684>${ssrInterpolate(unref(t)("messages.terms.teacher.loading"))}</p></div>`);
      }
      _push(`</div></div><div class="footer fixed bottom-0 left-0 right-0 bg-white border-t p-4 z-10" data-v-dd74f684><div class="max-w-3xl mx-auto flex justify-end gap-4" data-v-dd74f684>`);
      _push(ssrRenderComponent(_component_UButton, {
        color: "gray",
        variant: "outline",
        size: "lg",
        onClick: handleDecline
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(t)("messages.terms.teacher.buttons.decline"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(t)("messages.terms.teacher.buttons.decline")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        color: "primary",
        variant: "solid",
        size: "lg",
        onClick: handleAgree
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(t)("messages.terms.teacher.buttons.agree"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(t)("messages.terms.teacher.buttons.agree")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/terms.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const terms = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-dd74f684"]]);

export { terms as default };
//# sourceMappingURL=terms-D0KZRy4F.mjs.map
