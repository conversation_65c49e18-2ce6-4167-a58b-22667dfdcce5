import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { e as useRoute } from './server.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const courseId = route.params.id;
    const course = ref({
      id: courseId,
      title: "\u5C11\u513F\u82F1\u8BED\u542F\u8499\u8BFE\u7A0B",
      level: "\u5165\u95E8\u7EA7",
      ageRange: "3-6\u5C81",
      price: "\xA5299/\u6708",
      duration: "45\u5206\u949F/\u8BFE",
      schedule: "\u6BCF\u54682\u6B21",
      maxStudents: "6\u4EBA",
      description: "\u901A\u8FC7\u6E38\u620F\u3001\u6B4C\u66F2\u548C\u4E92\u52A8\u6D3B\u52A8\u57F9\u517B\u5B69\u5B50\u5BF9\u82F1\u8BED\u7684\u5174\u8DA3",
      features: [
        "\u751F\u52A8\u6709\u8DA3\u7684\u4E92\u52A8\u6559\u5B66",
        "\u79D1\u5B66\u7684\u8BFE\u7A0B\u8FDB\u5EA6\u5B89\u6392",
        "\u4E13\u4E1A\u7684\u6559\u5B66\u53CD\u9988\u7CFB\u7EDF",
        "\u4E2A\u6027\u5316\u7684\u5B66\u4E60\u8BA1\u5212"
      ],
      syllabus: [
        {
          week: "\u7B2C\u4E00\u5468",
          content: "\u5B57\u6BCD\u8BA4\u77E5\u4E0E\u53D1\u97F3\u57FA\u7840",
          activities: ["\u5B57\u6BCD\u6B4C", "\u53D1\u97F3\u6E38\u620F", "\u4E92\u52A8\u95EA\u5361"]
        },
        {
          week: "\u7B2C\u4E8C\u5468",
          content: "\u7B80\u5355\u8BCD\u6C47\u5B66\u4E60",
          activities: ["\u5355\u8BCD\u8BB0\u5FC6", "\u56FE\u7247\u914D\u5BF9", "\u60C5\u5883\u5BF9\u8BDD"]
        }
        // 可以添加更多周的内容
      ],
      image: "/images/courses/english.png"
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UBadge = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "container mx-auto px-4 py-8" }, _attrs))}><div class="grid grid-cols-1 lg:grid-cols-2 gap-8"><div class="relative"><img${ssrRenderAttr("src", unref(course).image)}${ssrRenderAttr("alt", unref(course).title)} class="w-full h-[400px] object-cover rounded-lg shadow-lg"></div><div class="space-y-6"><h1 class="text-3xl font-bold">${ssrInterpolate(unref(course).title)}</h1><div class="flex gap-4">`);
      _push(ssrRenderComponent(_component_UBadge, null, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(course).level)}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(course).level), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UBadge, null, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(course).ageRange)}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(course).ageRange), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UBadge, null, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(course).maxStudents)}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(course).maxStudents), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="space-y-4"><div class="flex items-center gap-2">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-clock",
        class: "text-gray-600"
      }, null, _parent));
      _push(`<span>${ssrInterpolate(unref(course).duration)}</span></div><div class="flex items-center gap-2">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-calendar",
        class: "text-gray-600"
      }, null, _parent));
      _push(`<span>${ssrInterpolate(unref(course).schedule)}</span></div><div class="flex items-center gap-2">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-currency-yen",
        class: "text-gray-600"
      }, null, _parent));
      _push(`<span>${ssrInterpolate(unref(course).price)}</span></div></div><p class="text-gray-600">${ssrInterpolate(unref(course).description)}</p><div><h2 class="text-xl font-bold mb-4">\u8BFE\u7A0B\u7279\u8272</h2><ul class="space-y-2"><!--[-->`);
      ssrRenderList(unref(course).features, (feature) => {
        _push(`<li class="flex items-center gap-2">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-check-circle",
          class: "text-green-600"
        }, null, _parent));
        _push(`<span>${ssrInterpolate(feature)}</span></li>`);
      });
      _push(`<!--]--></ul></div>`);
      _push(ssrRenderComponent(_component_UButton, {
        color: "gray",
        variant: "solid",
        class: "w-full",
        size: "lg"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u7ACB\u5373\u62A5\u540D `);
          } else {
            return [
              createTextVNode(" \u7ACB\u5373\u62A5\u540D ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="mt-12"><h2 class="text-2xl font-bold mb-6">\u8BFE\u7A0B\u5927\u7EB2</h2><div class="space-y-6"><!--[-->`);
      ssrRenderList(unref(course).syllabus, (week, index) => {
        _push(`<div class="bg-gray-50 p-6 rounded-lg"><h3 class="text-xl font-bold mb-4">${ssrInterpolate(week.week)}</h3><p class="text-gray-600 mb-4">${ssrInterpolate(week.content)}</p><ul class="list-disc list-inside space-y-2"><!--[-->`);
        ssrRenderList(week.activities, (activity) => {
          _push(`<li class="text-gray-600">${ssrInterpolate(activity)}</li>`);
        });
        _push(`<!--]--></ul></div>`);
      });
      _push(`<!--]--></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/course/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-Dryg56LC.mjs.map
