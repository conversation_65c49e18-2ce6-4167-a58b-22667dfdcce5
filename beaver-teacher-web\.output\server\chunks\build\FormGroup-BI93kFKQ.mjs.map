{"version": 3, "file": "FormGroup-BI93kFKQ.mjs", "sources": ["../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/ui.config/forms/formGroup.js", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/forms/FormGroup.vue"], "sourcesContent": null, "names": ["_ssrRenderAttrs", "_mergeProps", "_ssrRenderClass", "_ssrRenderAttr", "_ssrInterpolate"], "mappings": "", "x_google_ignoreList": [0, 1]}