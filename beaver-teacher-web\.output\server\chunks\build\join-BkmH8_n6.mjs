import { _ as __nuxt_component_1 } from './LanguageToggle-iHF9iQqL.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_7 } from './Card-DSOtZzuw.mjs';
import { useSSRContext, defineComponent, computed, mergeProps, withCtx, createVNode, toDisplayString, createTextVNode, ref, unref, isRef } from 'vue';
import { ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrRenderAttr } from 'vue/server-renderer';
import DOMPurify from 'dompurify';
import { _ as __nuxt_component_0$1 } from './client-only-C3WHot0o.mjs';
import { f as useRouter, K as useAuthStore, c as useToast, B as useI18n, k as useRuntimeConfig, M as authApi } from './server.mjs';
import { s as setInterval } from './interval-gl53xdpR.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import './useFormGroup-B3564yef.mjs';
import '@vueuse/core';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "NoticePopup",
  __ssrInlineRender: true,
  props: {
    modelValue: { type: Boolean },
    notice: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const isVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const sanitizeHTML = (html) => {
      console.log("Original HTML:", html);
      DOMPurify.addHook("afterSanitizeAttributes", function(node) {
        if (node.hasAttribute("style")) {
          const style = node.getAttribute("style");
          if (style) {
            const enhancedStyle = style.split(";").map((s) => s.trim()).filter((s) => s).map((s) => `${s} !important`).join(";");
            node.setAttribute("style", enhancedStyle);
            node.removeAttribute("class");
          }
        }
      });
      const sanitized = DOMPurify.sanitize(html, {
        ADD_TAGS: [
          // 标题和文本
          "h1",
          "h2",
          "h3",
          "h4",
          "h5",
          "h6",
          "p",
          "br",
          "strong",
          "em",
          "u",
          "span",
          // 格式化
          "b",
          "i",
          "pre",
          "code",
          "blockquote",
          "mark",
          "del",
          "ins",
          "sub",
          "sup",
          "small",
          // 列表
          "ol",
          "ul",
          "li",
          // 表格
          "table",
          "thead",
          "tbody",
          "tr",
          "th",
          "td",
          // 其他
          "div",
          "hr",
          "img",
          "a",
          "font"
        ],
        ADD_ATTR: [
          // 通用属性
          "style",
          "class",
          "id",
          "name",
          // 链接属性
          "href",
          "target",
          "rel",
          // 图片属性
          "src",
          "alt",
          "width",
          "height",
          // 表格属性
          "colspan",
          "rowspan",
          "align",
          "valign",
          "border",
          // 字体属性
          "color",
          "size",
          "face"
        ],
        ADD_DATA_URI_TAGS: ["img"],
        ALLOW_DATA_ATTR: true,
        ALLOW_UNKNOWN_PROTOCOLS: true,
        ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|xxx):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
        FORBID_TAGS: ["script", "style", "iframe", "frame", "object", "embed"],
        FORBID_ATTR: ["onerror", "onload", "onclick", "onmouseover"],
        USE_PROFILES: { html: true }
      });
      console.log("Sanitized HTML:", sanitized);
      return sanitized;
    };
    const safePopupContent = computed(() => {
      var _a;
      if (!((_a = props.notice) == null ? void 0 : _a.content))
        return "";
      return sanitizeHTML(props.notice.content);
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UModal = __nuxt_component_6;
      const _component_UCard = __nuxt_component_7;
      const _component_UButton = __nuxt_component_2;
      _push(ssrRenderComponent(_component_UModal, mergeProps({
        modelValue: isVisible.value,
        "onUpdate:modelValue": ($event) => isVisible.value = $event,
        ui: {
          width: "sm:max-w-lg",
          container: "flex items-center justify-center min-h-screen p-4"
        },
        "prevent-close": true
      }, _attrs), {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UCard, { ui: { divide: "divide-y divide-gray-100 dark:divide-gray-800" } }, {
              header: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex items-center"${_scopeId2}><h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white"${_scopeId2}>${ssrInterpolate(_ctx.$t("messages.popup.notice.title"))}</h3></div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex items-center" }, [
                      createVNode("h3", { class: "text-base font-semibold leading-6 text-gray-900 dark:text-white" }, toDisplayString(_ctx.$t("messages.popup.notice.title")), 1)
                    ])
                  ];
                }
              }),
              footer: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex justify-end"${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "gray",
                    variant: "solid",
                    onClick: ($event) => isVisible.value = false
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate(_ctx.$t("messages.popup.notice.confirm"))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString(_ctx.$t("messages.popup.notice.confirm")), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex justify-end" }, [
                      createVNode(_component_UButton, {
                        color: "gray",
                        variant: "solid",
                        onClick: ($event) => isVisible.value = false
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(_ctx.$t("messages.popup.notice.confirm")), 1)
                        ]),
                        _: 1
                      }, 8, ["onClick"])
                    ])
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                var _a;
                if (_push3) {
                  _push3(`<div class="p-4"${_scopeId2}><div class="notice-popup-content whitespace-pre-wrap"${_scopeId2}>${(_a = safePopupContent.value) != null ? _a : ""}</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "p-4" }, [
                      createVNode("div", {
                        innerHTML: safePopupContent.value,
                        class: "notice-popup-content whitespace-pre-wrap"
                      }, null, 8, ["innerHTML"])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UCard, { ui: { divide: "divide-y divide-gray-100 dark:divide-gray-800" } }, {
                header: withCtx(() => [
                  createVNode("div", { class: "flex items-center" }, [
                    createVNode("h3", { class: "text-base font-semibold leading-6 text-gray-900 dark:text-white" }, toDisplayString(_ctx.$t("messages.popup.notice.title")), 1)
                  ])
                ]),
                footer: withCtx(() => [
                  createVNode("div", { class: "flex justify-end" }, [
                    createVNode(_component_UButton, {
                      color: "gray",
                      variant: "solid",
                      onClick: ($event) => isVisible.value = false
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(_ctx.$t("messages.popup.notice.confirm")), 1)
                      ]),
                      _: 1
                    }, 8, ["onClick"])
                  ])
                ]),
                default: withCtx(() => [
                  createVNode("div", { class: "p-4" }, [
                    createVNode("div", {
                      innerHTML: safePopupContent.value,
                      class: "notice-popup-content whitespace-pre-wrap"
                    }, null, 8, ["innerHTML"])
                  ])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/NoticePopup.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "join",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    const config = useRuntimeConfig();
    const userType = config.public.userType;
    useAuthStore();
    const toast = useToast();
    const { t } = useI18n();
    const isPasswordLogin = ref(false);
    const form = ref({
      phone: "",
      verificationCode: "",
      captcha: "",
      captchaKey: "",
      // Account password login fields
      password: ""
    });
    const loading = ref(false);
    const countdown = ref(0);
    const canSendCode = computed(() => countdown.value === 0);
    const banners = ref([]);
    const bannerLoading = ref(false);
    const currentSlide = ref(0);
    const showPopup = ref(false);
    const currentPopup = ref(null);
    const showStudentForgotPwd = ref(false);
    const captchaImage = ref("");
    const captchaLoading = ref(false);
    async function getCaptcha() {
      captchaLoading.value = true;
      try {
        const response = await authApi.getCaptcha();
        captchaImage.value = `data:image/png;base64,${response.image}`;
        form.value.captchaKey = response.key;
      } catch (error) {
      } finally {
        captchaLoading.value = false;
      }
    }
    async function sendVerificationCode() {
      if (!form.value.phone) {
        toast.add({
          title: t("messages.toast.error.title"),
          description: t("messages.signup.validation.phone.required"),
          color: "red"
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(form.value.phone)) {
        toast.add({
          title: t("messages.toast.error.title"),
          description: t("messages.signup.validation.phone.invalid"),
          color: "red"
        });
        return;
      }
      loading.value = true;
      try {
        await authApi.sendSmsCode(form.value.phone, 1);
        countdown.value = 60;
        const timer = setInterval(() => {
          countdown.value--;
          if (countdown.value === 0) {
            clearInterval(timer);
          }
        }, 1e3);
        toast.add({
          title: t("messages.toast.sms.success.title"),
          description: t("messages.toast.sms.success.description"),
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
      } catch (error) {
        toast.add({
          title: t("messages.toast.sms.error.title"),
          description: error.message || t("messages.toast.sms.error.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    }
    function goToSignIn() {
      router.push("/signin");
    }
    function handleJoinClick() {
      router.push("/teacher/terms");
    }
    function toggleLoginMethod() {
      isPasswordLogin.value = !isPasswordLogin.value;
      form.value = {
        phone: form.value.phone,
        // Keep phone number when switching
        verificationCode: "",
        captcha: "",
        captchaKey: form.value.captchaKey,
        password: ""
      };
      getCaptcha();
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_LanguageToggle = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      const _component_UInput = __nuxt_component_9;
      const _component_NoticePopup = _sfc_main$1;
      const _component_ClientOnly = __nuxt_component_0$1;
      const _component_UModal = __nuxt_component_6;
      const _component_UCard = __nuxt_component_7;
      _push(`<!--[--><div class="min-h-screen bg-gray-50 flex flex-col" data-v-0009e45f>`);
      _push(ssrRenderComponent(_component_LanguageToggle, null, null, _parent));
      _push(`<div class="max-w-screen-2xl mx-auto flex-1" data-v-0009e45f><div class="max-w-[1200px] mx-auto" data-v-0009e45f>`);
      if (unref(bannerLoading)) {
        _push(`<div class="w-full aspect-[16/8] flex items-center justify-center" data-v-0009e45f>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin text-4xl text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!--[-->`);
        if (unref(banners).length > 0) {
          _push(`<div class="relative w-full aspect-[16/8] overflow-hidden" data-v-0009e45f><div class="relative w-full h-full" data-v-0009e45f><!--[-->`);
          ssrRenderList(unref(banners), (banner, index) => {
            _push(`<div class="${ssrRenderClass([index === unref(currentSlide) ? "opacity-100 z-10" : "opacity-0 z-0", "absolute inset-0 transition-opacity duration-500"])}" data-v-0009e45f><div class="relative w-full h-full cursor-pointer group" data-v-0009e45f><img${ssrRenderAttr("src", banner.imageUrl)}${ssrRenderAttr("alt", banner.title)} class="w-full h-full object-cover" data-v-0009e45f>`);
            if (banner.title) {
              _push(`<div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent text-white p-4 transform transition-transform duration-300 group-hover:translate-y-0" data-v-0009e45f><h3 class="text-lg font-medium" data-v-0009e45f>${ssrInterpolate(banner.title)}</h3></div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div></div>`);
          });
          _push(`<!--]--></div>`);
          if (unref(banners).length > 1) {
            _push(`<div class="absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4 z-20" data-v-0009e45f><button class="bg-black/20 hover:bg-black/40 rounded-full p-2 transition-colors backdrop-blur-sm" data-v-0009e45f>`);
            _push(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-chevron-left-20-solid",
              class: "w-6 h-6 text-white"
            }, null, _parent));
            _push(`</button><button class="bg-black/20 hover:bg-black/40 rounded-full p-2 transition-colors backdrop-blur-sm" data-v-0009e45f>`);
            _push(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-chevron-right-20-solid",
              class: "w-6 h-6 text-white"
            }, null, _parent));
            _push(`</button></div>`);
          } else {
            _push(`<!---->`);
          }
          if (unref(banners).length > 1) {
            _push(`<div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-20" data-v-0009e45f><!--[-->`);
            ssrRenderList(unref(banners), (_, index) => {
              _push(`<button class="${ssrRenderClass([unref(currentSlide) === index ? "bg-white" : "bg-white/50", "w-2 h-2 rounded-full transition-colors backdrop-blur-sm"])}" data-v-0009e45f></button>`);
            });
            _push(`<!--]--></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        } else {
          _push(`<div class="w-full aspect-[16/6]" data-v-0009e45f><img${ssrRenderAttr("src", unref(userType) === "teacher" ? "/images/cover/teacher.png" : "/images/cover/student.png")}${ssrRenderAttr("alt", unref(userType) === "teacher" ? "Teacher Cover" : "Student Cover")} class="w-full h-full object-cover" data-v-0009e45f></div>`);
        }
        _push(`<!--]-->`);
      }
      _push(`</div><div class="h-10" data-v-0009e45f></div><div class="px-4" data-v-0009e45f><div class="max-w-md mx-auto" data-v-0009e45f>`);
      if (unref(userType) === "student" || unref(userType) === "all") {
        _push(`<div class="bg-white p-8 rounded-xl border border-gray-200 mb-6" data-v-0009e45f><form class="space-y-4" data-v-0009e45f><div class="flex justify-end mb-4" data-v-0009e45f>`);
        _push(ssrRenderComponent(_component_UButton, {
          variant: "link",
          color: "gray",
          onClick: toggleLoginMethod
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(isPasswordLogin) ? unref(t)("messages.signin.student.form.switchToSms") : unref(t)("messages.signin.student.form.switchToPassword"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(isPasswordLogin) ? unref(t)("messages.signin.student.form.switchToSms") : unref(t)("messages.signin.student.form.switchToPassword")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
        if (!unref(isPasswordLogin)) {
          _push(`<!--[--><div data-v-0009e45f>`);
          _push(ssrRenderComponent(_component_UInput, {
            modelValue: unref(form).phone,
            "onUpdate:modelValue": ($event) => unref(form).phone = $event,
            type: "tel",
            placeholder: unref(t)("messages.signin.student.form.phonePlaceholder"),
            class: "w-full",
            size: "lg"
          }, null, _parent));
          _push(`</div><div class="flex gap-2" data-v-0009e45f>`);
          _push(ssrRenderComponent(_component_UInput, {
            modelValue: unref(form).verificationCode,
            "onUpdate:modelValue": ($event) => unref(form).verificationCode = $event,
            placeholder: unref(t)("messages.signin.student.form.verificationCodePlaceholder"),
            class: "flex-1",
            size: "lg"
          }, null, _parent));
          _push(ssrRenderComponent(_component_UButton, {
            disabled: !unref(canSendCode) || unref(loading),
            onClick: sendVerificationCode,
            class: "whitespace-nowrap",
            color: "primary",
            size: "lg"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(unref(canSendCode) ? unref(t)("messages.signin.student.form.sendCode") : unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) }))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(unref(canSendCode) ? unref(t)("messages.signin.student.form.sendCode") : unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) })), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div><!--]-->`);
        } else {
          _push(`<!--[--><div data-v-0009e45f>`);
          _push(ssrRenderComponent(_component_UInput, {
            modelValue: unref(form).phone,
            "onUpdate:modelValue": ($event) => unref(form).phone = $event,
            type: "tel",
            placeholder: unref(t)("messages.signin.student.form.phonePlaceholder"),
            class: "w-full",
            size: "lg"
          }, null, _parent));
          _push(`</div><div data-v-0009e45f>`);
          _push(ssrRenderComponent(_component_UInput, {
            modelValue: unref(form).password,
            "onUpdate:modelValue": ($event) => unref(form).password = $event,
            type: "password",
            placeholder: unref(t)("messages.signin.student.form.passwordPlaceholder"),
            class: "w-full",
            size: "lg"
          }, null, _parent));
          _push(`</div><!--]-->`);
        }
        _push(`<div class="flex gap-2 items-stretch" data-v-0009e45f>`);
        _push(ssrRenderComponent(_component_UInput, {
          modelValue: unref(form).captcha,
          "onUpdate:modelValue": ($event) => unref(form).captcha = $event,
          placeholder: unref(t)("messages.signin.student.form.imageCaptchaPlaceholder"),
          class: "flex-1",
          size: "lg"
        }, null, _parent));
        _push(`<div class="w-32 h-11 flex items-center justify-center border rounded cursor-pointer overflow-hidden" data-v-0009e45f>`);
        if (unref(captchaLoading)) {
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-arrow-path",
            class: "animate-spin text-2xl"
          }, null, _parent));
        } else {
          _push(`<img${ssrRenderAttr("src", unref(captchaImage))}${ssrRenderAttr("alt", unref(t)("messages.signin.student.form.imageCaptcha"))} class="w-full h-full object-cover" data-v-0009e45f>`);
        }
        _push(`</div></div>`);
        _push(ssrRenderComponent(_component_UButton, {
          type: "submit",
          block: "",
          loading: unref(loading),
          class: "mt-6",
          color: "primary"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.signin.buttons.login"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.signin.buttons.login")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</form><div class="text-center text-sm text-gray-500 mt-4" data-v-0009e45f>${ssrInterpolate(unref(t)("messages.signin.student.loginHint"))}</div><div class="flex justify-between text-sm text-primary mt-4" data-v-0009e45f><a href="#" class="hover:underline" data-v-0009e45f>${ssrInterpolate(unref(t)("messages.forgotPassword.title"))}</a><a href="#" class="hover:underline" data-v-0009e45f>${ssrInterpolate(unref(t)("messages.join.buttons.terms"))}</a></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (unref(userType) === "all" || unref(userType) === "teacher") {
        _push(`<div class="max-w-md mx-auto space-y-4 w-full" data-v-0009e45f>`);
        _push(ssrRenderComponent(_component_UButton, {
          block: "",
          size: "lg",
          color: "gray",
          variant: "solid",
          class: "whitespace-nowrap flex items-center justify-center",
          onClick: handleJoinClick
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-user-plus",
                class: "shrink-0 mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.join.buttons.joinUs"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-user-plus",
                  class: "shrink-0 mr-2"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.join.buttons.joinUs")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`<div class="text-center" data-v-0009e45f><p class="text-gray-600 mb-2" data-v-0009e45f>${ssrInterpolate(_ctx.$t("messages.join.buttons.hasAccount"))}</p>`);
        _push(ssrRenderComponent(_component_UButton, {
          block: "",
          size: "lg",
          color: "white",
          variant: "outline",
          class: "whitespace-nowrap flex items-center justify-center",
          onClick: goToSignIn
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-arrow-right-on-rectangle",
                class: "shrink-0 mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.join.buttons.signIn"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-arrow-right-on-rectangle",
                  class: "shrink-0 mr-2"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.join.buttons.signIn")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
      _push(ssrRenderComponent(_component_NoticePopup, {
        modelValue: unref(showPopup),
        "onUpdate:modelValue": ($event) => isRef(showPopup) ? showPopup.value = $event : null,
        notice: unref(currentPopup)
      }, null, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: unref(showStudentForgotPwd),
        "onUpdate:modelValue": ($event) => isRef(showStudentForgotPwd) ? showStudentForgotPwd.value = $event : null
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UCard, null, {
              header: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex items-center" data-v-0009e45f${_scopeId2}><h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white" data-v-0009e45f${_scopeId2}>${ssrInterpolate(unref(t)("messages.signin.studentForgotPassword.title"))}</h3></div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex items-center" }, [
                      createVNode("h3", { class: "text-base font-semibold leading-6 text-gray-900 dark:text-white" }, toDisplayString(unref(t)("messages.signin.studentForgotPassword.title")), 1)
                    ])
                  ];
                }
              }),
              footer: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex justify-end" data-v-0009e45f${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "primary",
                    onClick: ($event) => showStudentForgotPwd.value = false
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate(unref(t)("messages.common.confirm"))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString(unref(t)("messages.common.confirm")), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex justify-end" }, [
                      createVNode(_component_UButton, {
                        color: "primary",
                        onClick: ($event) => showStudentForgotPwd.value = false
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(unref(t)("messages.common.confirm")), 1)
                        ]),
                        _: 1
                      }, 8, ["onClick"])
                    ])
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="p-2 text-gray-700 dark:text-gray-200" data-v-0009e45f${_scopeId2}>${ssrInterpolate(unref(t)("messages.signin.studentForgotPassword.message"))}</div>`);
                } else {
                  return [
                    createVNode("div", { class: "p-2 text-gray-700 dark:text-gray-200" }, toDisplayString(unref(t)("messages.signin.studentForgotPassword.message")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UCard, null, {
                header: withCtx(() => [
                  createVNode("div", { class: "flex items-center" }, [
                    createVNode("h3", { class: "text-base font-semibold leading-6 text-gray-900 dark:text-white" }, toDisplayString(unref(t)("messages.signin.studentForgotPassword.title")), 1)
                  ])
                ]),
                footer: withCtx(() => [
                  createVNode("div", { class: "flex justify-end" }, [
                    createVNode(_component_UButton, {
                      color: "primary",
                      onClick: ($event) => showStudentForgotPwd.value = false
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(unref(t)("messages.common.confirm")), 1)
                      ]),
                      _: 1
                    }, 8, ["onClick"])
                  ])
                ]),
                default: withCtx(() => [
                  createVNode("div", { class: "p-2 text-gray-700 dark:text-gray-200" }, toDisplayString(unref(t)("messages.signin.studentForgotPassword.message")), 1)
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/join.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const join = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-0009e45f"]]);

export { join as default };
//# sourceMappingURL=join-BkmH8_n6.mjs.map
