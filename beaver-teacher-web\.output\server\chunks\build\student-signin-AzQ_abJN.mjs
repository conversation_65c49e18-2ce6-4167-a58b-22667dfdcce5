import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { toRefs, defineComponent, ref, computed, mergeProps, unref, withCtx, createVNode, createTextVNode, toDisplayString, openBlock, createBlock, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderAttr } from 'vue/server-renderer';
import { defineStore } from 'pinia';
import { useStorage } from '@vueuse/core';
import { s as studentApi } from './student-DtKAviut.mjs';
import { f as useRouter, c as useToast, K as useAuthStore, B as useI18n, M as authApi } from './server.mjs';
import { s as setInterval } from './interval-gl53xdpR.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './useFormGroup-B3564yef.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const useStudentStore = defineStore("student", () => {
  const state = useStorage("student-state", {
    id: "",
    phone: "",
    name: "",
    avatar: "",
    isLoggedIn: false
  });
  async function login(params) {
    try {
      const response = await studentApi.login(params);
      setStudentInfo(response);
      return response;
    } catch (error) {
      throw error;
    }
  }
  function setStudentInfo(studentInfo) {
    state.value.id = studentInfo.id;
    state.value.phone = studentInfo.phone;
    state.value.name = studentInfo.name || "";
    state.value.avatar = studentInfo.avatar || "";
    state.value.isLoggedIn = true;
  }
  async function getStudentInfo() {
    try {
      const response = await studentApi.getStudentInfo(state.value.id);
      setStudentInfo(response);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async function logout() {
    try {
      await studentApi.logout();
      clearStudentInfo();
    } catch (error) {
      throw error;
    }
  }
  function clearStudentInfo() {
    state.value.id = "";
    state.value.phone = "";
    state.value.name = "";
    state.value.avatar = "";
    state.value.isLoggedIn = false;
  }
  return {
    ...toRefs(state.value),
    login,
    setStudentInfo,
    getStudentInfo,
    logout,
    clearStudentInfo
  };
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "student-signin",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const toast = useToast();
    const loading = ref(false);
    useStudentStore();
    useAuthStore();
    const { t } = useI18n();
    const form = ref({
      phone: "",
      verificationCode: "",
      captcha: "",
      captchaKey: ""
    });
    const formState = ref({
      phone: "",
      verificationCode: "",
      captcha: ""
    });
    const countdown = ref(0);
    const canSendCode = computed(() => countdown.value === 0);
    const captchaImage = ref("");
    const captchaLoading = ref(false);
    async function getCaptcha() {
      captchaLoading.value = true;
      try {
        const response = await authApi.getCaptcha();
        captchaImage.value = `data:image/png;base64,${response.image}`;
        form.value.captchaKey = response.key;
      } catch (error) {
        toast.add({
          title: t("messages.toast.captcha.error.title"),
          description: t("messages.toast.captcha.error.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        captchaLoading.value = false;
      }
    }
    async function sendVerificationCode() {
      if (!form.value.phone) {
        formState.value.phone = t("messages.signup.validation.phone.required");
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(form.value.phone)) {
        formState.value.phone = t("messages.signup.validation.phone.invalid");
        return;
      }
      loading.value = true;
      try {
        await authApi.sendSmsCode(form.value.phone, 1);
        countdown.value = 60;
        const timer = setInterval(() => {
          countdown.value--;
          if (countdown.value === 0) {
            clearInterval(timer);
          }
        }, 1e3);
        toast.add({
          title: t("messages.toast.sms.success.title"),
          description: t("messages.toast.sms.success.description"),
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
      } catch (error) {
        toast.add({
          title: t("messages.toast.sms.error.title"),
          description: t("messages.toast.sms.error.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_UButton = __nuxt_component_2;
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 bg-gray-50 flex flex-col" }, _attrs))}><div class="container mx-auto px-4 py-6 md:py-12 flex-1 safe-area"><div class="max-w-md mx-auto w-full"><div class="text-center mb-8"><h1 class="text-xl font-bold mb-2">${ssrInterpolate(_ctx.$t("messages.signin.student.title"))}</h1><p class="text-gray-600">${ssrInterpolate(_ctx.$t("messages.signin.student.subtitle"))}</p></div><div class="bg-white p-6 rounded-xl border border-gray-200"><form class="space-y-4"><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: _ctx.$t("messages.signin.student.form.phone"),
        error: unref(formState).phone
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).phone,
              "onUpdate:modelValue": ($event) => unref(form).phone = $event,
              type: "tel",
              placeholder: _ctx.$t("messages.signin.student.form.phonePlaceholder"),
              error: !!unref(formState).phone,
              autocomplete: "tel"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UInput, {
                modelValue: unref(form).phone,
                "onUpdate:modelValue": ($event) => unref(form).phone = $event,
                type: "tel",
                placeholder: _ctx.$t("messages.signin.student.form.phonePlaceholder"),
                error: !!unref(formState).phone,
                autocomplete: "tel"
              }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: _ctx.$t("messages.signin.student.form.verificationCode"),
        error: unref(formState).verificationCode
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).verificationCode,
              "onUpdate:modelValue": ($event) => unref(form).verificationCode = $event,
              placeholder: _ctx.$t("messages.signin.student.form.verificationCodePlaceholder"),
              error: !!unref(formState).verificationCode,
              autocomplete: "off",
              class: "flex-1"
            }, null, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UButton, {
              disabled: !unref(canSendCode) || unref(loading),
              onClick: sendVerificationCode,
              class: "whitespace-nowrap",
              color: "primary"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(unref(canSendCode) ? _ctx.$t("messages.signin.student.form.sendCode") : _ctx.$t("messages.signin.student.form.resendCode", { countdown: unref(countdown) }))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(unref(canSendCode) ? _ctx.$t("messages.signin.student.form.sendCode") : _ctx.$t("messages.signin.student.form.resendCode", { countdown: unref(countdown) })), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode(_component_UInput, {
                  modelValue: unref(form).verificationCode,
                  "onUpdate:modelValue": ($event) => unref(form).verificationCode = $event,
                  placeholder: _ctx.$t("messages.signin.student.form.verificationCodePlaceholder"),
                  error: !!unref(formState).verificationCode,
                  autocomplete: "off",
                  class: "flex-1"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                createVNode(_component_UButton, {
                  disabled: !unref(canSendCode) || unref(loading),
                  onClick: sendVerificationCode,
                  class: "whitespace-nowrap",
                  color: "primary"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(canSendCode) ? _ctx.$t("messages.signin.student.form.sendCode") : _ctx.$t("messages.signin.student.form.resendCode", { countdown: unref(countdown) })), 1)
                  ]),
                  _: 1
                }, 8, ["disabled"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: _ctx.$t("messages.signin.student.form.imageCaptcha"),
        error: unref(formState).captcha
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).captcha,
              "onUpdate:modelValue": ($event) => unref(form).captcha = $event,
              placeholder: _ctx.$t("messages.signin.student.form.imageCaptchaPlaceholder"),
              error: !!unref(formState).captcha,
              class: "flex-1"
            }, null, _parent2, _scopeId));
            _push2(`<div class="w-32 h-11 flex items-center justify-center border rounded-lg cursor-pointer overflow-hidden"${_scopeId}>`);
            if (unref(captchaLoading)) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-arrow-path",
                class: "animate-spin text-2xl"
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<img${ssrRenderAttr("src", unref(captchaImage))} alt="\u56FE\u5F62\u9A8C\u8BC1\u7801" class="w-full h-full object-cover"${_scopeId}>`);
            }
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode(_component_UInput, {
                  modelValue: unref(form).captcha,
                  "onUpdate:modelValue": ($event) => unref(form).captcha = $event,
                  placeholder: _ctx.$t("messages.signin.student.form.imageCaptchaPlaceholder"),
                  error: !!unref(formState).captcha,
                  class: "flex-1"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                createVNode("div", {
                  class: "w-32 h-11 flex items-center justify-center border rounded-lg cursor-pointer overflow-hidden",
                  onClick: getCaptcha
                }, [
                  unref(captchaLoading) ? (openBlock(), createBlock(_component_UIcon, {
                    key: 0,
                    name: "i-heroicons-arrow-path",
                    class: "animate-spin text-2xl"
                  })) : (openBlock(), createBlock("img", {
                    key: 1,
                    src: unref(captchaImage),
                    alt: "\u56FE\u5F62\u9A8C\u8BC1\u7801",
                    class: "w-full h-full object-cover"
                  }, null, 8, ["src"]))
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_UButton, {
        type: "submit",
        block: "",
        size: "lg",
        loading: unref(loading),
        class: "mt-6",
        color: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-arrow-right-on-rectangle",
              class: "mr-2"
            }, null, _parent2, _scopeId));
            _push2(` ${ssrInterpolate(_ctx.$t("messages.signin.buttons.login"))}`);
          } else {
            return [
              createVNode(_component_UIcon, {
                name: "i-heroicons-arrow-right-on-rectangle",
                class: "mr-2"
              }),
              createTextVNode(" " + toDisplayString(_ctx.$t("messages.signin.buttons.login")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</form></div><div class="mt-6 text-center text-sm text-gray-600">${ssrInterpolate(_ctx.$t("messages.signin.student.loginHint"))}</div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student-signin.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=student-signin-AzQ_abJN.mjs.map
