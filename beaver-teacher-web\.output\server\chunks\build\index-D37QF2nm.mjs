import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { defineComponent, ref, withCtx, createTextVNode, toDisplayString, unref, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrRenderStyle } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const courseCards = ref([]);
    const loading = ref(false);
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString("zh-CN");
    };
    const getStatusInfo = (status) => {
      const { t } = useI18n();
      switch (status) {
        case 0:
          return { text: t("messages.courseCards.status.expired"), color: "red" };
        case 1:
          return { text: t("messages.courseCards.status.active"), color: "green" };
        case 2:
          return { text: t("messages.courseCards.status.exhausted"), color: "red" };
        case 3:
          return { text: t("messages.courseCards.status.refunded"), color: "red" };
        default:
          return { text: t("messages.courseCards.status.unknown"), color: "gray" };
      }
    };
    const getCardTypeText = (cardType) => {
      const { t } = useI18n();
      return cardType === 1 ? t("messages.courseCards.cardType.oneToOne") : t("messages.courseCards.cardType.group");
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_UBadge = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.courseCards.title"))}</h1></div><div class="max-w-4xl mx-auto px-4 md:px-6 pt-[calc(48px+env(safe-area-inset-top))] md:pt-6 pb-6"><div class="flex items-center justify-between mb-4 sm:mb-6"><div class="hidden sm:flex items-center"><h1 class="text-xl font-bold">${ssrInterpolate(_ctx.$t("messages.courseCards.title"))}</h1></div>`);
      _push(ssrRenderComponent(_component_UButton, {
        to: "/student/course-cards/buy",
        color: "primary",
        variant: "solid"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("messages.courseCards.buttons.purchase"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("messages.courseCards.buttons.purchase")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      if (unref(loading)) {
        _push(`<div><div class="grid gap-6"><!--[-->`);
        ssrRenderList(3, (i) => {
          _push(`<div class="bg-white rounded-xl border border-gray-200 p-6"><div class="flex justify-between items-start mb-4"><div><div class="h-6 bg-gray-200 rounded animate-pulse w-32 mb-2"></div><div class="h-4 bg-gray-200 rounded animate-pulse w-24"></div></div><div class="h-6 bg-gray-200 rounded animate-pulse w-16"></div></div><div class="mt-4"><div class="flex justify-between mb-2"><div class="h-4 bg-gray-200 rounded animate-pulse w-16"></div><div class="h-4 bg-gray-200 rounded animate-pulse w-20"></div></div><div class="w-full bg-gray-200 rounded-full h-2 animate-pulse"></div></div></div>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<div><div class="grid gap-6"><!--[-->`);
        ssrRenderList(unref(courseCards), (card) => {
          _push(`<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-100 transition-all duration-300"><div class="flex items-center justify-between mb-4"><div class="space-y-2"><div class="flex items-center gap-2"><h3 class="text-xl font-bold text-gray-800">${ssrInterpolate(card.name)}</h3>`);
          _push(ssrRenderComponent(_component_UBadge, {
            color: card.cardType === 1 ? "primary" : "purple",
            class: "text-sm font-medium",
            variant: "solid"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(getCardTypeText(card.cardType))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(getCardTypeText(card.cardType)), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div><p class="text-sm text-gray-500 flex items-center gap-2">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-calendar",
            class: "w-4 h-4"
          }, null, _parent));
          _push(` ${ssrInterpolate(_ctx.$t("messages.courseCards.card.validUntil"))}: ${ssrInterpolate(formatDate(card.expireDate))}</p></div>`);
          _push(ssrRenderComponent(_component_UBadge, {
            color: getStatusInfo(card.status).color,
            class: "text-sm font-medium",
            variant: "solid"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(getStatusInfo(card.status).text)}`);
              } else {
                return [
                  createTextVNode(toDisplayString(getStatusInfo(card.status).text), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div><div class="space-y-4"><div class="flex items-center justify-between text-sm text-gray-600"><div class="flex items-center gap-2">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-clock",
            class: "w-4 h-4 text-primary"
          }, null, _parent));
          _push(`<span>${ssrInterpolate(_ctx.$t("messages.courseCards.card.remainingLessons"))}</span></div><span class="font-medium">${ssrInterpolate(card.remainingLessons)}/${ssrInterpolate(card.totalLessons)}</span></div><div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden"><div class="${ssrRenderClass([{
            "bg-primary": card.status === 1,
            "bg-red-500": card.status === 0 || card.status === 2,
            "bg-gray-400": card.status === 3
          }, "h-full rounded-full transition-all duration-500"])}" style="${ssrRenderStyle(`width: ${card.remainingLessons / card.totalLessons * 100}%`)}"></div></div></div></div>`);
        });
        _push(`<!--]--></div>`);
        if (unref(courseCards).length === 0) {
          _push(`<div class="text-center py-12">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-ticket",
            class: "w-16 h-16 mx-auto text-gray-400"
          }, null, _parent));
          _push(`<p class="text-gray-500 mt-4">${ssrInterpolate(_ctx.$t("messages.courseCards.card.noCards"))}</p>`);
          _push(ssrRenderComponent(_component_UButton, {
            class: "mt-4",
            to: "/student/course-cards/buy",
            color: "primary",
            variant: "solid"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.$t("messages.courseCards.buttons.buyNow"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.$t("messages.courseCards.buttons.buyNow")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      }
      _push(`</div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/course-cards/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-D37QF2nm.mjs.map
