import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_4 } from './Pagination-JPpl4AYq.mjs';
import { defineComponent, ref, computed, watch, unref, withCtx, createVNode, toDisplayString, createTextVNode, isRef, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderComponent, ssrRenderList, ssrRenderAttr } from 'vue/server-renderer';
import { f as useRouter, K as useAuthStore, c as useToast, B as useI18n } from './server.mjs';
import { s as studentApi } from './student-DtKAviut.mjs';
import { g as generateDateRange } from './datetime-BvKd-1hF.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "dashboard",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    useAuthStore();
    const toast = useToast();
    useI18n();
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const teachers = ref([]);
    const loading = ref(false);
    const selectedDate = ref("");
    const weekDates = computed(() => {
      return generateDateRange(7, true);
    });
    const selectDate = (date) => {
      selectedDate.value = date;
      currentPage.value = 1;
      fetchTeachers();
    };
    const clearDateFilter = () => {
      selectedDate.value = "";
      currentPage.value = 1;
      fetchTeachers();
    };
    const fetchTeachers = async () => {
      loading.value = true;
      try {
        const response = await studentApi.getSchedulesTeacherList({
          name: "",
          startDate: selectedDate.value,
          endDate: selectedDate.value,
          // 使用相同的日期
          page: currentPage.value,
          pageSize: pageSize.value
        });
        teachers.value = response.list;
        total.value = response.totalCount;
      } catch (error) {
        toast.add({
          title: "\u83B7\u53D6\u6559\u5E08\u5217\u8868\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    };
    watch(currentPage, () => {
      fetchTeachers();
    });
    const bookTeacher = (teacherId) => {
      const teacher = teachers.value.find((t2) => t2.id === teacherId);
      router.push({
        path: `/student/book/${teacherId}`,
        query: {
          teacherName: teacher == null ? void 0 : teacher.nickname,
          date: selectedDate.value
          // 传递选中的日期
        }
      });
    };
    const currentLocalTime = ref("");
    const currentUTC8Time = ref("");
    const lastUpdateTime = ref("");
    ref(null);
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_UBadge = __nuxt_component_1;
      const _component_UPagination = __nuxt_component_4;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.schedule.title"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6"><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6"><div class="flex items-center justify-between"><div class="flex items-center gap-6 sm:gap-8"><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.localTime"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentLocalTime))}</span></div><div class="text-sm text-gray-600"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.utc8Time"))}\uFF1A</span><span class="font-mono">${ssrInterpolate(unref(currentUTC8Time))}</span></div></div><div class="text-xs text-gray-500">${ssrInterpolate(_ctx.$t("messages.schedule.currentTime.lastUpdate"))}\uFF1A${ssrInterpolate(unref(lastUpdateTime))}</div></div></div><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6"><div class="hidden sm:flex items-center justify-between mb-4 pb-3 border-b border-gray-100"><h2 class="text-lg font-semibold">${ssrInterpolate(_ctx.$t("messages.schedule.title"))}</h2></div><div class="mb-4 sm:mb-6"><div class="grid grid-cols-4 sm:grid-cols-8 gap-2">`);
      _push(ssrRenderComponent(_component_UButton, {
        color: !unref(selectedDate) ? "primary" : "gray",
        variant: !unref(selectedDate) ? "solid" : "soft",
        class: "w-full h-12 sm:h-12 justify-center rounded-lg",
        onClick: clearDateFilter
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex flex-col items-center leading-tight"${_scopeId}><span class="text-[13px]"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.date.filter.all"))}</span><span class="text-[11px]"${_scopeId}>${ssrInterpolate(_ctx.$t("messages.date.filter.noLimit"))}</span></div>`);
          } else {
            return [
              createVNode("div", { class: "flex flex-col items-center leading-tight" }, [
                createVNode("span", { class: "text-[13px]" }, toDisplayString(_ctx.$t("messages.date.filter.all")), 1),
                createVNode("span", { class: "text-[11px]" }, toDisplayString(_ctx.$t("messages.date.filter.noLimit")), 1)
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--[-->`);
      ssrRenderList(unref(weekDates), (date) => {
        _push(ssrRenderComponent(_component_UButton, {
          key: date.value,
          color: unref(selectedDate) === date.value ? "primary" : "gray",
          variant: unref(selectedDate) === date.value ? "solid" : "soft",
          class: "w-full h-12 sm:h-12 justify-center rounded-lg",
          onClick: ($event) => selectDate(date.value)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="flex flex-col items-center leading-tight"${_scopeId}><span class="text-[13px]"${_scopeId}>${ssrInterpolate(date.dayName)}</span><span class="text-[11px]"${_scopeId}>${ssrInterpolate(date.display)}</span></div>`);
            } else {
              return [
                createVNode("div", { class: "flex flex-col items-center leading-tight" }, [
                  createVNode("span", { class: "text-[13px]" }, toDisplayString(date.dayName), 1),
                  createVNode("span", { class: "text-[11px]" }, toDisplayString(date.display), 1)
                ])
              ];
            }
          }),
          _: 2
        }, _parent));
      });
      _push(`<!--]--></div></div>`);
      if (unref(loading)) {
        _push(`<div class="grid gap-4"><!--[-->`);
        ssrRenderList(3, (i) => {
          _push(`<div class="animate-pulse"><div class="h-20 bg-gray-100 rounded-lg"></div></div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<div class="space-y-4">`);
        if (unref(teachers).length === 0) {
          _push(`<div class="text-center py-10 text-gray-500"> \u6682\u65E0\u53EF\u9884\u7EA6\u7684\u6559\u5E08 </div>`);
        } else {
          _push(`<div><!--[-->`);
          ssrRenderList(unref(teachers), (teacher, index) => {
            _push(`<div class="py-3"><div class="flex justify-between items-start gap-4"><div class="flex items-start gap-4 min-w-0"><div class="w-14 h-14 sm:w-16 sm:h-16 rounded-full overflow-hidden flex-shrink-0 cursor-pointer"><img${ssrRenderAttr("src", teacher.avatarUrl || "/default-avatar.png")}${ssrRenderAttr("alt", teacher.nickname)} class="w-full h-full object-cover aspect-square"></div><div class="min-w-0"><h3 class="text-base sm:text-lg font-medium mb-1 truncate">${ssrInterpolate(teacher.nickname)}</h3><div class="text-gray-600 text-sm space-y-1"><p class="line-clamp-2">${ssrInterpolate(teacher.specialty)}</p><div class="flex flex-wrap gap-2 mt-2"><!--[-->`);
            ssrRenderList(teacher.tags, (tag) => {
              _push(ssrRenderComponent(_component_UBadge, {
                key: tag.id,
                color: "primary",
                variant: "subtle",
                class: "text-xs font-medium px-2.5 py-1 rounded-full"
              }, {
                default: withCtx((_, _push2, _parent2, _scopeId) => {
                  if (_push2) {
                    _push2(`${ssrInterpolate(tag.tagName)}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString(tag.tagName), 1)
                    ];
                  }
                }),
                _: 2
              }, _parent));
            });
            _push(`<!--]--></div></div></div></div>`);
            _push(ssrRenderComponent(_component_UButton, {
              color: "primary",
              variant: "soft",
              size: "sm",
              class: "!px-4 h-10 w-[84px] flex items-center justify-center whitespace-nowrap",
              onClick: ($event) => bookTeacher(teacher.id)
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(`${ssrInterpolate(_ctx.$t("messages.schedule.actions.book"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.schedule.actions.book")), 1)
                  ];
                }
              }),
              _: 2
            }, _parent));
            _push(`</div>`);
            if (index !== unref(teachers).length - 1) {
              _push(`<div class="h-px bg-gray-200 mt-3"></div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div>`);
      }
      if (unref(total) > unref(pageSize)) {
        _push(`<div class="mt-6 flex justify-center" dir="ltr">`);
        _push(ssrRenderComponent(_component_UPagination, {
          modelValue: unref(currentPage),
          "onUpdate:modelValue": ($event) => isRef(currentPage) ? currentPage.value = $event : null,
          total: unref(total),
          "page-size": unref(pageSize),
          ui: { rounded: "rounded-full" }
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/dashboard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=dashboard-Cmzo3liB.mjs.map
