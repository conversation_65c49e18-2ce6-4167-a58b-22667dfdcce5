const staticSuccess_vue_vue_type_style_index_0_scoped_f3011b87_lang = ".min-h-screen[data-v-f3011b87]{min-height:100vh;min-height:-webkit-fill-available}.glass[data-v-f3011b87]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);background:hsla(0,0%,100%,.25);border:1px solid hsla(0,0%,100%,.18)}.pulse-green[data-v-f3011b87]{animation:pulseGreen-f3011b87 2s infinite}@keyframes pulseGreen-f3011b87{0%,to{box-shadow:0 0 0 0 rgba(34,197,94,.7)}70%{box-shadow:0 0 0 10px rgba(34,197,94,0)}}button[data-v-f3011b87]{min-height:44px;min-width:44px}@media (max-width:768px){.max-w-md[data-v-f3011b87]{margin:1rem;width:calc(100% - 2rem)}button[data-v-f3011b87]{min-height:48px}}@media (min-width:320px) and (max-width:768px){.py-8[data-v-f3011b87]{padding-bottom:2rem;padding-top:2rem}.space-y-3[data-v-f3011b87]>*+*{margin-top:.75rem}}";

export { staticSuccess_vue_vue_type_style_index_0_scoped_f3011b87_lang as s };
//# sourceMappingURL=static-success-styles-1.mjs-k5__OG8y.mjs.map
