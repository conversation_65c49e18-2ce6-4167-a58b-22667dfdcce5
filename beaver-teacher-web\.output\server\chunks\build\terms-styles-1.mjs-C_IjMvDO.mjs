const terms_vue_vue_type_style_index_0_scoped_dd74f684_lang = ".terms-container[data-v-dd74f684]{min-height:100vh;padding-bottom:env(safe-area-inset-bottom)}.content-area[data-v-dd74f684]{min-height:calc(100vh - 140px)}@supports (padding-bottom:env(safe-area-inset-bottom)){.footer[data-v-dd74f684]{padding-bottom:calc(1rem + env(safe-area-inset-bottom))}}";

export { terms_vue_vue_type_style_index_0_scoped_dd74f684_lang as t };
//# sourceMappingURL=terms-styles-1.mjs-C_IjMvDO.mjs.map
