import { useSSRContext, defineComponent, computed, mergeProps, unref } from 'vue';
import { ssrRenderAttrs, ssrRenderClass, ssrRenderList, ssrRenderAttr, ssrInterpolate } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "LanguageToggle",
  __ssrInlineRender: true,
  props: {
    embedded: { type: Boolean, default: false }
  },
  setup(__props) {
    const { locale, setLocale } = useI18n();
    const languages = [
      { code: "zh-CN", name: "\u4E2D\u6587" },
      { code: "en-US", name: "EN" }
    ];
    const currentLocale = computed(() => locale.value);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({
        class: _ctx.embedded ? "inline-block" : "fixed top-4 right-4 z-50"
      }, _attrs))} data-v-e1d1dbca><div class="${ssrRenderClass([_ctx.embedded ? "shadow-sm" : "shadow-lg", "flex bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg p-1"])}" data-v-e1d1dbca><!--[-->`);
      ssrRenderList(languages, (lang) => {
        _push(`<button class="${ssrRenderClass([{
          "bg-primary text-white shadow-sm": unref(currentLocale) === lang.code,
          "text-gray-600 hover:text-primary hover:bg-primary/5": unref(currentLocale) !== lang.code
        }, "px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center whitespace-nowrap"])}"${ssrRenderAttr("title", `${_ctx.$t("languageToggle.switchTo")} ${lang.name}`)} data-v-e1d1dbca>${ssrInterpolate(lang.name)}</button>`);
      });
      _push(`<!--]--></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/generalElements/LanguageToggle.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const __nuxt_component_1 = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-e1d1dbca"]]);

export { __nuxt_component_1 as _ };
//# sourceMappingURL=LanguageToggle-iHF9iQqL.mjs.map
