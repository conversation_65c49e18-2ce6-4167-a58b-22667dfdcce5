{"file": "booked-courses-BL-4UTLQ.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHe,cAAU;AACzB,UAAM,YAAY,aAAa;AAC/B,UAAM,QAAQ,SAAS;AACjB,UAAA,EAAE,EAAE,IAAI,QAAQ;AAGhB,UAAA,cAAc,IAAI,CAAC;AACnB,UAAA,WAAW,IAAI,EAAE;AACjB,UAAA,QAAQ,IAAI,CAAC;AAGb,UAAA,UAAU,IAA2B,EAAE;AACvC,UAAA,UAAU,IAAI,KAAK;AACnB,UAAA,kBAAkB,IAAmB,IAAI;AAGzC,UAAA,mBAAmB,IAAI,EAAE;AACzB,UAAA,kBAAkB,IAAI,EAAE;AACxB,UAAA,iBAAiB,IAAI,EAAE;AAGH,QAA2C,IAAI;AACnE,UAAA,cAAc,IAAI,CAAC;AACH,QAAI,KAAK,IAAK,CAAA;AAGpC,UAAM,eAAe,YAAY;;AAC/B,UAAI,GAAC,qBAAU,SAAV,mBAAgB,YAAhB,mBAAyB,IAAI;AAElC,cAAQ,QAAQ;AACZ,UAAA;AACI,cAAA,WAAW,MAAM,WAAW,wBAAwB;AAAA,UACxD,IAAI,UAAU,KAAK,QAAQ;AAAA,UAC3B,WAAW;AAAA,UACX,SAAS;AAAA,UACT,MAAM,YAAY;AAAA,UAClB,UAAU,SAAS;AAAA,QAAA,CACpB;AAED,gBAAQ,QAAQ,SAAS;AACzB,cAAM,QAAQ,SAAS;AAAA,eAChB,OAAO;AACd,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAEpB;AAGA,UAAM,aAAa,MAAM;AACV,mBAAA;AAAA,IAAA,CACd;AAGK,UAAA,aAAa,CAAC,SAAiB;AAC7B,YAAA,IAAI,IAAI,KAAK,IAAI;AACjB,YAAA,QAAQ,EAAE,SAAA,IAAa;AACvB,YAAA,MAAM,EAAE,QAAQ;AACf,aAAA,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB;AA+BM,UAAAA,kBAAgB,CAAC,MAAc,SAAiB;AAC7C,aAAAC,cAAkB,MAAM,IAAI;AAAA,IACrC;AAoDM,UAAAC,kBAAgB,CAAC,WAAgC;AACzC,kBAAA;AAGZ,aAAOC,cAAkB,OAAO,cAAc,OAAO,WAAW,OAAO,OAAO;AAAA,IAChF;AAGM,UAAA,mBAAmB,CAAC,WAAgC;AAC5C,kBAAA;AAGZ,YAAM,cAAc,oBAAoB,OAAO,cAAc,OAAO,SAAS;AAE7E,UAAI,cAAc,GAAG;AACnB,eAAO,EAAE,4CAA4C;AAAA,MAAA,WAC5C,eAAe,KAAK;AAC7B,eAAO,EAAE,yCAAyC;AAAA,MAAA;AAE7C,aAAA;AAAA,IACT;AAGM,UAAA,mBAAmB,OAAO,WAAgC;;AAC9D,UAAI,gBAAgB,MAAO;AAC3B,sBAAgB,QAAQ,OAAO;AAE3B,UAAA;AAEF,cAAM,SAAS,iBAAiB;AAGhC,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,8CAA8C;AAAA,UACvD,aAAa,EAAE,oDAAoD;AAAA,UACnE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAGK,cAAA,EAAE,SAAS,OAAA,IAAW,MAAM,QAAQ,QAAQ,EAAE,UAAU,OAAO,IAAI;AAGzE,cAAM,eAAe,IAAI,IAAI,GAAG,OAAO,OAAO,SAAS,UAAU;AACjE,qBAAa,aAAa,IAAI,YAAU,eAAU,SAAV,mBAAgB,OAAM,EAAE;AAChE,qBAAa,aAAa,IAAI,UAAU,OAAO,UAAU;AACzD,qBAAa,aAAa,IAAI,YAAY,OAAO,EAAE;AACtC,qBAAA,aAAa,IAAI,WAAW,OAAO;AAGhD,cAAM,YAAYH,gBAAc,OAAO,cAAc,OAAO,SAAS,EAAE,QAAQ;AAC/E,cAAM,UAAUA,gBAAc,OAAO,cAAc,OAAO,OAAO,EAAE,QAAQ;AAC3E,qBAAa,aAAa,IAAI,kBAAkB,UAAU,UAAU;AACpE,qBAAa,aAAa,IAAI,gBAAgB,QAAQ,UAAU;AAGhE,cAAM,YAAmB,SAAA,KAAK,aAAa,YAAY,QAAQ;AAG3D,YAAA,CAAC,aAAa,UAAU,QAAQ;AAElC,gBAAM,IAAI;AAAA,YACR,OAAO,EAAE,oDAAoD;AAAA,YAC7D,aAAa,EAAE,0DAA0D;AAAA,YACzE,OAAO;AAAA,YACP,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AAGD,gBAAM,SAAS;AACf,gBAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAG,CAAC;AAG9C,UAAA,SAAA,SAAS,OAAO,aAAa,SAAS;AAC7C;AAAA,QAAA;AAIF,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,kDAAkD;AAAA,UAC3D,aAAa,EAAE,wDAAwD;AAAA,UACvE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACd,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,+CAA+C;AAAA,UACxD,aAAa,EAAE,qDAAqD;AAAA,UACpE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,wBAAgB,QAAQ;AAAA,MAAA;AAAA,IAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["parseDateTime", "parseDateTimeUtil", "canEnterClass", "canEnterClassUtil"], "sources": ["../../../../src/pages/student/booked-courses.vue"], "sourcesContent": ["<template>\r\n  <!-- <PERSON> Header -->\r\n  <div class=\"sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center\">\r\n    <h1 class=\"text-base font-semibold\">{{ $t('messages.bookedCourses.title') }}</h1>\r\n  </div>\r\n  <div class=\"container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area\">\r\n    <div class=\"max-w-4xl mx-auto w-full py-4 sm:py-6\">\r\n      <!-- 时间信息独立卡片 -->\r\n      <div class=\"bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6\">\r\n        <div class=\"flex items-center justify-between\">\r\n          <div class=\"flex items-center gap-6 sm:gap-8\">\r\n            <div class=\"text-sm text-gray-600\">\r\n              <span class=\"font-medium\">{{ $t('messages.schedule.currentTime.localTime') }}：</span>\r\n              <span class=\"font-mono\">{{ currentLocalTime }}</span>\r\n            </div>\r\n            <div class=\"text-sm text-gray-600\">\r\n              <span class=\"font-medium\">{{ $t('messages.schedule.currentTime.utc8Time') }}：</span>\r\n              <span class=\"font-mono\">{{ currentUTC8Time }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"text-xs text-gray-500\">\r\n            {{ $t('messages.schedule.currentTime.lastUpdate') }}：{{ lastUpdateTime }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-xl border border-gray-200 p-4 sm:p-6\">\r\n        <!-- 标题（桌面端显示）放入卡片 Header 区域 -->\r\n        <div class=\"hidden sm:flex items-center justify-between mb-4 pb-3 border-b border-gray-100\">\r\n          <h2 class=\"text-lg font-semibold\">{{ $t('messages.bookedCourses.title') }}</h2>\r\n        </div>\r\n\r\n        <!-- 加载状态 -->\r\n        <div v-if=\"loading\" class=\"grid gap-4\">\r\n          <div v-for=\"i in 3\" :key=\"i\" class=\"animate-pulse\">\r\n            <div class=\"h-20 bg-gray-100 rounded-lg\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 课程列表 -->\r\n        <div v-else class=\"space-y-0\">\r\n          <div v-if=\"courses.length === 0\" class=\"text-center py-10 text-gray-500\">\r\n            {{ $t('messages.bookedCourses.empty') }}\r\n          </div>\r\n          \r\n          <div v-else>\r\n            <div v-for=\"(course, index) in courses\" :key=\"course.id\" class=\"py-3\">\r\n              <div class=\"flex justify-between items-start gap-4\">\r\n                <!-- 课程基本信息 -->\r\n                <div class=\"flex items-start gap-4 min-w-0\">\r\n                  <div class=\"w-14 h-14 sm:w-16 sm:h-16 rounded-full overflow-hidden flex-shrink-0\">\r\n                    <img \r\n                      :src=\"course.teacherAvatarUrl || '/default-avatar.png'\" \r\n                      :alt=\"course.teacherNickname\"\r\n                      class=\"w-full h-full object-cover aspect-square\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div class=\"min-w-0\">\r\n                    <h3 class=\"text-base sm:text-lg font-medium mb-1 truncate\">{{ course.teacherNickname }}老师的{{ course.classType }}</h3>\r\n                    <div class=\"text-gray-600 text-sm space-y-1\">\r\n                      <p>{{ formatDate(course.scheduleDate) }} · {{ course.startTime }} - {{ course.endTime }}</p>\r\n                      <p class=\"text-xs text-gray-400\">{{ convertUTC8ToLocalTime(course.scheduleDate, course.startTime, course.endTime) }}</p>\r\n                      <p class=\"text-xs text-gray-400\">{{ getEnterTimeText(course) }}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 进入课堂按钮 -->\r\n                <UButton\r\n                  v-if=\"canEnterClass(course)\"\r\n                  color=\"primary\" \r\n                  variant=\"soft\" \r\n                  size=\"sm\"\r\n                  :loading=\"enteringClassId === course.id\"\r\n                  :disabled=\"enteringClassId === course.id\"\r\n                  class=\"!px-4 h-10 w-[84px] flex items-center justify-center whitespace-nowrap\"\r\n                  @click=\"getClassroomLink(course)\"\r\n                >\r\n                  {{ $t('messages.schedule.actions.enterClass') }}\r\n                </UButton>\r\n                <UButton\r\n                  v-else\r\n                  color=\"gray\" \r\n                  variant=\"soft\" \r\n                  size=\"sm\"\r\n                  disabled\r\n                  class=\"!px-4 h-10 w-[84px] flex items-center justify-center whitespace-nowrap\"\r\n                >\r\n                  {{ $t('messages.schedule.actions.wait') }}\r\n                </UButton>\r\n              </div>\r\n\r\n              <!-- 分隔线，最后一个项目不显示 -->\r\n              <div v-if=\"index !== courses.length - 1\" class=\"h-px bg-gray-200 mt-3\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div v-if=\"total > pageSize\" class=\"mt-6 flex justify-center\" dir=\"ltr\">\r\n          <UPagination\r\n            v-model=\"currentPage\"\r\n            :total=\"total\"\r\n            :page-size=\"pageSize\"\r\n            :ui=\"{ rounded: 'rounded-full' }\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { useAuthStore } from '~/stores/useAuthStore'\r\nimport { studentApi } from '~/api/student'\r\nimport type { StudentBookedCourse } from '~/types/api'\r\n// Avoid importing #ui/types in SFC to silence linter in some environments\r\nimport { trtcApi } from '~/api/trtc'\r\nimport { parseDateTime as parseDateTimeUtil, canEnterClass as canEnterClassUtil, getCountdownMinutes, getNow, convertUTC8ToLocalTime } from '~/utils/datetime'\r\n\r\nconst router = useRouter()\r\nconst authStore = useAuthStore()\r\nconst toast = useToast()\r\nconst { t } = useI18n()\r\n\r\n// 分页参数\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\n\r\n// 课程列表\r\nconst courses = ref<StudentBookedCourse[]>([])\r\nconst loading = ref(false)\r\nconst enteringClassId = ref<string | null>(null)\r\n\r\n// 当前时间显示相关\r\nconst currentLocalTime = ref('')\r\nconst currentUTC8Time = ref('')\r\nconst lastUpdateTime = ref('')\r\n\r\n// 状态更新定时器\r\nconst statusUpdateTimer = ref<ReturnType<typeof setInterval> | null>(null)\r\nconst forceUpdate = ref(0)\r\nconst lastFetchTime = ref(Date.now())\r\n\r\n// 获取课程列表\r\nconst fetchCourses = async () => {\r\n  if (!authStore.user?.student?.id) return\r\n  \r\n  loading.value = true\r\n  try {\r\n    const response = await studentApi.getStudentBookedCourses({\r\n      id: authStore.user.student.id,\r\n      startDate: '',\r\n      endDate: '',\r\n      page: currentPage.value,\r\n      pageSize: pageSize.value\r\n    })\r\n    \r\n    courses.value = response.list\r\n    total.value = response.totalCount\r\n  } catch (error) {\r\n    toast.add({\r\n      title: '获取课程列表失败',\r\n      description: '请稍后重试',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 监听分页\r\nwatch(currentPage, () => {\r\n  fetchCourses()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (date: string) => {\r\n  const d = new Date(date)\r\n  const month = d.getMonth() + 1\r\n  const day = d.getDate()\r\n  return `${month}月${day}日`\r\n}\r\n\r\n// 更新时间显示\r\nconst updateTimeDisplay = () => {\r\n  const now = new Date()\r\n  \r\n  // 本地时间\r\n  currentLocalTime.value = now.toLocaleString('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    second: '2-digit',\r\n    hour12: false\r\n  })\r\n  \r\n  // 东八区时间 - 使用 getNow() 函数\r\n  const utc8Now = getNow()\r\n  currentUTC8Time.value = utc8Now.format('YYYY-MM-DD HH:mm:ss')\r\n  \r\n  // 更新时间戳\r\n  lastUpdateTime.value = now.toLocaleTimeString('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    second: '2-digit',\r\n    hour12: false\r\n  })\r\n}\r\n\r\n// 时间处理辅助函数\r\nconst parseDateTime = (date: string, time: string) => {\r\n  return parseDateTimeUtil(date, time)\r\n}\r\n\r\n// 检查是否需要刷新数据\r\nconst checkNeedRefresh = () => {\r\n  const now = Date.now()\r\n  \r\n  // 检查是否有即将开始的课程（5分钟内）\r\n  const hasUpcomingCourse = courses.value.some((course: StudentBookedCourse) => {\r\n    if (course.status === 2) { // 已预约状态\r\n      const startTime = parseDateTime(course.scheduleDate, course.startTime)\r\n      const timeDiff = startTime.valueOf() - now\r\n      return timeDiff > 0 && timeDiff <= 5 * 60 * 1000 // 5分钟内有课程要开始\r\n    }\r\n    return false\r\n  })\r\n  \r\n  // 如果有即将开始的课程，使用30秒的刷新间隔；否则使用2分钟的刷新间隔\r\n  const refreshInterval = hasUpcomingCourse ? 30 * 1000 : 2 * 60 * 1000\r\n  const needRefresh = now - lastFetchTime.value > refreshInterval\r\n  \r\n  if (needRefresh) {\r\n    lastFetchTime.value = now\r\n    fetchCourses()\r\n  }\r\n}\r\n\r\n// 设置状态更新定时器\r\nconst setupStatusUpdateTimer = () => {\r\n  clearStatusUpdateTimer()\r\n  \r\n  // 立即执行一次更新\r\n  forceUpdate.value++\r\n  updateTimeDisplay()\r\n  checkNeedRefresh()\r\n  \r\n  // 设置定时更新 - 每秒更新时间显示，每60秒检查数据刷新\r\n  statusUpdateTimer.value = setInterval(() => {\r\n    forceUpdate.value++\r\n    updateTimeDisplay()\r\n    checkNeedRefresh()\r\n  }, 1000) // 每秒更新时间显示\r\n}\r\n\r\n// 清理定时器\r\nconst clearStatusUpdateTimer = () => {\r\n  if (statusUpdateTimer.value) {\r\n    clearInterval(statusUpdateTimer.value)\r\n    statusUpdateTimer.value = null\r\n  }\r\n}\r\n\r\n// 判断是否可以进入课堂\r\nconst canEnterClass = (course: StudentBookedCourse) => {\r\n  forceUpdate.value // 触发响应式更新\r\n\r\n  // 与student/book/[id]页面保持一致，只检查时间，不检查状态\r\n  return canEnterClassUtil(course.scheduleDate, course.startTime, course.endTime)\r\n}\r\n\r\n// 获取进入时间提示文本\r\nconst getEnterTimeText = (course: StudentBookedCourse) => {\r\n  forceUpdate.value // 触发响应式更新\r\n\r\n  // 与student/book/[id]页面保持一致，只检查时间，不检查状态\r\n  const diffMinutes = getCountdownMinutes(course.scheduleDate, course.startTime)\r\n\r\n  if (diffMinutes > 5) {\r\n    return t('messages.schedule.countdown.canEnterIn5Min')\r\n  } else if (diffMinutes >= -60) { // 课程开始前5分钟内或课程进行中1小时内\r\n    return t('messages.schedule.countdown.canEnterNow')\r\n  }\r\n  return ''\r\n}\r\n\r\n// 获取课堂链接\r\nconst getClassroomLink = async (course: StudentBookedCourse) => {\r\n  if (enteringClassId.value) return // 防止重复点击\r\n  enteringClassId.value = course.id\r\n\r\n  try {\r\n    // 跨浏览器兼容性：先获取TRTC签名，再同步打开弹窗\r\n    const config = useRuntimeConfig()\r\n\r\n    // 显示加载状态\r\n    toast.add({\r\n      title: t('messages.schedule.toast.preparingClass.title'),\r\n      description: t('messages.schedule.toast.preparingClass.description'),\r\n      color: 'blue',\r\n      timeout: 2000,\r\n      icon: 'i-heroicons-arrow-path'\r\n    })\r\n\r\n    // 先获取TRTC签名（避免Safari异步弹窗限制）\r\n    const { userSig, roomId } = await trtcApi.genSign({ courseId: course.id })\r\n\r\n    // 构建教室URL\r\n    const classroomUrl = new URL(`${config.public.rtcDomain}/#/class`)\r\n    classroomUrl.searchParams.set('userId', authStore.user?.id || '')\r\n    classroomUrl.searchParams.set('roomId', roomId.toString())\r\n    classroomUrl.searchParams.set('courseId', course.id)\r\n    classroomUrl.searchParams.set('userSig', userSig)\r\n\r\n    // Convert schedule times to timestamps\r\n    const beginTime = parseDateTime(course.scheduleDate, course.startTime).valueOf()\r\n    const endTime = parseDateTime(course.scheduleDate, course.endTime).valueOf()\r\n    classroomUrl.searchParams.set('classBeginTime', beginTime.toString())\r\n    classroomUrl.searchParams.set('classEndTime', endTime.toString())\r\n\r\n    // 同步打开弹窗窗口（所有浏览器兼容）\r\n    const newWindow = window.open(classroomUrl.toString(), '_blank')\r\n\r\n    // 检查弹窗是否被阻止\r\n    if (!newWindow || newWindow.closed) {\r\n      // 弹窗被阻止时，在本页面跳转\r\n      toast.add({\r\n        title: t('messages.schedule.toast.enteringClassSuccess.title'),\r\n        description: t('messages.schedule.toast.enteringClassSuccess.description'),\r\n        color: 'green',\r\n        timeout: 2000,\r\n        icon: 'i-heroicons-check-circle'\r\n      })\r\n\r\n      // 使用 nextTick 确保 toast 显示后再跳转\r\n      await nextTick()\r\n      await new Promise(resolve => setTimeout(resolve, 500))\r\n\r\n      // 在本页面跳转到教室\r\n      window.location.href = classroomUrl.toString()\r\n      return\r\n    }\r\n\r\n    // 弹窗成功打开\r\n    toast.add({\r\n      title: t('messages.schedule.toast.enteringClassPopup.title'),\r\n      description: t('messages.schedule.toast.enteringClassPopup.description'),\r\n      color: 'green',\r\n      timeout: 2000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n\r\n  } catch (error) {\r\n    toast.add({\r\n      title: t('messages.schedule.toast.enterClassError.title'),\r\n      description: t('messages.schedule.toast.enterClassError.description'),\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    enteringClassId.value = null\r\n  }\r\n}\r\n\r\n// 检查登录状态和权限\r\nconst checkAuth = async () => {\r\n  await authStore.checkAuth()\r\n  \r\n  // 检查是否已登录\r\n  if (!authStore.isAuthenticated) {\r\n    router.push('/join')\r\n    return\r\n  }\r\n  \r\n  // 检查是否是学生身份\r\n  if (!authStore.user?.student) {\r\n    toast.add({\r\n      title: '访问受限',\r\n      description: '只有学生才能访问此页面',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n    router.push('/')\r\n    return\r\n  }\r\n}\r\n\r\n// 更新 onMounted\r\nonMounted(async () => {\r\n  await checkAuth()\r\n  fetchCourses()\r\n  setupStatusUpdateTimer()\r\n})\r\n\r\n// 添加 onUnmounted\r\nonUnmounted(() => {\r\n  clearStatusUpdateTimer()\r\n})\r\n</script>"], "version": 3}