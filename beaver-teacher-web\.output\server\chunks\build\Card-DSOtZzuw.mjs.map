{"version": 3, "file": "Card-DSOtZzuw.mjs", "sources": ["../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/ui.config/layout/card.js", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/layout/Card.vue"], "sourcesContent": null, "names": ["_ssrRenderVNode", "_createVNode", "_resolveDynamicComponent", "_mergeProps", "_withCtx", "_push", "_parent", "_ssrRenderClass", "_createBlock", "_renderSlot", "_createCommentVNode"], "mappings": "", "x_google_ignoreList": [0, 1]}