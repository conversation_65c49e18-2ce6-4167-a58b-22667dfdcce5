import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { useSSRContext, defineComponent, ref, mergeProps, withCtx, createTextVNode, openBlock, createBlock, unref } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrRenderAttr } from 'vue/server-renderer';
import { B as useI18n, e as useRoute, f as useRouter, c as useToast, N as request } from './server.mjs';
import { s as studentApi } from './student-DtKAviut.mjs';
import { s as setInterval } from './interval-gl53xdpR.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const payApi = {
  //统一支付接口
  async createPay(data) {
    return await request({
      method: "POST",
      url: `/api/pay/create`,
      data
    });
  },
  //查询支付状态  /api/pay/status form-data入参：orderNo (string)
  //返回： 0-待支付，1-支付成功，2-支付失败
  async getPayStatus(params) {
    return await request({
      method: "GET",
      url: `/api/pay/status`,
      params
    });
  }
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const paymentMethods = [
      {
        id: "alipay",
        name: "",
        icon: {
          small: "/images/alipay/58_168.png",
          medium: "/images/alipay/80_232.png",
          large: "/images/alipay/120_348.png",
          xlarge: "/images/alipay/300_868.png"
        },
        recommended: true
      }
      /*   {
          id: 'wxpay',
          name: '',
          icon: {
            small: '/images/wxpay/wxpay.png',
            medium: '/images/wxpay/wxpay.png',
            large: '/images/wxpay/wxpay.png',
            xlarge: '/images/wxpay/wxpay.png'
          },
          recommended: false
        } */
    ];
    const selectedMethod = ref(paymentMethods[0].id);
    const route = useRoute();
    const router = useRouter();
    const orderAmount = ref("0.00");
    const orderTitle = ref(route.query.title || "\u8BFE\u5361\u8D2D\u4E70");
    const cardId = ref(route.query.cardId);
    const redirect = ref(route.query.redirect);
    const teacherName = ref(route.query.teacherName);
    const courseCardInfo = ref(null);
    const priceLoading = ref(false);
    const priceError = ref("");
    const toast = useToast();
    const loading = ref(false);
    const isComponentMounted = ref(true);
    let globalTimer = null;
    const fetchCourseCardPrice = async () => {
      if (!cardId.value) {
        priceError.value = "\u8BFE\u7A0B\u5361ID\u4E0D\u80FD\u4E3A\u7A7A";
        return;
      }
      try {
        priceLoading.value = true;
        priceError.value = "";
        const response = await studentApi.getCourseCardPrice(cardId.value);
        courseCardInfo.value = response;
        if (!response.purchasable) {
          priceError.value = response.unavailableReason || "\u8BE5\u8BFE\u7A0B\u5361\u4E0D\u53EF\u8D2D\u4E70";
          toast.add({
            title: "\u8BFE\u7A0B\u5361\u4E0D\u53EF\u8D2D\u4E70",
            description: response.unavailableReason || "\u8BE5\u8BFE\u7A0B\u5361\u4E0D\u53EF\u8D2D\u4E70",
            color: "red",
            timeout: 5e3
          });
          return;
        }
        orderAmount.value = response.price.toString();
        orderTitle.value = response.cardName;
      } catch (error) {
        console.error("\u83B7\u53D6\u8BFE\u7A0B\u5361\u4EF7\u683C\u5931\u8D25:", error);
        priceError.value = "\u83B7\u53D6\u8BFE\u7A0B\u5361\u4EF7\u683C\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5";
        toast.add({
          title: "\u83B7\u53D6\u4EF7\u683C\u5931\u8D25",
          description: "\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5",
          color: "red",
          timeout: 5e3
        });
      } finally {
        priceLoading.value = false;
      }
    };
    const checkPayStatus = async (orderNo) => {
      let retryCount = 0;
      const maxRetries = 60;
      const checkStatus = async () => {
        if (!isComponentMounted.value) {
          if (globalTimer) {
            clearInterval(globalTimer);
            globalTimer = null;
          }
          return;
        }
        try {
          const status = await payApi.getPayStatus({ orderNo });
          if (!isComponentMounted.value) {
            if (globalTimer) {
              clearInterval(globalTimer);
              globalTimer = null;
            }
            return;
          }
          switch (status) {
            case 1:
              toast.add({
                id: "payment-success",
                title: "\u652F\u4ED8\u6210\u529F",
                description: "\u6B63\u5728\u8DF3\u8F6C\u5230\u652F\u4ED8\u7ED3\u679C\u9875\u9762...",
                color: "green",
                timeout: 3e3,
                icon: "i-heroicons-check-circle"
              });
              if (globalTimer) {
                clearInterval(globalTimer);
                globalTimer = null;
              }
              router.push({
                path: "/student/payment/callback",
                query: {
                  orderNo,
                  amount: orderAmount.value,
                  cardName: orderTitle.value,
                  redirect: redirect.value,
                  teacherName: teacherName.value
                }
              });
              break;
            case 2:
              toast.add({
                id: "payment-failed",
                title: "\u652F\u4ED8\u5931\u8D25",
                description: "\u8BF7\u91CD\u65B0\u5C1D\u8BD5\u652F\u4ED8",
                color: "red",
                timeout: 3e3,
                icon: "i-heroicons-x-circle"
              });
              if (globalTimer) {
                clearInterval(globalTimer);
                globalTimer = null;
              }
              loading.value = false;
              break;
            case 0:
              retryCount++;
              if (retryCount >= maxRetries) {
                toast.add({
                  id: "payment-timeout",
                  title: "\u652F\u4ED8\u8D85\u65F6",
                  description: "\u8BF7\u91CD\u65B0\u53D1\u8D77\u652F\u4ED8",
                  color: "yellow",
                  timeout: 3e3,
                  icon: "i-heroicons-clock"
                });
                if (globalTimer) {
                  clearInterval(globalTimer);
                  globalTimer = null;
                }
                loading.value = false;
              }
              break;
          }
        } catch (error) {
          if (!isComponentMounted.value) {
            if (globalTimer) {
              clearInterval(globalTimer);
              globalTimer = null;
            }
            return;
          }
          console.error("\u67E5\u8BE2\u652F\u4ED8\u72B6\u6001\u5931\u8D25:", error);
          if (globalTimer) {
            clearInterval(globalTimer);
            globalTimer = null;
          }
          loading.value = false;
          toast.add({
            id: "query-error",
            title: "\u67E5\u8BE2\u5931\u8D25",
            description: "\u652F\u4ED8\u72B6\u6001\u67E5\u8BE2\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5",
            color: "red",
            timeout: 3e3,
            icon: "i-heroicons-exclamation-circle"
          });
        }
      };
      if (globalTimer) {
        clearInterval(globalTimer);
        globalTimer = null;
      }
      globalTimer = setInterval();
      checkStatus();
    };
    const handlePayment = async () => {
      if (loading.value)
        return;
      if (!courseCardInfo.value) {
        toast.add({
          title: "\u652F\u4ED8\u5931\u8D25",
          description: "\u8BFE\u7A0B\u5361\u4FE1\u606F\u672A\u52A0\u8F7D\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5",
          color: "red",
          timeout: 3e3
        });
        return;
      }
      if (!courseCardInfo.value.purchasable) {
        toast.add({
          title: "\u65E0\u6CD5\u652F\u4ED8",
          description: courseCardInfo.value.unavailableReason || "\u8BE5\u8BFE\u7A0B\u5361\u4E0D\u53EF\u8D2D\u4E70",
          color: "red",
          timeout: 3e3
        });
        return;
      }
      loading.value = true;
      try {
        const payParams = {
          businessId: cardId.value,
          paymentMethod: selectedMethod.value === "alipay" ? 1 : 2,
          paymentChannel: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test((void 0).userAgent) ? 2 : 1
        };
        const response = await payApi.createPay(payParams);
        const paymentWindow = (void 0).open(response.formHtml, "_blank");
        if (!paymentWindow) {
          throw new Error("\u8BF7\u5141\u8BB8\u5F39\u51FA\u7A97\u53E3\u4EE5\u7EE7\u7EED\u652F\u4ED8");
        }
        checkPayStatus(response.orderNo);
        toast.add({
          id: "payment-waiting",
          title: "\u8BF7\u5728\u65B0\u7A97\u53E3\u5B8C\u6210\u652F\u4ED8",
          description: "\u652F\u4ED8\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u8DF3\u8F6C",
          color: "blue",
          timeout: 5e3,
          icon: "i-heroicons-information-circle"
        });
      } catch (error) {
        console.error("\u652F\u4ED8\u5931\u8D25:", error);
        toast.add({
          id: "payment-error",
          title: "\u652F\u4ED8\u5931\u8D25",
          description: error instanceof Error ? error.message : "\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
        loading.value = false;
      }
    };
    const handleBack = () => {
      router.push({
        path: "/student/course-cards/buy",
        query: {
          redirect: redirect.value,
          teacherName: teacherName.value
        }
      });
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      const _component_UButton = __nuxt_component_2;
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex flex-col min-h-screen bg-gray-50" }, _attrs))} data-v-f45cdfec><div class="bg-white p-6" data-v-f45cdfec><div class="flex items-center mb-6" data-v-f45cdfec>`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        onClick: handleBack
      }, null, _parent));
      _push(`<h1 class="text-xl font-bold ml-2" data-v-f45cdfec>\u786E\u8BA4\u652F\u4ED8</h1></div><div class="text-center" data-v-f45cdfec><div class="text-gray-600 mb-2" data-v-f45cdfec>\u652F\u4ED8\u91D1\u989D</div>`);
      if (priceLoading.value) {
        _push(`<div class="text-3xl font-bold flex items-center justify-center text-gray-400" data-v-f45cdfec>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "w-8 h-8 animate-spin mr-2"
        }, null, _parent));
        _push(`<span data-v-f45cdfec>\u52A0\u8F7D\u4E2D...</span></div>`);
      } else if (priceError.value) {
        _push(`<div class="text-center" data-v-f45cdfec><div class="text-red-500 text-lg mb-2" data-v-f45cdfec>${ssrInterpolate(priceError.value)}</div>`);
        _push(ssrRenderComponent(_component_UButton, {
          variant: "outline",
          color: "red",
          size: "sm",
          onClick: fetchCourseCardPrice
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u91CD\u65B0\u52A0\u8F7D `);
            } else {
              return [
                createTextVNode(" \u91CD\u65B0\u52A0\u8F7D ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<div class="text-3xl font-bold flex items-center justify-center text-orange-500" data-v-f45cdfec><span class="text-2xl" data-v-f45cdfec>\xA5</span><span data-v-f45cdfec>${ssrInterpolate(orderAmount.value)}</span></div>`);
      }
      _push(`<div class="text-gray-500 text-sm mt-2" data-v-f45cdfec>${ssrInterpolate(orderTitle.value)}</div></div></div><div class="flex-1 p-6 overflow-y-auto" data-v-f45cdfec><h3 class="text-base font-medium mb-4" data-v-f45cdfec>\u8BF7\u9009\u62E9\u652F\u4ED8\u65B9\u5F0F</h3><div class="bg-white rounded-lg divide-y divide-gray-100" data-v-f45cdfec><!--[-->`);
      ssrRenderList(paymentMethods, (method) => {
        _push(`<div class="flex items-center p-4 cursor-pointer hover:bg-gray-50" data-v-f45cdfec><div class="w-6 h-6 flex items-center justify-center" data-v-f45cdfec><div class="${ssrRenderClass([selectedMethod.value === method.id ? "border-blue-500 bg-blue-500" : "border-gray-300", "w-4 h-4 rounded-full border-2"])}" data-v-f45cdfec>`);
        if (selectedMethod.value === method.id) {
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-check",
            class: "w-3 h-3 text-white"
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div><div class="flex items-center flex-1 ml-3" data-v-f45cdfec><img${ssrRenderAttr("src", method.icon.medium)}${ssrRenderAttr("alt", method.name)} class="h-8 mr-2 sm:hidden" data-v-f45cdfec><img${ssrRenderAttr("src", method.icon.large)}${ssrRenderAttr("alt", method.name)} class="h-10 mr-2 hidden sm:block" data-v-f45cdfec><span class="text-gray-900" data-v-f45cdfec>${ssrInterpolate(method.name)}</span>`);
        if (method.recommended) {
          _push(`<span class="ml-2 text-xs text-orange-500 border border-orange-500 rounded px-1" data-v-f45cdfec> \u63A8\u8350 </span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      });
      _push(`<!--]--></div></div><div class="sticky bottom-0 p-4 bg-white border-t mt-auto" data-v-f45cdfec>`);
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        color: selectedMethod.value === "wxpay" ? "green" : "orange",
        size: "lg",
        loading: loading.value,
        disabled: priceLoading.value || !!priceError.value || !((_a = courseCardInfo.value) == null ? void 0 : _a.purchasable),
        onClick: handlePayment
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          var _a2, _b;
          if (_push2) {
            if (priceLoading.value) {
              _push2(`<span data-v-f45cdfec${_scopeId}>\u52A0\u8F7D\u4E2D...</span>`);
            } else if (priceError.value) {
              _push2(`<span data-v-f45cdfec${_scopeId}>\u4EF7\u683C\u52A0\u8F7D\u5931\u8D25</span>`);
            } else if (!((_a2 = courseCardInfo.value) == null ? void 0 : _a2.purchasable)) {
              _push2(`<span data-v-f45cdfec${_scopeId}>\u4E0D\u53EF\u8D2D\u4E70</span>`);
            } else {
              _push2(`<span data-v-f45cdfec${_scopeId}>\u7ACB\u5373\u652F\u4ED8</span>`);
            }
          } else {
            return [
              priceLoading.value ? (openBlock(), createBlock("span", { key: 0 }, "\u52A0\u8F7D\u4E2D...")) : priceError.value ? (openBlock(), createBlock("span", { key: 1 }, "\u4EF7\u683C\u52A0\u8F7D\u5931\u8D25")) : !((_b = courseCardInfo.value) == null ? void 0 : _b.purchasable) ? (openBlock(), createBlock("span", { key: 2 }, "\u4E0D\u53EF\u8D2D\u4E70")) : (openBlock(), createBlock("span", { key: 3 }, "\u7ACB\u5373\u652F\u4ED8"))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<div class="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg glass" data-v-f45cdfec><div class="flex items-start space-x-2" data-v-f45cdfec>`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-information-circle",
        class: "w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"
      }, null, _parent));
      _push(`<div class="text-xs text-green-700 leading-relaxed" data-v-f45cdfec>${ssrInterpolate(unref(t)("messages.payment.amountNotice.description"))}</div></div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/payment/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-f45cdfec"]]);

export { index as default };
//# sourceMappingURL=index-DVXGUv7L.mjs.map
