{"version": 3, "file": "_id_-DeNnG8L9.js", "sources": ["../../../../src/pages/student/book/[id].vue"], "sourcesContent": ["<template>\r\n  <!-- Mobile Header with back -->\r\n  <div class=\"sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center\">\r\n    <div class=\"absolute left-2\">\r\n      <UButton icon=\"i-heroicons-arrow-left\" variant=\"ghost\" @click=\"router.back()\" />\r\n    </div>\r\n    <h1 class=\"text-base font-semibold\">预约 {{ route.query.teacherName || '' }} 老师课程</h1>\r\n  </div>\r\n  <div class=\"container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area\">\r\n    <div class=\"max-w-4xl mx-auto w-full py-4 sm:py-6\">\r\n    <ConfirmationDialog\r\n      :visible=\"showConfirmDialog\"\r\n      :title=\"confirmDialogTitle\"\r\n      :message=\"confirmDialogMessage\"\r\n      @confirm=\"selectedCourse?.status === TeacherScheduleStatus.Available ? confirmBooking() : confirmCancellation()\"\r\n      @cancel=\"cancelDialog\"\r\n    />\r\n    \r\n    <!-- 昵称设置弹窗 -->\r\n    <UModal \r\n      v-model=\"showNicknameDialog\"\r\n      :closeOnClickOutside=\"false\"\r\n      :preventClose=\"true\"\r\n    >\r\n      <div class=\"p-4\">\r\n        <h3 class=\"text-lg font-medium mb-4\">为方便与教师交流，请设置英文名：</h3>\r\n        <UInput\r\n          v-model=\"nickname\"\r\n          placeholder=\"请输入英文名\"\r\n          class=\"mb-4\"\r\n        />\r\n        <div class=\"flex justify-end gap-2\">\r\n          <UButton\r\n            color=\"gray\"\r\n            variant=\"soft\"\r\n            @click=\"cancelNicknameDialog\"\r\n          >\r\n            取消\r\n          </UButton>\r\n          <UButton\r\n            color=\"primary\"\r\n            :loading=\"submittingNickname\"\r\n            @click=\"submitNickname\"\r\n          >\r\n            确定\r\n          </UButton>\r\n        </div>\r\n      </div>\r\n    </UModal>\r\n    \r\n    <!-- 时间信息独立卡片 -->\r\n    <div class=\"bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center gap-6 sm:gap-8\">\r\n          <div class=\"text-sm text-gray-600\">\r\n            <span class=\"font-medium\">{{ $t('messages.schedule.currentTime.localTime') }}：</span>\r\n            <span class=\"font-mono\">{{ currentLocalTime }}</span>\r\n          </div>\r\n          <div class=\"text-sm text-gray-600\">\r\n            <span class=\"font-medium\">{{ $t('messages.schedule.currentTime.utc8Time') }}：</span>\r\n            <span class=\"font-mono\">{{ currentUTC8Time }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"text-xs text-gray-500\">\r\n          {{ $t('messages.schedule.currentTime.lastUpdate') }}：{{ lastUpdateTime }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"bg-white rounded-xl border border-gray-200 p-4 sm:p-6\">\r\n      <div class=\"hidden sm:flex items-center gap-2 mb-4 pb-3 border-b border-gray-100\">\r\n        <UButton icon=\"i-heroicons-arrow-left\" variant=\"ghost\" color=\"gray\" size=\"sm\" @click=\"router.back()\" />\r\n        <h2 class=\"text-lg font-semibold\">预约 {{ route.query.teacherName || '' }} 老师课程</h2>\r\n      </div>\r\n\r\n    <!-- 日期选择器 -->\r\n    <div class=\"mb-4 sm:mb-6\">\r\n      <!-- 8 个 Tab：7 天 + 全部。移动端 2 行 x 4 列，桌面端 1 行 8 列 -->\r\n      <div class=\"grid grid-cols-4 sm:grid-cols-8 gap-2\">\r\n        <!-- ALL 放在最前，便于发现与重置选择 -->\r\n        <UButton\r\n          :color=\"!selectedDate ? 'primary' : 'gray'\"\r\n          :variant=\"!selectedDate ? 'solid' : 'soft'\"\r\n          class=\"w-full h-12 sm:h-12 justify-center rounded-lg\"\r\n          @click=\"clearDateFilter\"\r\n        >\r\n          <div class=\"flex flex-col items-center leading-tight\">\r\n            <span class=\"text-[13px]\">{{ $t('messages.date.filter.all') }}</span>\r\n            <span class=\"text-[11px]\">{{ $t('messages.date.filter.noLimit') }}</span>\r\n          </div>\r\n        </UButton>\r\n\r\n        <UButton\r\n          v-for=\"date in weekDates\"\r\n          :key=\"date.value\"\r\n          :color=\"selectedDate === date.value ? 'primary' : 'gray'\"\r\n          :variant=\"selectedDate === date.value ? 'solid' : 'soft'\"\r\n          class=\"w-full h-12 sm:h-12 justify-center rounded-lg\"\r\n          @click=\"selectDate(date.value)\"\r\n        >\r\n          <div class=\"flex flex-col items-center leading-tight\">\r\n            <span class=\"text-[13px]\">{{ date.dayName }}</span>\r\n            <span class=\"text-[11px]\">{{ date.display }}</span>\r\n          </div>\r\n        </UButton>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"flex justify-center py-12\">\r\n      <UIcon name=\"i-heroicons-arrow-path\" class=\"animate-spin text-3xl text-gray-400\" />\r\n    </div>\r\n\r\n    <!-- 课程列表 -->\r\n    <div v-else class=\"space-y-0\">\r\n      <div v-if=\"courses.length === 0\" class=\"text-center py-10 text-gray-500\">\r\n        暂无可预约的课程\r\n      </div>\r\n      \r\n      <div v-else>\r\n        <div v-for=\"(course, index) in courses\" :key=\"course.id\" class=\"py-3\">\r\n          <div class=\"flex justify-between items-start gap-4\">\r\n            <div>\r\n              <div class=\"text-base sm:text-lg font-medium\">\r\n                {{ formatDate(course.scheduleDate) }} {{ course.startTime }} - {{ course.endTime }}\r\n              </div>\r\n              <div class=\"text-sm text-gray-500\">\r\n                {{ convertUTC8ToLocalTime(course.scheduleDate, course.startTime, course.endTime) }}\r\n              </div>\r\n              <div class=\"text-sm text-gray-500 mt-1\">\r\n                {{ course.classType }}\r\n                <span v-if=\"course.classType === '一对多课程'\" class=\"text-xs text-gray-500\">\r\n                  ({{ course.currentStudents || 0 }}/{{ course.maxStudents }})\r\n                </span>\r\n                <span v-if=\"course.remark\" class=\"ml-2\">({{ course.remark }})</span>\r\n              </div>\r\n              <div class=\"text-sm mt-1 flex items-center gap-2\">\r\n                <span class=\"text-gray-400\">\r\n                  {{ getStatusDescription(course) }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 状态和操作按钮 -->\r\n            <div class=\"flex items-center gap-4\">\r\n              <UButton\r\n                v-if=\"course.status === TeacherScheduleStatus.Available && canBookCourse(course)\"\r\n                color=\"primary\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n                @click=\"handleBook(course)\"\r\n              >\r\n                预约\r\n              </UButton>\r\n\r\n              <UButton\r\n                v-if=\"course.status === TeacherScheduleStatus.Available && !canBookCourse(course)\"\r\n                color=\"gray\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n              >\r\n                不可预约\r\n              </UButton>\r\n              \r\n              <UButton\r\n                v-if=\"course.status === TeacherScheduleStatus.Unavailable\"\r\n                color=\"gray\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                disabled\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n              >\r\n                不可预约\r\n              </UButton>\r\n              <UButton\r\n                v-else-if=\"course.status === TeacherScheduleStatus.Booked && course.studentId === studentId && canCancelCourse(course)\"\r\n                color=\"blue\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n                @click=\"handleCancel(course)\"\r\n              >\r\n                已预约<br>\r\n                点击取消\r\n              </UButton>\r\n\r\n              <UButton\r\n                v-else-if=\"(course.status === TeacherScheduleStatus.Booked || course.status === TeacherScheduleStatus.Started) && course.studentId === studentId && canEnterClassUtil(course.scheduleDate, course.startTime, course.endTime)\"\r\n                color=\"blue\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n                :loading=\"enteringClassId === course.id\"\r\n                :disabled=\"enteringClassId === course.id\"\r\n                @click=\"handleEnterClass(course)\"\r\n              >\r\n                <template #leading>\r\n                  <UIcon v-if=\"enteringClassId === course.id\" name=\"i-heroicons-arrow-path\" class=\"animate-spin\" />\r\n                </template>\r\n                进入课堂\r\n              </UButton>\r\n\r\n              <UButton\r\n                v-else-if=\"course.status === TeacherScheduleStatus.Booked && course.studentId === studentId && !canCancelCourse(course)\"\r\n                color=\"gray\"\r\n                variant=\"soft\"\r\n                size=\"sm\"\r\n                class=\"!px-4 h-10 w-[100px] flex items-center justify-center whitespace-nowrap\"\r\n                @click=\"canNotEnterClassTips(course)\"\r\n              >\r\n                进入课堂\r\n              </UButton>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 分隔线，最后一个项目不显示 -->\r\n          <div v-if=\"index !== courses.length - 1\" class=\"h-px bg-gray-200 mt-3\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div v-if=\"total > pageSize\" class=\"mt-6 flex justify-center\" dir=\"ltr\">\r\n      <UPagination\r\n        v-model=\"currentPage\"\r\n        :total=\"total\"\r\n        :page-size=\"pageSize\"\r\n        :ui=\"{ rounded: 'rounded-full' }\"\r\n      />\r\n    </div>\r\n    </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\n  \r\n  import { useAuthStore } from '~/stores/useAuthStore'\r\nimport { studentApi } from '~/api/student'\r\nimport type { TeacherCourse } from '~/types/api'\r\nimport { TeacherScheduleStatus } from '~/types/api.d'\r\nimport { trtcApi } from '~/api/trtc'\r\nimport { \r\n  formatDate as formatDateUtil, \r\n  parseDateTime as parseDateTimeUtil, \r\n  canBookCourse as canBookCourseUtil, \r\n  canCancelCourse as canCancelCourseUtil, \r\n  canEnterClass as canEnterClassUtil, \r\n  generateDateRange,\r\n  getNow,\r\n  convertUTC8ToLocalTime\r\n} from '~/utils/datetime'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst { t } = useI18n()\r\nconst route = useRoute()\r\nconst router = useRouter()\r\nconst authStore = useAuthStore()\r\nconst toast = useToast()\r\n\r\nconst teacherId = route.params.id as string\r\nconst studentId = computed(() => authStore.user?.student?.id)\r\n// 分页参数\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\n\r\n// 课程列表\r\nconst courses = ref<TeacherCourse[]>([])\r\nconst loading = ref(false)\r\n\r\n// 日期筛选\r\nconst selectedDate = ref(route.query.date as string || '')\r\n\r\n// 生成最近一周的日期\r\nconst weekDates = computed(() => {\r\n  return generateDateRange(7, true)\r\n})\r\n\r\n// 选择日期\r\nconst selectDate = (date: string) => {\r\n  selectedDate.value = date\r\n  currentPage.value = 1\r\n  fetchCourses()\r\n}\r\n\r\n// 清除日期筛选\r\nconst clearDateFilter = () => {\r\n  selectedDate.value = ''\r\n  currentPage.value = 1\r\n  fetchCourses()\r\n}\r\n\r\n// Dialog state\r\nconst showConfirmDialog = ref(false)\r\nconst confirmDialogTitle = ref('')\r\nconst confirmDialogMessage = ref('')\r\nconst selectedCourse = ref<TeacherCourse | null>(null)\r\n\r\n// 添加 enteringClassId 状态\r\nconst enteringClassId = ref<string | null>(null)\r\n\r\n// 当前时间显示相关\r\nconst currentLocalTime = ref('')\r\nconst currentUTC8Time = ref('')\r\nconst lastUpdateTime = ref('')\r\n\r\n// 状态更新定时器\r\nconst statusUpdateTimer = ref<ReturnType<typeof setInterval> | null>(null)\r\nconst forceUpdate = ref(0)\r\nconst lastFetchTime = ref(Date.now())\r\n\r\n// 昵称设置相关\r\nconst showNicknameDialog = ref(false)\r\nconst nickname = ref('')\r\nconst submittingNickname = ref(false)\r\n\r\n// 更新时间显示\r\nconst updateTimeDisplay = () => {\r\n  const now = new Date()\r\n  \r\n  // 本地时间\r\n  currentLocalTime.value = now.toLocaleString('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    second: '2-digit',\r\n    hour12: false\r\n  })\r\n  \r\n  // 东八区时间 - 使用 getNow() 函数\r\n  const utc8Now = getNow()\r\n  currentUTC8Time.value = utc8Now.format('YYYY-MM-DD HH:mm:ss')\r\n  \r\n  // 更新时间戳\r\n  lastUpdateTime.value = now.toLocaleTimeString('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    second: '2-digit',\r\n    hour12: false\r\n  })\r\n}\r\n\r\n// 检查是否需要刷新数据\r\nconst checkNeedRefresh = () => {\r\n  const now = Date.now()\r\n  \r\n  // 检查是否有即将开始的课程（5分钟内）\r\n  const hasUpcomingCourse = courses.value.some((course: TeacherCourse) => {\r\n    if (course.status === TeacherScheduleStatus.Booked && course.studentId === studentId.value) {\r\n      const startTime = parseDateTimeUtil(course.scheduleDate, course.startTime)\r\n      const timeDiff = startTime.valueOf() - now\r\n      return timeDiff > 0 && timeDiff <= 5 * 60 * 1000 // 5分钟内有课程要开始\r\n    }\r\n    return false\r\n  })\r\n  \r\n  // 如果有即将开始的课程，使用30秒的刷新间隔；否则使用2分钟的刷新间隔\r\n  const refreshInterval = hasUpcomingCourse ? 30 * 1000 : 2 * 60 * 1000\r\n  const needRefresh = now - lastFetchTime.value > refreshInterval\r\n  \r\n  if (needRefresh) {\r\n    lastFetchTime.value = now\r\n    fetchCourses()\r\n  }\r\n}\r\n\r\n// 设置状态更新定时器\r\nconst setupStatusUpdateTimer = () => {\r\n  clearStatusUpdateTimer()\r\n  \r\n  // 立即执行一次更新\r\n  forceUpdate.value++\r\n  updateTimeDisplay()\r\n  checkNeedRefresh()\r\n  \r\n  // 设置定时更新 - 每秒更新时间显示，每60秒检查数据刷新\r\n  statusUpdateTimer.value = setInterval(() => {\r\n    forceUpdate.value++\r\n    updateTimeDisplay()\r\n    checkNeedRefresh()\r\n  }, 1000) // 每秒更新时间显示\r\n}\r\n\r\n// 清理定时器\r\nconst clearStatusUpdateTimer = () => {\r\n  if (statusUpdateTimer.value) {\r\n    clearInterval(statusUpdateTimer.value)\r\n    statusUpdateTimer.value = null\r\n  }\r\n}\r\n\r\n// 获取课程列表\r\nconst fetchCourses = async () => {\r\n  loading.value = true\r\n  try {\r\n    // 修改API调用，startDate和endDate使用相同的日期\r\n    const response = await studentApi.getTeacherCourseList({\r\n      teacherId,\r\n      startDate: selectedDate.value,\r\n      endDate: selectedDate.value, // 使用相同的日期\r\n      page: currentPage.value,\r\n      pageSize: pageSize.value\r\n    })\r\n    \r\n    courses.value = response.list || []\r\n    total.value = response.totalCount || 0\r\n  } catch (error) {\r\n    toast.add({\r\n      title: '获取课程列表失败',\r\n      description: '请稍后重试',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 格式化日期\r\nconst formatDate = (date: string) => {\r\n  return formatDateUtil(date, 'MM-DD')\r\n}\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status: TeacherScheduleStatus) => {\r\n  const statusMap = {\r\n    [TeacherScheduleStatus.Cancelled]: '已取消',\r\n    [TeacherScheduleStatus.Available]: '可预约',\r\n    [TeacherScheduleStatus.Booked]: '已预约',\r\n    [TeacherScheduleStatus.Completed]: '已完成',\r\n    [TeacherScheduleStatus.Unavailable]: '不可预约',\r\n    [TeacherScheduleStatus.Started]: '已开始'\r\n  }\r\n  return statusMap[status]\r\n}\r\n\r\n// 时间处理辅助函数\r\nconst parseDateTime = (date: string, time: string) => {\r\n  return parseDateTimeUtil(date, time)\r\n}\r\n\r\n// 获取状态颜色\r\n  const getStatusColor = (status: TeacherScheduleStatus): any => {\r\n  const colorMap: Record<TeacherScheduleStatus, 'gray' | 'green' | 'blue'> = {\r\n    [TeacherScheduleStatus.Cancelled]: 'gray',\r\n    [TeacherScheduleStatus.Available]: 'green',\r\n    [TeacherScheduleStatus.Booked]: 'blue',\r\n    [TeacherScheduleStatus.Completed]: 'gray',\r\n    [TeacherScheduleStatus.Unavailable]: 'gray',\r\n    [TeacherScheduleStatus.Started]: 'blue'\r\n  }\r\n  return colorMap[status]\r\n}\r\n\r\n// 处理预约\r\nconst handleBook = (course: TeacherCourse) => {\r\n  selectedCourse.value = course\r\n  confirmDialogTitle.value = '确认预约'\r\n  confirmDialogMessage.value = `确定要预约 ${formatDate(course.scheduleDate)} ${course.startTime} - ${course.endTime} 的课程吗？`\r\n  showConfirmDialog.value = true\r\n}\r\n\r\nconst confirmBooking = async () => {\r\n  if (!selectedCourse.value) return\r\n  \r\n  try {\r\n    await studentApi.bookCourse({\r\n      teacherId: selectedCourse.value.teacherId,\r\n      courseId: selectedCourse.value.id\r\n    })\r\n    \r\n    // 检查用户是否设置了昵称\r\n    if (!authStore.user?.student?.nickname || authStore.user?.student?.nickname==='未设置昵称') {\r\n      await useAuthStore().fetchUserInfo()\r\n      if(authStore.user?.student?.nickname==='未设置昵称'){\r\n        showNicknameDialog.value = true\r\n        return\r\n      }\r\n    }\r\n    \r\n    toast.add({\r\n      title: '预约成功',\r\n      description: '课程已成功预约',\r\n      color: 'green',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n    \r\n    fetchCourses()\r\n  } catch (error: any) {\r\n    toast.add({\r\n      title: '预约失败',\r\n      description: error?.message || '请稍后重试',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n    //缺少对一课卡\r\n    if(error?.code === 100005){\r\n      router.push({\r\n        path: '/student/course-cards/buy',\r\n        query: {\r\n          redirect: '/student/book/' + teacherId,\r\n          teacherName: route.query.teacherName as string,\r\n          type: '1' // 一对一课卡\r\n        }\r\n      })\r\n    }\r\n    //缺少小组课课卡\r\n    if(error?.code === 100006){\r\n      router.push({\r\n        path: '/student/course-cards/buy',\r\n        query: {\r\n          redirect: '/student/book/' + teacherId,\r\n          teacherName: route.query.teacherName as string,\r\n          type: '2' // 小组课课卡\r\n        }\r\n      })\r\n    }\r\n  } finally {\r\n    showConfirmDialog.value = false\r\n    selectedCourse.value = null\r\n  }\r\n}\r\n\r\nconst handleCancel = (course: TeacherCourse) => {\r\n  // 检查是否在可取消时间范围内（提前1小时）\r\n  if (!canCancelCourse(course)) {\r\n    toast.add({\r\n      title: '无法取消预约',\r\n      description: '课程开始前1小时内无法取消预约',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n    return\r\n  }\r\n\r\n  selectedCourse.value = course\r\n  confirmDialogTitle.value = '确认取消预约'\r\n  confirmDialogMessage.value = `确定要取消 ${formatDate(course.scheduleDate)} ${course.startTime} - ${course.endTime} 的课程预约吗？`\r\n  showConfirmDialog.value = true\r\n}\r\n\r\nconst confirmCancellation = async () => {\r\n  if (!selectedCourse.value) return\r\n  \r\n  try {\r\n    await studentApi.cancelCourse({\r\n      courseId: selectedCourse.value.id\r\n    })\r\n    \r\n    toast.add({\r\n      title: '取消成功',\r\n      description: '课程预约已取消',\r\n      color: 'green',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n    \r\n    // 刷新课程列表\r\n    fetchCourses()\r\n  } catch (error: any) {\r\n    toast.add({\r\n      title: '取消失败',\r\n      description: error?.message || '请稍后重试',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    showConfirmDialog.value = false\r\n    selectedCourse.value = null\r\n  }\r\n}\r\n\r\nconst cancelDialog = () => {\r\n  showConfirmDialog.value = false\r\n  selectedCourse.value = null\r\n}\r\n\r\nconst canNotEnterClassTips = (course: TeacherCourse) => {\r\n  toast.add({\r\n    title: '无法进入课堂',\r\n    description: '课程开始前5分钟到结束前可以进入课堂',\r\n    color: 'red',\r\n    timeout: 5000,\r\n    icon: 'i-heroicons-x-circle'\r\n  })\r\n}\r\n\r\n// 处理进入课堂\r\nconst handleEnterClass = async (course: TeacherCourse) => {\r\n  if (enteringClassId.value) return // 防止重复点击\r\n  enteringClassId.value = course.id\r\n\r\n  try {\r\n    // 跨浏览器兼容性：先获取TRTC签名，再同步打开弹窗\r\n    const config = useRuntimeConfig()\r\n\r\n    // 显示加载状态\r\n    toast.add({\r\n      title: t('messages.schedule.toast.preparingClass.title'),\r\n      description: t('messages.schedule.toast.preparingClass.description'),\r\n      color: 'blue',\r\n      timeout: 2000,\r\n      icon: 'i-heroicons-arrow-path'\r\n    })\r\n\r\n    // 先获取TRTC签名（避免Safari异步弹窗限制）\r\n    const { userSig, roomId } = await trtcApi.genSign({ courseId: course.id })\r\n\r\n    // 构建教室URL\r\n    const classroomUrl = new URL(`${config.public.rtcDomain}/#/class`)\r\n    classroomUrl.searchParams.set('userId', authStore.user?.id || '')\r\n    classroomUrl.searchParams.set('roomId', roomId.toString())\r\n    classroomUrl.searchParams.set('courseId', course.id)\r\n    classroomUrl.searchParams.set('userSig', userSig)\r\n\r\n    // Convert schedule times to timestamps\r\n    const beginTime = parseDateTimeUtil(course.scheduleDate, course.startTime).valueOf()\r\n    const endTime = parseDateTimeUtil(course.scheduleDate, course.endTime).valueOf()\r\n    classroomUrl.searchParams.set('classBeginTime', beginTime.toString())\r\n    classroomUrl.searchParams.set('classEndTime', endTime.toString())\r\n\r\n    // 同步打开弹窗窗口（所有浏览器兼容）\r\n    const newWindow = window.open(classroomUrl.toString(), '_blank')\r\n\r\n    // 检查弹窗是否被阻止\r\n    if (!newWindow || newWindow.closed) {\r\n      // 弹窗被阻止时，在本页面跳转\r\n      toast.add({\r\n        title: t('messages.schedule.toast.enteringClassSuccess.title'),\r\n        description: t('messages.schedule.toast.enteringClassSuccess.description'),\r\n        color: 'green',\r\n        timeout: 2000,\r\n        icon: 'i-heroicons-check-circle'\r\n      })\r\n\r\n      // 使用 nextTick 确保 toast 显示后再跳转\r\n      await nextTick()\r\n      await new Promise(resolve => setTimeout(resolve, 500))\r\n\r\n      // 在本页面跳转到教室\r\n      window.location.href = classroomUrl.toString()\r\n      return\r\n    }\r\n\r\n    // 弹窗成功打开\r\n    toast.add({\r\n      title: t('messages.schedule.toast.enteringClassPopup.title'),\r\n      description: t('messages.schedule.toast.enteringClassPopup.description'),\r\n      color: 'green',\r\n      timeout: 2000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n\r\n  } catch (error) {\r\n    toast.add({\r\n      title: t('messages.schedule.toast.enterClassError.title'),\r\n      description: t('messages.schedule.toast.enterClassError.description'),\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    enteringClassId.value = null\r\n  }\r\n}\r\n\r\n//检查是否可以预约，当前时间在课程开始前\r\nconst canBookCourse = (course: TeacherCourse) => {\r\n  return canBookCourseUtil(course.scheduleDate, course.startTime)\r\n}\r\n\r\n// 检查是否可以取消预约（提前1小时）\r\nconst canCancelCourse = (course: TeacherCourse) => {\r\n  return canCancelCourseUtil(course.scheduleDate, course.startTime)\r\n}\r\n\r\n// 监听分页变化\r\nwatch(currentPage, () => {\r\n  fetchCourses()\r\n})\r\n\r\nonMounted(() => {\r\n  fetchCourses()\r\n  setupStatusUpdateTimer()\r\n})\r\n\r\nonUnmounted(() => {\r\n  clearStatusUpdateTimer()\r\n})\r\n\r\n// 获取状态描述文本\r\nconst getStatusDescription = (course: TeacherCourse) => {\r\n  let description = ''\r\n  if (course.status === TeacherScheduleStatus.Available) {\r\n    if(canBookCourse(course)){\r\n      description = '(可预约)'\r\n    }else{\r\n      description = '(已过可预约时间)'\r\n    }\r\n  } else if (course.status === TeacherScheduleStatus.Booked && course.studentId === studentId.value) {\r\n    description = canCancelCourse(course) ? '(提前1小时可取消预约)' : '(提前5分钟可进入)'\r\n  } else if (course.status === TeacherScheduleStatus.Unavailable) {\r\n    description = '(已被他人预约)'\r\n  }else if(course.status === TeacherScheduleStatus.Started){\r\n    description = '(课程已开始)'\r\n  }\r\n  return description\r\n}\r\n\r\n// 添加提交昵称的函数\r\nconst submitNickname = async () => {\r\n  if (!nickname.value.trim()) {\r\n    toast.add({\r\n      title: '请输入昵称',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n    return\r\n  }\r\n\r\n  submittingNickname.value = true\r\n  try {\r\n    await studentApi.updateStudentInfo(studentId.value as string, {\r\n      nickname: nickname.value.trim()\r\n    })\r\n    \r\n    // 更新本地用户信息\r\n    if (authStore.user?.student) {\r\n      authStore.user.student.nickname = nickname.value.trim()\r\n    }\r\n    \r\n    showNicknameDialog.value = false\r\n    toast.add({\r\n      title: '昵称设置成功',\r\n      color: 'green',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n    \r\n    // 继续预约流程\r\n    toast.add({\r\n      title: '预约成功',\r\n      description: '课程已成功预约',\r\n      color: 'green',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-check-circle'\r\n    })\r\n    \r\n    fetchCourses()\r\n  } catch (error: any) {\r\n    toast.add({\r\n      title: '昵称设置失败',\r\n      description: error?.message || '请稍后重试',\r\n      color: 'red',\r\n      timeout: 3000,\r\n      icon: 'i-heroicons-x-circle'\r\n    })\r\n  } finally {\r\n    submittingNickname.value = false\r\n  }\r\n}\r\n\r\nconst cancelNicknameDialog = () => {\r\n  showNicknameDialog.value = false\r\n  // 继续预约流程\r\n  toast.add({\r\n    title: '预约成功',\r\n    description: '课程已成功预约',\r\n    color: 'green',\r\n    timeout: 3000,\r\n    icon: 'i-heroicons-check-circle'\r\n  })\r\n  \r\n  fetchCourses()\r\n}\r\n</script> "], "names": ["formatDate", "formatDateUtil", "canCancelCourse", "parseDateTimeUtil", "canBookCourse", "canBookCourseUtil", "canCancelCourseUtil"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgQM,UAAA,EAAE,EAAE,IAAI,QAAQ;AACtB,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,UAAU;AACzB,UAAM,YAAY,aAAa;AAC/B,UAAM,QAAQ,SAAS;AAEjB,UAAA,YAAY,MAAM,OAAO;AAC/B,UAAM,YAAY,SAAS,MAAA;;AAAM,mCAAU,SAAV,mBAAgB,YAAhB,mBAAyB;AAAA,KAAE;AAEtD,UAAA,cAAc,IAAI,CAAC;AACnB,UAAA,WAAW,IAAI,EAAE;AACjB,UAAA,QAAQ,IAAI,CAAC;AAGb,UAAA,UAAU,IAAqB,EAAE;AACjC,UAAA,UAAU,IAAI,KAAK;AAGzB,UAAM,eAAe,IAAI,MAAM,MAAM,QAAkB,EAAE;AAGnD,UAAA,YAAY,SAAS,MAAM;AACxB,aAAA,kBAAkB,GAAG,IAAI;AAAA,IAAA,CACjC;AAGK,UAAA,aAAa,CAAC,SAAiB;AACnC,mBAAa,QAAQ;AACrB,kBAAY,QAAQ;AACP,mBAAA;AAAA,IACf;AAGA,UAAM,kBAAkB,MAAM;AAC5B,mBAAa,QAAQ;AACrB,kBAAY,QAAQ;AACP,mBAAA;AAAA,IACf;AAGM,UAAA,oBAAoB,IAAI,KAAK;AAC7B,UAAA,qBAAqB,IAAI,EAAE;AAC3B,UAAA,uBAAuB,IAAI,EAAE;AAC7B,UAAA,iBAAiB,IAA0B,IAAI;AAG/C,UAAA,kBAAkB,IAAmB,IAAI;AAGzC,UAAA,mBAAmB,IAAI,EAAE;AACzB,UAAA,kBAAkB,IAAI,EAAE;AACxB,UAAA,iBAAiB,IAAI,EAAE;AAGH,QAA2C,IAAI;AACrD,QAAI,CAAC;AACH,QAAI,KAAK,IAAK,CAAA;AAG9B,UAAA,qBAAqB,IAAI,KAAK;AAC9B,UAAA,WAAW,IAAI,EAAE;AACjB,UAAA,qBAAqB,IAAI,KAAK;AAgFpC,UAAM,eAAe,YAAY;AAC/B,cAAQ,QAAQ;AACZ,UAAA;AAEI,cAAA,WAAW,MAAM,WAAW,qBAAqB;AAAA,UACrD;AAAA,UACA,WAAW,aAAa;AAAA,UACxB,SAAS,aAAa;AAAA;AAAA,UACtB,MAAM,YAAY;AAAA,UAClB,UAAU,SAAS;AAAA,QAAA,CACpB;AAEO,gBAAA,QAAQ,SAAS,QAAQ,CAAC;AAC5B,cAAA,QAAQ,SAAS,cAAc;AAAA,eAC9B,OAAO;AACd,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAEpB;AAGM,UAAAA,eAAa,CAAC,SAAiB;AAC5B,aAAAC,WAAe,MAAM,OAAO;AAAA,IACrC;AAkCM,UAAA,aAAa,CAAC,WAA0B;AAC5C,qBAAe,QAAQ;AACvB,yBAAmB,QAAQ;AACN,2BAAA,QAAQ,SAASD,aAAW,OAAO,YAAY,CAAC,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO;AAC7G,wBAAkB,QAAQ;AAAA,IAC5B;AAEA,UAAM,iBAAiB,YAAY;;AAC7B,UAAA,CAAC,eAAe,MAAO;AAEvB,UAAA;AACF,cAAM,WAAW,WAAW;AAAA,UAC1B,WAAW,eAAe,MAAM;AAAA,UAChC,UAAU,eAAe,MAAM;AAAA,QAAA,CAChC;AAGG,YAAA,GAAC,qBAAU,SAAV,mBAAgB,YAAhB,mBAAyB,eAAY,qBAAU,SAAV,mBAAgB,YAAhB,mBAAyB,cAAW,SAAS;AAC/E,gBAAA,eAAe,cAAc;AACnC,gBAAG,qBAAU,SAAV,mBAAgB,YAAhB,mBAAyB,cAAW,SAAQ;AAC7C,+BAAmB,QAAQ;AAC3B;AAAA,UAAA;AAAA,QACF;AAGF,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAEY,qBAAA;AAAA,eACN,OAAY;AACnB,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,cAAa,+BAAO,YAAW;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAEE,aAAA,+BAAO,UAAS,QAAO;AACxB,iBAAO,KAAK;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,cACL,UAAU,mBAAmB;AAAA,cAC7B,aAAa,MAAM,MAAM;AAAA,cACzB,MAAM;AAAA;AAAA,YAAA;AAAA,UACR,CACD;AAAA,QAAA;AAGA,aAAA,+BAAO,UAAS,QAAO;AACxB,iBAAO,KAAK;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,cACL,UAAU,mBAAmB;AAAA,cAC7B,aAAa,MAAM,MAAM;AAAA,cACzB,MAAM;AAAA;AAAA,YAAA;AAAA,UACR,CACD;AAAA,QAAA;AAAA,MACH,UACA;AACA,0BAAkB,QAAQ;AAC1B,uBAAe,QAAQ;AAAA,MAAA;AAAA,IAE3B;AAEM,UAAA,eAAe,CAAC,WAA0B;AAE1C,UAAA,CAACE,kBAAgB,MAAM,GAAG;AAC5B,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGF,qBAAe,QAAQ;AACvB,yBAAmB,QAAQ;AACN,2BAAA,QAAQ,SAASF,aAAW,OAAO,YAAY,CAAC,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO;AAC7G,wBAAkB,QAAQ;AAAA,IAC5B;AAEA,UAAM,sBAAsB,YAAY;AAClC,UAAA,CAAC,eAAe,MAAO;AAEvB,UAAA;AACF,cAAM,WAAW,aAAa;AAAA,UAC5B,UAAU,eAAe,MAAM;AAAA,QAAA,CAChC;AAED,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAGY,qBAAA;AAAA,eACN,OAAY;AACnB,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,cAAa,+BAAO,YAAW;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,0BAAkB,QAAQ;AAC1B,uBAAe,QAAQ;AAAA,MAAA;AAAA,IAE3B;AAEA,UAAM,eAAe,MAAM;AACzB,wBAAkB,QAAQ;AAC1B,qBAAe,QAAQ;AAAA,IACzB;AAEM,UAAA,uBAAuB,CAAC,WAA0B;AACtD,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAGM,UAAA,mBAAmB,OAAO,WAA0B;;AACxD,UAAI,gBAAgB,MAAO;AAC3B,sBAAgB,QAAQ,OAAO;AAE3B,UAAA;AAEF,cAAM,SAAS,iBAAiB;AAGhC,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,8CAA8C;AAAA,UACvD,aAAa,EAAE,oDAAoD;AAAA,UACnE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAGK,cAAA,EAAE,SAAS,OAAA,IAAW,MAAM,QAAQ,QAAQ,EAAE,UAAU,OAAO,IAAI;AAGzE,cAAM,eAAe,IAAI,IAAI,GAAG,OAAO,OAAO,SAAS,UAAU;AACjE,qBAAa,aAAa,IAAI,YAAU,eAAU,SAAV,mBAAgB,OAAM,EAAE;AAChE,qBAAa,aAAa,IAAI,UAAU,OAAO,UAAU;AACzD,qBAAa,aAAa,IAAI,YAAY,OAAO,EAAE;AACtC,qBAAA,aAAa,IAAI,WAAW,OAAO;AAGhD,cAAM,YAAYG,cAAkB,OAAO,cAAc,OAAO,SAAS,EAAE,QAAQ;AACnF,cAAM,UAAUA,cAAkB,OAAO,cAAc,OAAO,OAAO,EAAE,QAAQ;AAC/E,qBAAa,aAAa,IAAI,kBAAkB,UAAU,UAAU;AACpE,qBAAa,aAAa,IAAI,gBAAgB,QAAQ,UAAU;AAGhE,cAAM,YAAmB,SAAA,KAAK,aAAa,YAAY,QAAQ;AAG3D,YAAA,CAAC,aAAa,UAAU,QAAQ;AAElC,gBAAM,IAAI;AAAA,YACR,OAAO,EAAE,oDAAoD;AAAA,YAC7D,aAAa,EAAE,0DAA0D;AAAA,YACzE,OAAO;AAAA,YACP,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AAGD,gBAAM,SAAS;AACf,gBAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAG,CAAC;AAG9C,UAAA,SAAA,SAAS,OAAO,aAAa,SAAS;AAC7C;AAAA,QAAA;AAIF,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,kDAAkD;AAAA,UAC3D,aAAa,EAAE,wDAAwD;AAAA,UACvE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,eAEM,OAAO;AACd,cAAM,IAAI;AAAA,UACR,OAAO,EAAE,+CAA+C;AAAA,UACxD,aAAa,EAAE,qDAAqD;AAAA,UACpE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,wBAAgB,QAAQ;AAAA,MAAA;AAAA,IAE5B;AAGM,UAAAC,kBAAgB,CAAC,WAA0B;AAC/C,aAAOC,cAAkB,OAAO,cAAc,OAAO,SAAS;AAAA,IAChE;AAGM,UAAAH,oBAAkB,CAAC,WAA0B;AACjD,aAAOI,gBAAoB,OAAO,cAAc,OAAO,SAAS;AAAA,IAClE;AAGA,UAAM,aAAa,MAAM;AACV,mBAAA;AAAA,IAAA,CACd;AAYK,UAAA,uBAAuB,CAAC,WAA0B;AACtD,UAAI,cAAc;AACd,UAAA,OAAO,WAAW,sBAAsB,WAAW;AAClD,YAAAF,gBAAc,MAAM,GAAE;AACT,wBAAA;AAAA,QAAA,OACX;AACW,wBAAA;AAAA,QAAA;AAAA,MAChB,WACS,OAAO,WAAW,sBAAsB,UAAU,OAAO,cAAc,UAAU,OAAO;AACnF,sBAAAF,kBAAgB,MAAM,IAAI,iBAAiB;AAAA,MAChD,WAAA,OAAO,WAAW,sBAAsB,aAAa;AAChD,sBAAA;AAAA,MACP,WAAA,OAAO,WAAW,sBAAsB,SAAQ;AACzC,sBAAA;AAAA,MAAA;AAET,aAAA;AAAA,IACT;AAGA,UAAM,iBAAiB,YAAY;;AACjC,UAAI,CAAC,SAAS,MAAM,QAAQ;AAC1B,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGF,yBAAmB,QAAQ;AACvB,UAAA;AACI,cAAA,WAAW,kBAAkB,UAAU,OAAiB;AAAA,UAC5D,UAAU,SAAS,MAAM,KAAK;AAAA,QAAA,CAC/B;AAGG,aAAA,eAAU,SAAV,mBAAgB,SAAS;AAC3B,oBAAU,KAAK,QAAQ,WAAW,SAAS,MAAM,KAAK;AAAA,QAAA;AAGxD,2BAAmB,QAAQ;AAC3B,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAGD,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAEY,qBAAA;AAAA,eACN,OAAY;AACnB,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,cAAa,+BAAO,YAAW;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,2BAAmB,QAAQ;AAAA,MAAA;AAAA,IAE/B;AAEA,UAAM,uBAAuB,MAAM;AACjC,yBAAmB,QAAQ;AAE3B,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MAAA,CACP;AAEY,mBAAA;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}