import { _ as __nuxt_component_2$1 } from './Button-3EsiVOgL.mjs';
import { useSSRContext, ref, unref, withCtx, createTextVNode, toDisplayString } from 'vue';
import { ssrRenderTeleport, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = {
  __name: "ConfirmationDialog",
  __ssrInlineRender: true,
  props: {
    title: String,
    message: String,
    visible: { type: <PERSON>olean, required: true },
    confirmText: String,
    cancelText: String
  },
  emits: ["confirm", "cancel", "update:visible"],
  setup(__props, { emit: __emit }) {
    const { t } = useI18n();
    const emit = __emit;
    const isLoading = ref(false);
    async function confirm() {
      isLoading.value = true;
      try {
        await emit("confirm");
      } finally {
        isLoading.value = false;
      }
    }
    function cancel() {
      emit("cancel");
      emit("update:visible", false);
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2$1;
      ssrRenderTeleport(_push, (_push2) => {
        if (__props.visible) {
          _push2(`<div class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999]" data-v-57db56a4><div class="bg-white rounded-lg shadow-lg p-6 w-80" data-v-57db56a4><h3 class="text-lg font-medium" data-v-57db56a4>${ssrInterpolate(__props.title)}</h3><p class="mt-2" data-v-57db56a4>${ssrInterpolate(__props.message)}</p><div class="mt-4 flex justify-end gap-2" data-v-57db56a4>`);
          _push2(ssrRenderComponent(_component_UButton, {
            color: "gray",
            onClick: cancel,
            disabled: unref(isLoading)
          }, {
            default: withCtx((_, _push3, _parent2, _scopeId) => {
              if (_push3) {
                _push3(`${ssrInterpolate(__props.cancelText || unref(t)("messages.common.cancel"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(__props.cancelText || unref(t)("messages.common.cancel")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push2(ssrRenderComponent(_component_UButton, {
            color: "primary",
            onClick: confirm,
            loading: unref(isLoading),
            disabled: unref(isLoading)
          }, {
            default: withCtx((_, _push3, _parent2, _scopeId) => {
              if (_push3) {
                _push3(`${ssrInterpolate(__props.confirmText || unref(t)("messages.common.confirm"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(__props.confirmText || unref(t)("messages.common.confirm")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push2(`</div></div></div>`);
        } else {
          _push2(`<!---->`);
        }
      }, "body", false, _parent);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ConfirmationDialog.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const __nuxt_component_2 = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-57db56a4"]]);

export { __nuxt_component_2 as _ };
//# sourceMappingURL=ConfirmationDialog-C6YtqB9_.mjs.map
