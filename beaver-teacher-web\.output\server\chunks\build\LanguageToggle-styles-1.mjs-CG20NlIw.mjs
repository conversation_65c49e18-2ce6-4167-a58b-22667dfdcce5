const LanguageToggle_vue_vue_type_style_index_0_scoped_e1d1dbca_lang = "@media (max-width:768px){.fixed[data-v-e1d1dbca]{right:1rem;top:1rem}.min-h-\\[44px\\][data-v-e1d1dbca]{min-height:48px}.min-w-\\[44px\\][data-v-e1d1dbca]{min-width:48px}.backdrop-blur-sm[data-v-e1d1dbca]{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}}";

export { LanguageToggle_vue_vue_type_style_index_0_scoped_e1d1dbca_lang as L };
//# sourceMappingURL=LanguageToggle-styles-1.mjs-CG20NlIw.mjs.map
