import { _ as __nuxt_component_0 } from './index-eP-xd45t.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, toDisplayString, isRef, createVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { B as useI18n, c as useToast } from './server.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './useFormGroup-B3564yef.mjs';
import '@vueuse/core';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "forgot-password",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    useToast();
    const loading = ref(false);
    const email = ref("");
    const emailError = ref("");
    const isSuccess = ref(false);
    function resetForm() {
      isSuccess.value = false;
      email.value = "";
      emailError.value = "";
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Icon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 bg-gray-50 flex flex-col" }, _attrs))}><div class="container mx-auto px-4 py-6 md:py-12 flex-1 safe-area"><div class="max-w-md mx-auto w-full">`);
      if (unref(isSuccess)) {
        _push(`<div class="text-center"><div class="mb-6"><div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">`);
        _push(ssrRenderComponent(_component_Icon, {
          name: "fa6-solid:circle-check",
          class: "w-8 h-8 text-green-600"
        }, null, _parent));
        _push(`</div><h1 class="text-xl font-bold mb-2 text-green-800">${ssrInterpolate(unref(t)("messages.forgotPassword.success.title"))}</h1><p class="text-gray-600 mb-6">${ssrInterpolate(unref(t)("messages.forgotPassword.success.description"))}</p><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"><div class="flex items-start">`);
        _push(ssrRenderComponent(_component_Icon, {
          name: "fa6-solid:circle-info",
          class: "w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0"
        }, null, _parent));
        _push(`<div class="text-sm text-blue-800"><p class="font-medium mb-1">${ssrInterpolate(unref(t)("messages.forgotPassword.success.checkEmail"))}</p><p>${ssrInterpolate(unref(t)("messages.forgotPassword.success.emailInstructions"))}</p></div></div></div></div><div class="space-y-3">`);
        _push(ssrRenderComponent(_component_UButton, {
          to: "/signin",
          block: "",
          size: "lg",
          color: "blue"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.forgotPassword.success.backToLogin"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.forgotPassword.success.backToLogin")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          variant: "outline",
          block: "",
          size: "lg",
          onClick: resetForm
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.forgotPassword.success.sendAnother"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.forgotPassword.success.sendAnother")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else {
        _push(`<div><div class="text-center mb-8"><h1 class="text-xl font-bold mb-2">${ssrInterpolate(unref(t)("messages.forgotPassword.title"))}</h1><p class="text-gray-600">${ssrInterpolate(unref(t)("messages.forgotPassword.subtitle"))}</p></div><div class="bg-white p-6 rounded-xl border border-gray-200"><form class="space-y-4">`);
        _push(ssrRenderComponent(_component_UFormGroup, {
          label: unref(t)("messages.signin.form.email"),
          error: unref(emailError)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UInput, {
                modelValue: unref(email),
                "onUpdate:modelValue": ($event) => isRef(email) ? email.value = $event : null,
                type: "email",
                placeholder: unref(t)("messages.signin.form.emailPlaceholder"),
                error: !!unref(emailError)
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_UInput, {
                  modelValue: unref(email),
                  "onUpdate:modelValue": ($event) => isRef(email) ? email.value = $event : null,
                  type: "email",
                  placeholder: unref(t)("messages.signin.form.emailPlaceholder"),
                  error: !!unref(emailError)
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"])
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          type: "submit",
          block: "",
          size: "lg",
          loading: unref(loading)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.forgotPassword.submit"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.forgotPassword.submit")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</form></div><div class="text-center mt-6">`);
        _push(ssrRenderComponent(_component_UButton, {
          variant: "link",
          color: "gray",
          to: "/signin"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.forgotPassword.backToLogin"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.forgotPassword.backToLogin")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      }
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/forgot-password.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=forgot-password-BvhzBvb2.mjs.map
