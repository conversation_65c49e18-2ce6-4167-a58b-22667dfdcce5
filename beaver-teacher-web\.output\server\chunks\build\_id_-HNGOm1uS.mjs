import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrInterpolate, ssrRenderList } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { c as useToast } from './server.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const router = useRouter();
    useRoute();
    useToast();
    const loading = ref(true);
    const teacher = ref(null);
    const audioPlayer = ref(null);
    const playVoice = () => {
      if (audioPlayer.value) {
        audioPlayer.value.play();
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e, _f, _g, _h;
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "max-w-4xl mx-auto p-6" }, _attrs))}>`);
      if (loading.value) {
        _push(`<div class="flex justify-center py-12">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin text-3xl text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<div class="space-y-8"><div>`);
        _push(ssrRenderComponent(_component_UButton, {
          variant: "ghost",
          color: "gray",
          icon: "i-heroicons-arrow-left",
          class: "!px-2",
          onClick: ($event) => unref(router).back()
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u8FD4\u56DE `);
            } else {
              return [
                createTextVNode(" \u8FD4\u56DE ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div><div class="flex items-start gap-8"><div class="w-32 h-32 rounded-full overflow-hidden flex-shrink-0"><img${ssrRenderAttr("src", ((_a = teacher.value) == null ? void 0 : _a.avatarUrl) || "/default-avatar.png")}${ssrRenderAttr("alt", (_b = teacher.value) == null ? void 0 : _b.nickname)} class="w-full h-full object-cover"></div><div class="flex-1"><h1 class="text-2xl font-semibold mb-2">${ssrInterpolate((_c = teacher.value) == null ? void 0 : _c.nickname)}</h1>`);
        if ((_d = teacher.value) == null ? void 0 : _d.voiceUrl) {
          _push(`<div class="mb-4"><audio${ssrRenderAttr("src", teacher.value.voiceUrl)} class="hidden"></audio>`);
          _push(ssrRenderComponent(_component_UButton, {
            color: "primary",
            variant: "solid",
            icon: "i-heroicons-speaker-wave",
            class: "rounded-full",
            onClick: playVoice
          }, null, _parent));
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div><div class="bg-white rounded-lg border p-6"><h2 class="text-lg font-semibold mb-4">\u6559\u5E08\u6807\u7B7E</h2><div class="space-y-4"><!--[-->`);
        ssrRenderList((_e = teacher.value) == null ? void 0 : _e.tags, (tag) => {
          _push(`<div class="flex items-center gap-2"><div class="border border-green-500 text-green-500 px-3 py-1 rounded">${ssrInterpolate(tag.tagName)}</div><div class="text-gray-600">${ssrInterpolate(tag.tagContent)}</div></div>`);
        });
        _push(`<!--]--></div></div><div class="bg-white rounded-lg border p-6"><h2 class="text-lg font-semibold mb-4">\u6559\u5E08\u6863\u6848</h2><div class="space-y-4"><div><div class="text-gray-500 mb-1">\u59D3\u540D</div><div>${ssrInterpolate((_f = teacher.value) == null ? void 0 : _f.nickname)}</div></div><div><div class="text-gray-500 mb-1">\u6BD5\u4E1A\u5B66\u6821</div><div>${ssrInterpolate((_g = teacher.value) == null ? void 0 : _g.graduatedSchool)}</div></div><div><div class="text-gray-500 mb-1">\u64C5\u957F\u8BFE\u7A0B</div><div>${ssrInterpolate((_h = teacher.value) == null ? void 0 : _h.specialty)}</div></div></div></div></div>`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/teacher/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-HNGOm1uS.mjs.map
