import { _ as __nuxt_component_0$1 } from './client-only-C3WHot0o.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { u as useUI, b as arrow$1, _ as __nuxt_component_0$2 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_4 } from './Pagination-JPpl4AYq.mjs';
import { i as i$3, o as o$1, E as E$2, u as u$5, t as t$2, a as i$1, q, N as N$1, b as E$1, w as w$2, c as w$4, h, A as A$2, d as i$5, n, f as f$1, e as u$3, l as l$2, g as N$3, P, j as N$4, v, s as s$4, k as o, m as d$3, T as T$2, _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_7 } from './Card-DSOtZzuw.mjs';
import { _ as __nuxt_component_2$2 } from './Form-CpNzGgY1.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { defineComponent, ref, computed, provide, watchEffect, h as h$1, Fragment, onMounted, onUnmounted, shallowRef, toRef, useId, inject, useSSRContext, watch, unref, withCtx, createVNode, createTextVNode, toDisplayString, isRef, mergeProps, reactive, openBlock, createBlock, renderList, createCommentVNode, resolveComponent, renderSlot, Transition } from 'vue';
import { m as mergeConfig, a as appConfig, t as twMerge, b as twJoin, C as defu, B as useI18n, f as useRouter, K as useAuthStore, c as useToast } from './server.mjs';
import { u as useFormGroup } from './useFormGroup-B3564yef.mjs';
import { ssrInterpolate, ssrRenderComponent, ssrRenderAttr, ssrRenderList, ssrRenderAttrs, ssrRenderClass, ssrLooseEqual, ssrGetDynamicModelProps, ssrRenderSlot, ssrIncludeBooleanAttr, ssrRenderStyle } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import { _ as __nuxt_component_3 } from './Select-8rIeiWNF.mjs';
import { a as getTeacherSchedules, b as cancelTeacherSchedule, d as batchCreateTeacherSchedules } from './teacher-BT-saGBd.mjs';
import { c as convertUTC8ToLocalTime, l as getTodayString, f as formatDate } from './datetime-BvKd-1hF.mjs';
import { _ as __nuxt_component_2$1 } from './ConfirmationDialog-C6YtqB9_.mjs';
import { T as TeacherScheduleStatus } from './api.d-D41uQaX7.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';

const radio = {
  wrapper: "relative flex items-start",
  container: "flex items-center h-5",
  base: "h-4 w-4 dark:checked:bg-current dark:checked:border-transparent disabled:opacity-50 disabled:cursor-not-allowed focus:ring-0 focus:ring-transparent focus:ring-offset-transparent",
  form: "form-radio",
  color: "text-{color}-500 dark:text-{color}-400",
  background: "bg-white dark:bg-gray-900",
  border: "border border-gray-300 dark:border-gray-700",
  ring: "focus-visible:ring-2 focus-visible:ring-{color}-500 dark:focus-visible:ring-{color}-400 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-gray-900",
  inner: "ms-3 flex flex-col",
  label: "text-sm font-medium text-gray-700 dark:text-gray-200",
  required: "text-sm text-red-500 dark:text-red-400",
  help: "text-sm text-gray-500 dark:text-gray-400",
  default: {
    color: "primary"
  }
};
const popover = {
  wrapper: "relative",
  container: "z-50 group",
  trigger: "inline-flex w-full",
  width: "",
  background: "bg-white dark:bg-gray-900",
  shadow: "shadow-lg",
  rounded: "rounded-md",
  ring: "ring-1 ring-gray-200 dark:ring-gray-800",
  base: "overflow-hidden focus:outline-none relative",
  // Syntax for `<Transition>` component https://vuejs.org/guide/built-ins/transition.html#css-based-transitions
  transition: {
    enterActiveClass: "transition ease-out duration-200",
    enterFromClass: "opacity-0 translate-y-1",
    enterToClass: "opacity-100 translate-y-0",
    leaveActiveClass: "transition ease-in duration-150",
    leaveFromClass: "opacity-100 translate-y-0",
    leaveToClass: "opacity-0 translate-y-1"
  },
  overlay: {
    base: "fixed inset-0 transition-opacity z-50",
    background: "bg-gray-200/75 dark:bg-gray-800/75",
    transition: {
      enterActiveClass: "ease-out duration-200",
      enterFromClass: "opacity-0",
      enterToClass: "opacity-100",
      leaveActiveClass: "ease-in duration-150",
      leaveFromClass: "opacity-100",
      leaveToClass: "opacity-0"
    }
  },
  popper: {
    strategy: "fixed"
  },
  default: {
    openDelay: 0,
    closeDelay: 0
  },
  arrow: arrow$1
};
function r(t2, e) {
  if (t2)
    return t2;
  let n2 = e != null ? e : "button";
  if (typeof n2 == "string" && n2.toLowerCase() === "button")
    return "button";
}
function s(t2, e) {
  let n2 = ref(r(t2.value.type, t2.value.as));
  return onMounted(() => {
    n2.value = r(t2.value.type, t2.value.as);
  }), watchEffect(() => {
    var u2;
    n2.value || o$1(e) && o$1(e) instanceof HTMLButtonElement && !((u2 = o$1(e)) != null && u2.hasAttribute("type")) && (n2.value = "button");
  }), n2;
}
var Se = ((s2) => (s2[s2.Open = 0] = "Open", s2[s2.Closed = 1] = "Closed", s2))(Se || {});
let re = Symbol("PopoverContext");
function U(d2) {
  let P2 = inject(re, null);
  if (P2 === null) {
    let s2 = new Error(`<${d2} /> is missing a parent <${ye.name} /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(s2, U), s2;
  }
  return P2;
}
let le = Symbol("PopoverGroupContext");
function ae() {
  return inject(le, null);
}
let ue = Symbol("PopoverPanelContext");
function ge() {
  return inject(ue, null);
}
let ye = defineComponent({ name: "Popover", inheritAttrs: false, props: { as: { type: [Object, String], default: "div" } }, setup(d2, { slots: P2, attrs: s2, expose: h$2 }) {
  var u$12;
  let f2 = ref(null);
  h$2({ el: f2, $el: f2 });
  let t$1 = ref(1), o$12 = ref(null), y = ref(null), v2 = ref(null), m = ref(null), b = computed(() => i$3(f2)), E$2$1 = computed(() => {
    var L, $;
    if (!o$1(o$12) || !o$1(m))
      return false;
    for (let x of (void 0).querySelectorAll("body > *"))
      if (Number(x == null ? void 0 : x.contains(o$1(o$12))) ^ Number(x == null ? void 0 : x.contains(o$1(m))))
        return true;
    let e = E$2(), r2 = e.indexOf(o$1(o$12)), l2 = (r2 + e.length - 1) % e.length, g = (r2 + 1) % e.length, G = e[l2], C = e[g];
    return !((L = o$1(m)) != null && L.contains(G)) && !(($ = o$1(m)) != null && $.contains(C));
  }), a = { popoverState: t$1, buttonId: ref(null), panelId: ref(null), panel: m, button: o$12, isPortalled: E$2$1, beforePanelSentinel: y, afterPanelSentinel: v2, togglePopover() {
    t$1.value = u$5(t$1.value, { [0]: 1, [1]: 0 });
  }, closePopover() {
    t$1.value !== 1 && (t$1.value = 1);
  }, close(e) {
    a.closePopover();
    let r2 = (() => e ? e instanceof HTMLElement ? e : e.value instanceof HTMLElement ? o$1(e) : o$1(a.button) : o$1(a.button))();
    r2 == null || r2.focus();
  } };
  provide(re, a), t$2(computed(() => u$5(t$1.value, { [0]: i$1.Open, [1]: i$1.Closed })));
  let S = { buttonId: a.buttonId, panelId: a.panelId, close() {
    a.closePopover();
  } }, c = ae(), I = c == null ? void 0 : c.registerPopover, [F, w$2$1] = q(), i$22 = N$1({ mainTreeNodeRef: c == null ? void 0 : c.mainTreeNodeRef, portals: F, defaultContainers: [o$12, m] });
  function p() {
    var e, r2, l2, g;
    return (g = c == null ? void 0 : c.isFocusWithinPopoverGroup()) != null ? g : ((e = b.value) == null ? void 0 : e.activeElement) && (((r2 = o$1(o$12)) == null ? void 0 : r2.contains(b.value.activeElement)) || ((l2 = o$1(m)) == null ? void 0 : l2.contains(b.value.activeElement)));
  }
  return watchEffect(() => I == null ? void 0 : I(S)), E$1((u$12 = b.value) == null ? void 0 : u$12.defaultView, "focus", (e) => {
    var r2, l2;
    e.target !== void 0 && e.target instanceof HTMLElement && t$1.value === 0 && (p() || o$12 && m && (i$22.contains(e.target) || (r2 = o$1(a.beforePanelSentinel)) != null && r2.contains(e.target) || (l2 = o$1(a.afterPanelSentinel)) != null && l2.contains(e.target) || a.closePopover()));
  }, true), w$2(i$22.resolveContainers, (e, r2) => {
    var l2;
    a.closePopover(), w$4(r2, h.Loose) || (e.preventDefault(), (l2 = o$1(o$12)) == null || l2.focus());
  }, computed(() => t$1.value === 0)), () => {
    let e = { open: t$1.value === 0, close: a.close };
    return h$1(Fragment, [h$1(w$2$1, {}, () => A$2({ theirProps: { ...d2, ...s2 }, ourProps: { ref: f2 }, slot: e, slots: P2, attrs: s2, name: "Popover" })), h$1(i$22.MainTreeNode)]);
  };
} }), Ge = defineComponent({ name: "PopoverButton", props: { as: { type: [Object, String], default: "button" }, disabled: { type: [Boolean], default: false }, id: { type: String, default: null } }, inheritAttrs: false, setup(d$1, { attrs: P$1, slots: s$12, expose: h2 }) {
  var u$2;
  let f$1$1 = (u$2 = d$1.id) != null ? u$2 : `headlessui-popover-button-${i$5()}`, t2 = U("PopoverButton"), o$2 = computed(() => i$3(t2.button));
  h2({ el: t2.button, $el: t2.button }), onMounted(() => {
    t2.buttonId.value = f$1$1;
  }), onUnmounted(() => {
    t2.buttonId.value = null;
  });
  let y = ae(), v2 = y == null ? void 0 : y.closeOthers, m = ge(), b = computed(() => m === null ? false : m.value === t2.panelId.value), E$12 = ref(null), a = `headlessui-focus-sentinel-${i$5()}`;
  b.value || watchEffect(() => {
    t2.button.value = o$1(E$12);
  });
  let S = s(computed(() => ({ as: d$1.as, type: P$1.type })), E$12);
  function c(e) {
    var r2, l2, g, G, C;
    if (b.value) {
      if (t2.popoverState.value === 1)
        return;
      switch (e.key) {
        case o.Space:
        case o.Enter:
          e.preventDefault(), (l2 = (r2 = e.target).click) == null || l2.call(r2), t2.closePopover(), (g = o$1(t2.button)) == null || g.focus();
          break;
      }
    } else
      switch (e.key) {
        case o.Space:
        case o.Enter:
          e.preventDefault(), e.stopPropagation(), t2.popoverState.value === 1 && (v2 == null || v2(t2.buttonId.value)), t2.togglePopover();
          break;
        case o.Escape:
          if (t2.popoverState.value !== 0)
            return v2 == null ? void 0 : v2(t2.buttonId.value);
          if (!o$1(t2.button) || (G = o$2.value) != null && G.activeElement && !((C = o$1(t2.button)) != null && C.contains(o$2.value.activeElement)))
            return;
          e.preventDefault(), e.stopPropagation(), t2.closePopover();
          break;
      }
  }
  function I(e) {
    b.value || e.key === o.Space && e.preventDefault();
  }
  function F(e) {
    var r2, l2;
    d$1.disabled || (b.value ? (t2.closePopover(), (r2 = o$1(t2.button)) == null || r2.focus()) : (e.preventDefault(), e.stopPropagation(), t2.popoverState.value === 1 && (v2 == null || v2(t2.buttonId.value)), t2.togglePopover(), (l2 = o$1(t2.button)) == null || l2.focus()));
  }
  function w2(e) {
    e.preventDefault(), e.stopPropagation();
  }
  let i$12 = n();
  function p() {
    let e = o$1(t2.panel);
    if (!e)
      return;
    function r2() {
      u$5(i$12.value, { [d$3.Forwards]: () => P(e, N$4.First), [d$3.Backwards]: () => P(e, N$4.Last) }) === T$2.Error && P(E$2().filter((g) => g.dataset.headlessuiFocusGuard !== "true"), u$5(i$12.value, { [d$3.Forwards]: N$4.Next, [d$3.Backwards]: N$4.Previous }), { relativeTo: o$1(t2.button) });
    }
    r2();
  }
  return () => {
    let e = t2.popoverState.value === 0, r2 = { open: e }, { ...l2 } = d$1, g = b.value ? { ref: E$12, type: S.value, onKeydown: c, onClick: F } : { ref: E$12, id: f$1$1, type: S.value, "aria-expanded": t2.popoverState.value === 0, "aria-controls": o$1(t2.panel) ? t2.panelId.value : void 0, disabled: d$1.disabled ? true : void 0, onKeydown: c, onKeyup: I, onClick: F, onMousedown: w2 };
    return h$1(Fragment, [A$2({ ourProps: g, theirProps: { ...P$1, ...l2 }, slot: r2, attrs: P$1, slots: s$12, name: "PopoverButton" }), e && !b.value && t2.isPortalled.value && h$1(f$1, { id: a, features: u$3.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: p })]);
  };
} });
defineComponent({ name: "PopoverOverlay", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true } }, setup(d2, { attrs: P2, slots: s2 }) {
  let h2 = U("PopoverOverlay"), f2 = `headlessui-popover-overlay-${i$5()}`, t2 = l$2(), o2 = computed(() => t2 !== null ? (t2.value & i$1.Open) === i$1.Open : h2.popoverState.value === 0);
  function y() {
    h2.closePopover();
  }
  return () => {
    let v2 = { open: h2.popoverState.value === 0 };
    return A$2({ ourProps: { id: f2, "aria-hidden": true, onClick: y }, theirProps: d2, slot: v2, attrs: P2, slots: s2, features: N$3.RenderStrategy | N$3.Static, visible: o2.value, name: "PopoverOverlay" });
  };
} });
let je = defineComponent({ name: "PopoverPanel", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, focus: { type: Boolean, default: false }, id: { type: String, default: null } }, inheritAttrs: false, setup(d$1, { attrs: P$1, slots: s2, expose: h2 }) {
  var w2;
  let f$1$1 = (w2 = d$1.id) != null ? w2 : `headlessui-popover-panel-${i$5()}`, { focus: t2 } = d$1, o$2 = U("PopoverPanel"), y = computed(() => i$3(o$2.panel)), v2 = `headlessui-focus-sentinel-before-${i$5()}`, m = `headlessui-focus-sentinel-after-${i$5()}`;
  h2({ el: o$2.panel, $el: o$2.panel }), onMounted(() => {
    o$2.panelId.value = f$1$1;
  }), onUnmounted(() => {
    o$2.panelId.value = null;
  }), provide(ue, o$2.panelId), watchEffect(() => {
    var p, u2;
    if (!t2 || o$2.popoverState.value !== 0 || !o$2.panel)
      return;
    let i2 = (p = y.value) == null ? void 0 : p.activeElement;
    (u2 = o$1(o$2.panel)) != null && u2.contains(i2) || P(o$1(o$2.panel), N$4.First);
  });
  let b = l$2(), E$12 = computed(() => b !== null ? (b.value & i$1.Open) === i$1.Open : o$2.popoverState.value === 0);
  function a(i2) {
    var p, u2;
    switch (i2.key) {
      case o.Escape:
        if (o$2.popoverState.value !== 0 || !o$1(o$2.panel) || y.value && !((p = o$1(o$2.panel)) != null && p.contains(y.value.activeElement)))
          return;
        i2.preventDefault(), i2.stopPropagation(), o$2.closePopover(), (u2 = o$1(o$2.button)) == null || u2.focus();
        break;
    }
  }
  function S(i2) {
    var u2, e, r2, l2, g;
    let p = i2.relatedTarget;
    p && o$1(o$2.panel) && ((u2 = o$1(o$2.panel)) != null && u2.contains(p) || (o$2.closePopover(), ((r2 = (e = o$1(o$2.beforePanelSentinel)) == null ? void 0 : e.contains) != null && r2.call(e, p) || (g = (l2 = o$1(o$2.afterPanelSentinel)) == null ? void 0 : l2.contains) != null && g.call(l2, p)) && p.focus({ preventScroll: true })));
  }
  let c = n();
  function I() {
    let i2 = o$1(o$2.panel);
    if (!i2)
      return;
    function p() {
      u$5(c.value, { [d$3.Forwards]: () => {
        var e;
        P(i2, N$4.First) === T$2.Error && ((e = o$1(o$2.afterPanelSentinel)) == null || e.focus());
      }, [d$3.Backwards]: () => {
        var u2;
        (u2 = o$1(o$2.button)) == null || u2.focus({ preventScroll: true });
      } });
    }
    p();
  }
  function F() {
    let i2 = o$1(o$2.panel);
    if (!i2)
      return;
    function p() {
      u$5(c.value, { [d$3.Forwards]: () => {
        let u2 = o$1(o$2.button), e = o$1(o$2.panel);
        if (!u2)
          return;
        let r2 = E$2(), l2 = r2.indexOf(u2), g = r2.slice(0, l2 + 1), C = [...r2.slice(l2 + 1), ...g];
        for (let L of C.slice())
          if (L.dataset.headlessuiFocusGuard === "true" || e != null && e.contains(L)) {
            let $ = C.indexOf(L);
            $ !== -1 && C.splice($, 1);
          }
        P(C, N$4.First, { sorted: false });
      }, [d$3.Backwards]: () => {
        var e;
        P(i2, N$4.Previous) === T$2.Error && ((e = o$1(o$2.button)) == null || e.focus());
      } });
    }
    p();
  }
  return () => {
    let i2 = { open: o$2.popoverState.value === 0, close: o$2.close }, { focus: p, ...u2 } = d$1, e = { ref: o$2.panel, id: f$1$1, onKeydown: a, onFocusout: t2 && o$2.popoverState.value === 0 ? S : void 0, tabIndex: -1 };
    return A$2({ ourProps: e, theirProps: { ...P$1, ...u2 }, attrs: P$1, slot: i2, slots: { ...s2, default: (...r2) => {
      var l2;
      return [h$1(Fragment, [E$12.value && o$2.isPortalled.value && h$1(f$1, { id: v2, ref: o$2.beforePanelSentinel, features: u$3.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: I }), (l2 = s2.default) == null ? void 0 : l2.call(s2, ...r2), E$12.value && o$2.isPortalled.value && h$1(f$1, { id: m, ref: o$2.afterPanelSentinel, features: u$3.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: F })])];
    } }, features: N$3.RenderStrategy | N$3.Static, visible: E$12.value, name: "PopoverPanel" });
  };
} });
defineComponent({ name: "PopoverGroup", inheritAttrs: false, props: { as: { type: [Object, String], default: "div" } }, setup(d2, { attrs: P2, slots: s2, expose: h2 }) {
  let f2 = ref(null), t2 = shallowRef([]), o$12 = computed(() => i$3(f2)), y = v();
  h2({ el: f2, $el: f2 });
  function v$1(a) {
    let S = t2.value.indexOf(a);
    S !== -1 && t2.value.splice(S, 1);
  }
  function m(a) {
    return t2.value.push(a), () => {
      v$1(a);
    };
  }
  function b() {
    var c;
    let a = o$12.value;
    if (!a)
      return false;
    let S = a.activeElement;
    return (c = o$1(f2)) != null && c.contains(S) ? true : t2.value.some((I) => {
      var F, w2;
      return ((F = a.getElementById(I.buttonId.value)) == null ? void 0 : F.contains(S)) || ((w2 = a.getElementById(I.panelId.value)) == null ? void 0 : w2.contains(S));
    });
  }
  function E2(a) {
    for (let S of t2.value)
      S.buttonId.value !== a && S.close();
  }
  return provide(le, { registerPopover: m, unregisterPopover: v$1, isFocusWithinPopoverGroup: b, closeOthers: E2, mainTreeNodeRef: y.mainTreeNodeRef }), () => h$1(Fragment, [A$2({ ourProps: { ref: f2 }, theirProps: { ...d2, ...P2 }, slot: {}, attrs: P2, slots: s2, name: "PopoverGroup" }), h$1(y.MainTreeNode)]);
} });
const config$1 = mergeConfig(appConfig.ui.strategy, appConfig.ui.radio, radio);
const _sfc_main$4 = defineComponent({
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: null
    },
    value: {
      type: [String, Number, Boolean],
      default: null
    },
    modelValue: {
      type: [String, Number, Boolean, Object],
      default: null
    },
    name: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    help: {
      type: String,
      default: null
    },
    label: {
      type: String,
      default: null
    },
    required: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: () => config$1.default.color,
      validator(value) {
        return appConfig.ui.colors.includes(value);
      }
    },
    inputClass: {
      type: String,
      default: null
    },
    class: {
      type: [String, Object, Array],
      default: () => ""
    },
    ui: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["update:modelValue", "change"],
  setup(props, { emit }) {
    var _a;
    const { ui, attrs } = useUI("radio", toRef(props, "ui"), config$1, toRef(props, "class"));
    const inputId = (_a = props.id) != null ? _a : useId("$IIOv1erkn7");
    const radioGroup = inject("radio-group", null);
    const { emitFormChange, color, name } = radioGroup != null ? radioGroup : useFormGroup(props, config$1);
    const pick = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        emit("update:modelValue", value);
        if (!radioGroup) {
          emitFormChange();
        }
      }
    });
    function onChange(event) {
      emit("change", event.target.value);
    }
    const inputClass = computed(() => {
      return twMerge(twJoin(
        ui.value.base,
        ui.value.form,
        ui.value.background,
        ui.value.border,
        color.value && ui.value.ring.replaceAll("{color}", color.value),
        color.value && ui.value.color.replaceAll("{color}", color.value)
      ), props.inputClass);
    });
    return {
      inputId,
      // eslint-disable-next-line vue/no-dupe-keys
      ui,
      attrs,
      pick,
      // eslint-disable-next-line vue/no-dupe-keys
      name,
      // eslint-disable-next-line vue/no-dupe-keys
      inputClass,
      onChange
    };
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  let _temp0;
  _push(`<div${ssrRenderAttrs(mergeProps({
    class: _ctx.ui.wrapper,
    "data-n-ids": _ctx.attrs["data-n-ids"]
  }, _attrs))}><div class="${ssrRenderClass(_ctx.ui.container)}"><input${ssrRenderAttrs((_temp0 = mergeProps({
    id: _ctx.inputId,
    checked: ssrLooseEqual(_ctx.pick, _ctx.value),
    name: _ctx.name,
    required: _ctx.required,
    value: _ctx.value,
    disabled: _ctx.disabled,
    type: "radio",
    class: _ctx.inputClass
  }, _ctx.attrs), mergeProps(_temp0, ssrGetDynamicModelProps(_temp0, _ctx.pick))))}></div>`);
  if (_ctx.label || _ctx.$slots.label) {
    _push(`<div class="${ssrRenderClass(_ctx.ui.inner)}"><label${ssrRenderAttr("for", _ctx.inputId)} class="${ssrRenderClass(_ctx.ui.label)}">`);
    ssrRenderSlot(_ctx.$slots, "label", { label: _ctx.label }, () => {
      _push(`${ssrInterpolate(_ctx.label)}`);
    }, _push, _parent);
    if (_ctx.required) {
      _push(`<span class="${ssrRenderClass(_ctx.ui.required)}">*</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</label>`);
    if (_ctx.help || _ctx.$slots.help) {
      _push(`<p class="${ssrRenderClass(_ctx.ui.help)}">`);
      ssrRenderSlot(_ctx.$slots, "help", { help: _ctx.help }, () => {
        _push(`${ssrInterpolate(_ctx.help)}`);
      }, _push, _parent);
      _push(`</p>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/forms/Radio.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const __nuxt_component_5$1 = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$1]]);
function getWindow(node) {
  if (node == null) {
    return void 0;
  }
  if (node.toString() !== "[object Window]") {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || void 0 : void 0;
  }
  return node;
}
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}
var max = Math.max;
var min = Math.min;
var round = Math.round;
function getUAString() {
  var uaData = (void 0).userAgentData;
  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
    return uaData.brands.map(function(item) {
      return item.brand + "/" + item.version;
    }).join(" ");
  }
  return (void 0).userAgent;
}
function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }
  var _ref = isElement(element) ? getWindow(element) : void 0, visualViewport = _ref.visualViewport;
  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width,
    height,
    top: y,
    right: x + width,
    bottom: y + height,
    left: x,
    x,
    y
  };
}
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop
  };
}
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}
function getNodeName(element) {
  return element ? (element.nodeName || "").toLowerCase() : null;
}
function getDocumentElement(element) {
  return ((isElement(element) ? element.ownerDocument : (
    // $FlowFixMe[prop-missing]
    element.document
  )) || (void 0).document).documentElement;
}
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height
  };
}
function getParentNode(element) {
  if (getNodeName(element) === "html") {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}
function getScrollParent(node) {
  if (["html", "body", "#document"].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    updatedList.concat(listScrollParents(getParentNode(target)))
  );
}
function isTableElement(element) {
  return ["table", "td", "th"].indexOf(getNodeName(element)) >= 0;
}
function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle(element).position === "fixed") {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle(element);
    if (elementCss.position === "fixed") {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }
  while (isHTMLElement(currentNode) && ["html", "body"].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle(currentNode);
    if (css.transform !== "none" || css.perspective !== "none" || css.contain === "paint" || ["transform", "perspective"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === "filter" || isFirefox && css.filter && css.filter !== "none") {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === "static") {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (offsetParent && (getNodeName(offsetParent) === "html" || getNodeName(offsetParent) === "body" && getComputedStyle(offsetParent).position === "static")) {
    return window;
  }
  return offsetParent || getContainingBlock(element) || window;
}
var top = "top";
var bottom = "bottom";
var right = "right";
var left = "left";
var auto = "auto";
var basePlacements = [top, bottom, right, left];
var start = "start";
var end = "end";
var clippingParents = "clippingParents";
var viewport = "viewport";
var popper = "popper";
var reference = "reference";
var variationPlacements = /* @__PURE__ */ basePlacements.reduce(function(acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = /* @__PURE__ */ [].concat(basePlacements, [auto]).reduce(function(acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []);
var beforeRead = "beforeRead";
var read = "read";
var afterRead = "afterRead";
var beforeMain = "beforeMain";
var main = "main";
var afterMain = "afterMain";
var beforeWrite = "beforeWrite";
var write = "write";
var afterWrite = "afterWrite";
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function(modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function(dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function(modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function(acc, phase) {
    return acc.concat(orderedModifiers.filter(function(modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}
function debounce(fn2) {
  var pending;
  return function() {
    if (!pending) {
      pending = new Promise(function(resolve) {
        Promise.resolve().then(function() {
          pending = void 0;
          resolve(fn2());
        });
      });
    }
    return pending;
  };
}
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function(merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function(key) {
    return merged[key];
  });
}
function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();
    if (layoutViewport || !layoutViewport && strategy === "fixed") {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x + getWindowScrollBarX(element),
    y
  };
}
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;
  if (getComputedStyle(body || html).direction === "rtl") {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x,
    y
  };
}
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}
function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === "fixed");
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping = ["absolute", "fixed"].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function(clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== "body";
  });
}
function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === "clippingParents" ? getClippingParents(element) : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(function(accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}
function getBasePlacement(placement) {
  return placement.split("-")[0];
}
function getVariation(placement) {
  return placement.split("-")[1];
}
function getMainAxisFromPlacement(placement) {
  return ["top", "bottom"].indexOf(placement) >= 0 ? "x" : "y";
}
function computeOffsets(_ref) {
  var reference2 = _ref.reference, element = _ref.element, placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === "y" ? "height" : "width";
    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
    }
  }
  return offsets;
}
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}
function expandToHashMap(value, keys) {
  return keys.reduce(function(hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: "absolute",
    placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets2));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function(key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? "y" : "x";
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}
var DEFAULT_OPTIONS = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return !args.some(function(element) {
    return !(element && typeof element.getBoundingClientRect === "function");
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers2 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 = typeof setOptionsAction === "function" ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options2);
        state.scrollParents = {
          reference: isElement(reference2) ? listScrollParents(reference2) : reference2.contextElement ? listScrollParents(reference2.contextElement) : [],
          popper: listScrollParents(popper2)
        };
        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers2, state.options.modifiers)));
        state.orderedModifiers = orderedModifiers.filter(function(m) {
          return m.enabled;
        });
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements, reference3 = _state$elements.reference, popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          return;
        }
        state.rects = {
          reference: getCompositeRect(reference3, getOffsetParent(popper3), state.options.strategy === "fixed"),
          popper: getLayoutRect(popper3)
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function(modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index], fn2 = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
          if (typeof fn2 === "function") {
            state = fn2({
              state,
              options: _options,
              name,
              instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function() {
        return new Promise(function(resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };
    if (!areValidElements(reference2, popper2)) {
      return instance;
    }
    instance.setOptions(options).then(function(state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function(_ref) {
        var name = _ref.name, _ref$options = _ref.options, options2 = _ref$options === void 0 ? {} : _ref$options, effect2 = _ref.effect;
        if (typeof effect2 === "function") {
          var cleanupFn = effect2({
            state,
            name,
            instance,
            options: options2
          });
          var noopFn = function noopFn2() {
          };
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function(fn2) {
        return fn2();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}
var passive = {
  passive: true
};
function effect$2(_ref) {
  var state = _ref.state, instance = _ref.instance, options = _ref.options;
  var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
  var window = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
  if (scroll) {
    scrollParents.forEach(function(scrollParent) {
      scrollParent.addEventListener("scroll", instance.update, passive);
    });
  }
  if (resize) {
    window.addEventListener("resize", instance.update, passive);
  }
  return function() {
    if (scroll) {
      scrollParents.forEach(function(scrollParent) {
        scrollParent.removeEventListener("scroll", instance.update, passive);
      });
    }
    if (resize) {
      window.removeEventListener("resize", instance.update, passive);
    }
  };
}
const eventListeners = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function fn() {
  },
  effect: effect$2,
  data: {}
};
function popperOffsets(_ref) {
  var state = _ref.state, name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: "absolute",
    placement: state.placement
  });
}
const popperOffsets$1 = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: popperOffsets,
  data: {}
};
var unsetSides = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function roundOffsetsByDPR(_ref, win) {
  var x = _ref.x, y = _ref.y;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x * dpr) / dpr || 0,
    y: round(y * dpr) / dpr || 0
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;
  var _ref3 = typeof roundOffsets === "function" ? roundOffsets({
    x,
    y
  }) : {
    x,
    y
  };
  x = _ref3.x;
  y = _ref3.y;
  var hasX = offsets.hasOwnProperty("x");
  var hasY = offsets.hasOwnProperty("y");
  var sideX = left;
  var sideY = top;
  var win = void 0;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = "clientHeight";
    var widthProp = "clientWidth";
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (getComputedStyle(offsetParent).position !== "static" && position === "absolute") {
        heightProp = "scrollHeight";
        widthProp = "scrollWidth";
      }
    }
    offsetParent = offsetParent;
    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        offsetParent[heightProp]
      );
      y -= offsetY - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }
    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        offsetParent[widthProp]
      );
      x -= offsetX - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign({
    position
  }, adaptive && unsetSides);
  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x,
    y
  }, getWindow(popper2)) : {
    x,
    y
  };
  x = _ref4.x;
  y = _ref4.y;
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? "0" : "", _Object$assign[sideX] = hasX ? "0" : "", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
  }
  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : "", _Object$assign2[sideX] = hasX ? x + "px" : "", _Object$assign2.transform = "", _Object$assign2));
}
function computeStyles(_ref5) {
  var state = _ref5.state, options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
    isFixed: state.options.strategy === "fixed"
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive,
      roundOffsets
    })));
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: "absolute",
      adaptive: false,
      roundOffsets
    })));
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-placement": state.placement
  });
}
const computeStyles$1 = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: computeStyles,
  data: {}
};
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function(name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function(name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? "" : value);
      }
    });
  });
}
function effect$1(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function() {
    Object.keys(state.elements).forEach(function(name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);
      var style = styleProperties.reduce(function(style2, property) {
        style2[property] = "";
        return style2;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function(attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
const applyStyles$1 = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: applyStyles,
  effect: effect$1,
  requires: ["computeStyles"]
};
var defaultModifiers = [eventListeners, popperOffsets$1, computeStyles$1, applyStyles$1];
var hash$1 = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function(matched) {
    return hash$1[matched];
  });
}
var hash = {
  start: "end",
  end: "start"
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function(matched) {
    return hash[matched];
  });
}
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements$1 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement2) {
    return getVariation(placement2) === variation;
  }) : basePlacements;
  var allowedPlacements = placements$1.filter(function(placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements$1;
  }
  var overflows = allowedPlacements.reduce(function(acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function(a, b) {
    return overflows[a] - overflows[b];
  });
}
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}
function flip(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement].concat(fallbackPlacements).reduce(function(acc, placement2) {
    return acc.concat(getBasePlacement(placement2) === auto ? computeAutoPlacement(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
      flipVariations,
      allowedAutoPlacements
    }) : placement2);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i2 = 0; i2 < placements2.length; i2++) {
    var placement = placements2[i2];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? "width" : "height";
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }
    if (checks.every(function(check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function(placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function(check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === "break")
        break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
const flip$1 = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: flip,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref = typeof offset2 === "function" ? offset2(Object.assign({}, rects, {
    placement
  })) : offset2, skidding = _ref[0], distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}
function offset(_ref2) {
  var state = _ref2.state, options = _ref2.options, name = _ref2.name;
  var _options$offset = options.offset, offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function(acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }
  state.modifiersData[name] = data;
}
const offset$1 = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: offset
};
function getAltAxis(axis) {
  return axis === "x" ? "y" : "x";
}
function within(min$1, value, max$1) {
  return max(min$1, min(value, max$1));
}
function withinMaxClamp(min2, value, max2) {
  var v2 = within(min2, value, max2);
  return v2 > max2 ? max2 : v2;
}
function preventOverflow(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === "function" ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === "number" ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis) {
    var _offsetModifierState$;
    var mainSide = mainAxis === "y" ? top : left;
    var altSide = mainAxis === "y" ? bottom : right;
    var len = mainAxis === "y" ? "height" : "width";
    var offset2 = popperOffsets2[mainAxis];
    var min$1 = offset2 + overflow[mainSide];
    var max$1 = offset2 - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData["arrow#persistent"] ? state.modifiersData["arrow#persistent"].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === "y" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset2 + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset2 + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min$1, tetherMin) : min$1, offset2, tether ? max(max$1, tetherMax) : max$1);
    popperOffsets2[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset2;
  }
  if (checkAltAxis) {
    var _offsetModifierState$2;
    var _mainSide = mainAxis === "x" ? top : left;
    var _altSide = mainAxis === "x" ? bottom : right;
    var _offset = popperOffsets2[altAxis];
    var _len = altAxis === "y" ? "height" : "width";
    var _min = _offset + overflow[_mainSide];
    var _max = _offset - overflow[_altSide];
    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;
    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
    popperOffsets2[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }
  state.modifiersData[name] = data;
}
const preventOverflow$1 = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: preventOverflow,
  requiresIfExists: ["offset"]
};
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding = typeof padding === "function" ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state, name = _ref.name, options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? "height" : "width";
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === "y" ? top : left;
  var maxProp = axis === "y" ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets2[axis] - state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === "y" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset2, _state$modifiersData$.centerOffset = offset2 - center, _state$modifiersData$);
}
function effect(_ref2) {
  var state = _ref2.state, options = _ref2.options;
  var _options$element = options.element, arrowElement = _options$element === void 0 ? "[data-popper-arrow]" : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === "string") {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    return;
  }
  state.elements.arrow = arrowElement;
}
const arrowModifier = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: arrow,
  effect,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};
popperGenerator({
  defaultModifiers: [...defaultModifiers, offset$1, flip$1, preventOverflow$1, computeStyles$1, eventListeners, arrowModifier]
});
function usePopper({
  locked = false,
  overflowPadding = 8,
  offsetDistance = 8,
  offsetSkid = 0,
  gpuAcceleration = true,
  adaptive = true,
  scroll = true,
  resize = true,
  arrow: arrow2 = false,
  placement,
  strategy
}, virtualReference) {
  const reference2 = ref(null);
  const popper2 = ref(null);
  const instance = ref(null);
  return [reference2, popper2, instance];
}
const config = mergeConfig(appConfig.ui.strategy, appConfig.ui.popover, popover);
const _sfc_main$3 = defineComponent({
  components: {
    HPopover: ye,
    HPopoverButton: Ge,
    HPopoverPanel: je
  },
  inheritAttrs: false,
  props: {
    mode: {
      type: String,
      default: "click",
      validator: (value) => ["click", "hover"].includes(value)
    },
    open: {
      type: Boolean,
      default: void 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    openDelay: {
      type: Number,
      default: () => config.default.openDelay
    },
    closeDelay: {
      type: Number,
      default: () => config.default.closeDelay
    },
    overlay: {
      type: Boolean,
      default: false
    },
    popper: {
      type: Object,
      default: () => ({})
    },
    class: {
      type: [String, Object, Array],
      default: () => ""
    },
    ui: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["update:open"],
  setup(props, { emit }) {
    const { ui, attrs } = useUI("popover", toRef(props, "ui"), config, toRef(props, "class"));
    const popper2 = computed(() => defu(props.mode === "hover" ? { offsetDistance: 0 } : {}, props.popper, ui.value.popper));
    const [trigger, container] = usePopper(popper2.value);
    const popover2 = ref(null);
    const popoverApi = ref(null);
    let openTimeout = null;
    let closeTimeout = null;
    const containerStyle = computed(() => {
      var _a, _b, _c;
      if (props.mode !== "hover") {
        return {};
      }
      const offsetDistance = ((_a = props.popper) == null ? void 0 : _a.offsetDistance) || ((_b = ui.value.popper) == null ? void 0 : _b.offsetDistance) || 8;
      const placement = (_c = popper2.value.placement) == null ? void 0 : _c.split("-")[0];
      const padding = `${offsetDistance}px`;
      if (placement === "top" || placement === "bottom") {
        return {
          paddingTop: padding,
          paddingBottom: padding
        };
      } else if (placement === "left" || placement === "right") {
        return {
          paddingLeft: padding,
          paddingRight: padding
        };
      } else {
        return {
          paddingTop: padding,
          paddingBottom: padding,
          paddingLeft: padding,
          paddingRight: padding
        };
      }
    });
    function onTouchStart(event) {
      if (!event.cancelable || !popoverApi.value || props.mode === "click") {
        return;
      }
      if (popoverApi.value.popoverState === 0) {
        popoverApi.value.closePopover();
      } else {
        popoverApi.value.togglePopover();
      }
    }
    function onMouseEnter() {
      if (props.mode !== "hover" || !popoverApi.value) {
        return;
      }
      if (closeTimeout) {
        clearTimeout(closeTimeout);
        closeTimeout = null;
      }
      if (popoverApi.value.popoverState === 0) {
        return;
      }
      openTimeout = openTimeout || setTimeout(() => {
        popoverApi.value.togglePopover && popoverApi.value.togglePopover();
        openTimeout = null;
      }, props.openDelay);
    }
    function onMouseLeave() {
      if (props.mode !== "hover" || !popoverApi.value) {
        return;
      }
      if (openTimeout) {
        clearTimeout(openTimeout);
        openTimeout = null;
      }
      if (popoverApi.value.popoverState === 1) {
        return;
      }
      closeTimeout = closeTimeout || setTimeout(() => {
        popoverApi.value.closePopover && popoverApi.value.closePopover();
        closeTimeout = null;
      }, props.closeDelay);
    }
    watch(() => props.open, (newValue, oldValue) => {
      if (!popoverApi.value)
        return;
      if (oldValue === void 0 || newValue === oldValue)
        return;
      if (newValue) {
        popoverApi.value.popoverState = 0;
      } else {
        popoverApi.value.closePopover();
      }
    });
    watch(() => {
      var _a;
      return (_a = popoverApi.value) == null ? void 0 : _a.popoverState;
    }, (newValue, oldValue) => {
      if (oldValue === void 0 || newValue === oldValue)
        return;
      emit("update:open", newValue === 0);
    });
    s$4(() => useId("$mvBhIaU7v1"));
    return {
      // eslint-disable-next-line vue/no-dupe-keys
      ui,
      attrs,
      popover: popover2,
      // eslint-disable-next-line vue/no-dupe-keys
      popper: popper2,
      trigger,
      container,
      containerStyle,
      onTouchStart,
      onMouseEnter,
      onMouseLeave
    };
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _component_HPopover = resolveComponent("HPopover");
  const _component_HPopoverButton = resolveComponent("HPopoverButton");
  const _component_HPopoverPanel = resolveComponent("HPopoverPanel");
  _push(ssrRenderComponent(_component_HPopover, mergeProps({
    ref: "popover",
    class: _ctx.ui.wrapper
  }, _ctx.attrs, { onMouseleave: _ctx.onMouseLeave }, _attrs), {
    default: withCtx(({ open, close }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent(_component_HPopoverButton, {
          ref: "trigger",
          as: "div",
          disabled: _ctx.disabled,
          class: _ctx.ui.trigger,
          role: "button",
          onMouseenter: _ctx.onMouseEnter,
          onTouchstart: _ctx.onTouchStart
        }, {
          default: withCtx((_, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              ssrRenderSlot(_ctx.$slots, "default", {
                open,
                close
              }, () => {
                _push3(`<button${ssrIncludeBooleanAttr(_ctx.disabled) ? " disabled" : ""}${_scopeId2}> Open </button>`);
              }, _push3, _parent3, _scopeId2);
            } else {
              return [
                renderSlot(_ctx.$slots, "default", {
                  open,
                  close
                }, () => [
                  createVNode("button", { disabled: _ctx.disabled }, " Open ", 8, ["disabled"])
                ])
              ];
            }
          }),
          _: 2
        }, _parent2, _scopeId));
        if (_ctx.overlay) {
          _push2(`<template>`);
          if (open) {
            _push2(`<div class="${ssrRenderClass([_ctx.ui.overlay.base, _ctx.ui.overlay.background])}"${_scopeId}></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</template>`);
        } else {
          _push2(`<!---->`);
        }
        if (open) {
          _push2(`<div class="${ssrRenderClass([_ctx.ui.container, _ctx.ui.width])}" style="${ssrRenderStyle(_ctx.containerStyle)}"${_scopeId}><template><div${_scopeId}>`);
          if (_ctx.popper.arrow) {
            _push2(`<div data-popper-arrow class="${ssrRenderClass(Object.values(_ctx.ui.arrow))}"${_scopeId}></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(ssrRenderComponent(_component_HPopoverPanel, {
            class: [_ctx.ui.base, _ctx.ui.background, _ctx.ui.ring, _ctx.ui.rounded, _ctx.ui.shadow],
            static: ""
          }, {
            default: withCtx((_, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                ssrRenderSlot(_ctx.$slots, "panel", {
                  open,
                  close
                }, null, _push3, _parent3, _scopeId2);
              } else {
                return [
                  renderSlot(_ctx.$slots, "panel", {
                    open,
                    close
                  })
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div></template></div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          createVNode(_component_HPopoverButton, {
            ref: "trigger",
            as: "div",
            disabled: _ctx.disabled,
            class: _ctx.ui.trigger,
            role: "button",
            onMouseenter: _ctx.onMouseEnter,
            onTouchstartPassive: _ctx.onTouchStart
          }, {
            default: withCtx(() => [
              renderSlot(_ctx.$slots, "default", {
                open,
                close
              }, () => [
                createVNode("button", { disabled: _ctx.disabled }, " Open ", 8, ["disabled"])
              ])
            ]),
            _: 2
          }, 1032, ["disabled", "class", "onMouseenter", "onTouchstartPassive"]),
          _ctx.overlay ? (openBlock(), createBlock(Transition, mergeProps({
            key: 0,
            appear: ""
          }, _ctx.ui.overlay.transition), {
            default: withCtx(() => [
              open ? (openBlock(), createBlock("div", {
                key: 0,
                class: [_ctx.ui.overlay.base, _ctx.ui.overlay.background]
              }, null, 2)) : createCommentVNode("", true)
            ]),
            _: 2
          }, 1040)) : createCommentVNode("", true),
          open ? (openBlock(), createBlock("div", {
            key: 1,
            ref: "container",
            class: [_ctx.ui.container, _ctx.ui.width],
            style: _ctx.containerStyle,
            onMouseenter: _ctx.onMouseEnter
          }, [
            createVNode(Transition, mergeProps({ appear: "" }, _ctx.ui.transition), {
              default: withCtx(() => [
                createVNode("div", null, [
                  _ctx.popper.arrow ? (openBlock(), createBlock("div", {
                    key: 0,
                    "data-popper-arrow": "",
                    class: Object.values(_ctx.ui.arrow)
                  }, null, 2)) : createCommentVNode("", true),
                  createVNode(_component_HPopoverPanel, {
                    class: [_ctx.ui.base, _ctx.ui.background, _ctx.ui.ring, _ctx.ui.rounded, _ctx.ui.shadow],
                    static: ""
                  }, {
                    default: withCtx(() => [
                      renderSlot(_ctx.$slots, "panel", {
                        open,
                        close
                      })
                    ]),
                    _: 2
                  }, 1032, ["class"])
                ])
              ]),
              _: 2
            }, 1040)
          ], 46, ["onMouseenter"])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/overlays/Popover.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const __nuxt_component_0 = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender]]);
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "TimePicker",
  __ssrInlineRender: true,
  props: {
    modelValue: {},
    disabled: { type: Boolean },
    placeholder: {}
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const selectedHour = ref("");
    const selectedMinute = ref("");
    const hourOptions = computed(() => {
      return Array.from({ length: 24 }, (_, i2) => {
        const hour = i2.toString().padStart(2, "0");
        return {
          label: hour,
          value: hour
        };
      });
    });
    const minuteOptions = computed(() => {
      return Array.from({ length: 12 }, (_, i2) => {
        const minute = (i2 * 5).toString().padStart(2, "0");
        return {
          label: minute,
          value: minute
        };
      });
    });
    const displayTime = computed(() => {
      if (selectedHour.value && selectedMinute.value) {
        return `${selectedHour.value}:${selectedMinute.value}`;
      }
      return props.modelValue || "";
    });
    watch(() => props.modelValue, (newValue) => {
      if (newValue) {
        const [hour, minute] = newValue.split(":");
        selectedHour.value = hour || "";
        selectedMinute.value = minute || "";
      } else {
        selectedHour.value = "";
        selectedMinute.value = "";
      }
    }, { immediate: true });
    const updateTime = () => {
      if (selectedHour.value && selectedMinute.value) {
        const timeValue = `${selectedHour.value}:${selectedMinute.value}`;
        emit("update:modelValue", timeValue);
        emit("change", timeValue);
      }
    };
    const clearTime = () => {
      selectedHour.value = "";
      selectedMinute.value = "";
      emit("update:modelValue", null);
      emit("change", null);
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UPopover = __nuxt_component_0;
      const _component_UInput = __nuxt_component_9;
      const _component_UIcon = __nuxt_component_0$2;
      const _component_USelect = __nuxt_component_3;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "time-picker-container" }, _attrs))} data-v-08bfe35d>`);
      _push(ssrRenderComponent(_component_UPopover, { popper: { placement: "bottom-start" } }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UInput, {
              "model-value": unref(displayTime),
              placeholder: _ctx.placeholder || _ctx.$t("messages.schedule.modal.validation.startTime"),
              disabled: _ctx.disabled,
              readonly: "",
              class: "w-full cursor-pointer"
            }, {
              trailing: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UIcon, {
                    name: "i-heroicons-clock",
                    class: "w-4 h-4 text-gray-400"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UIcon, {
                      name: "i-heroicons-clock",
                      class: "w-4 h-4 text-gray-400"
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UInput, {
                "model-value": unref(displayTime),
                placeholder: _ctx.placeholder || _ctx.$t("messages.schedule.modal.validation.startTime"),
                disabled: _ctx.disabled,
                readonly: "",
                class: "w-full cursor-pointer"
              }, {
                trailing: withCtx(() => [
                  createVNode(_component_UIcon, {
                    name: "i-heroicons-clock",
                    class: "w-4 h-4 text-gray-400"
                  })
                ]),
                _: 1
              }, 8, ["model-value", "placeholder", "disabled"])
            ];
          }
        }),
        panel: withCtx(({ close }, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-4 w-64 bg-white border border-gray-200 rounded-lg shadow-lg" data-v-08bfe35d${_scopeId}><div class="flex items-center space-x-3 mb-4" data-v-08bfe35d${_scopeId}><div class="flex-1" data-v-08bfe35d${_scopeId}><label class="block text-xs font-medium text-gray-700 mb-2" data-v-08bfe35d${_scopeId}>${ssrInterpolate(_ctx.$t("messages.schedule.modal.timePicker.hour"))}</label>`);
            _push2(ssrRenderComponent(_component_USelect, {
              modelValue: unref(selectedHour),
              "onUpdate:modelValue": ($event) => isRef(selectedHour) ? selectedHour.value = $event : null,
              options: unref(hourOptions),
              placeholder: _ctx.$t("messages.schedule.modal.timePicker.hourPlaceholder"),
              size: "sm",
              onChange: updateTime
            }, null, _parent2, _scopeId));
            _push2(`</div><div class="flex-1" data-v-08bfe35d${_scopeId}><label class="block text-xs font-medium text-gray-700 mb-2" data-v-08bfe35d${_scopeId}>${ssrInterpolate(_ctx.$t("messages.schedule.modal.timePicker.minute"))}</label>`);
            _push2(ssrRenderComponent(_component_USelect, {
              modelValue: unref(selectedMinute),
              "onUpdate:modelValue": ($event) => isRef(selectedMinute) ? selectedMinute.value = $event : null,
              options: unref(minuteOptions),
              placeholder: _ctx.$t("messages.schedule.modal.timePicker.minutePlaceholder"),
              size: "sm",
              onChange: updateTime
            }, null, _parent2, _scopeId));
            _push2(`</div></div><div class="flex justify-end space-x-2" data-v-08bfe35d${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UButton, {
              variant: "ghost",
              size: "xs",
              onClick: clearTime
            }, {
              default: withCtx((_, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.schedule.modal.timePicker.clear"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.clear")), 1)
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UButton, {
              size: "xs",
              onClick: close
            }, {
              default: withCtx((_, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.schedule.modal.timePicker.confirm"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.confirm")), 1)
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "p-4 w-64 bg-white border border-gray-200 rounded-lg shadow-lg" }, [
                createVNode("div", { class: "flex items-center space-x-3 mb-4" }, [
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("label", { class: "block text-xs font-medium text-gray-700 mb-2" }, toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.hour")), 1),
                    createVNode(_component_USelect, {
                      modelValue: unref(selectedHour),
                      "onUpdate:modelValue": ($event) => isRef(selectedHour) ? selectedHour.value = $event : null,
                      options: unref(hourOptions),
                      placeholder: _ctx.$t("messages.schedule.modal.timePicker.hourPlaceholder"),
                      size: "sm",
                      onChange: updateTime
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "placeholder"])
                  ]),
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("label", { class: "block text-xs font-medium text-gray-700 mb-2" }, toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.minute")), 1),
                    createVNode(_component_USelect, {
                      modelValue: unref(selectedMinute),
                      "onUpdate:modelValue": ($event) => isRef(selectedMinute) ? selectedMinute.value = $event : null,
                      options: unref(minuteOptions),
                      placeholder: _ctx.$t("messages.schedule.modal.timePicker.minutePlaceholder"),
                      size: "sm",
                      onChange: updateTime
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "placeholder"])
                  ])
                ]),
                createVNode("div", { class: "flex justify-end space-x-2" }, [
                  createVNode(_component_UButton, {
                    variant: "ghost",
                    size: "xs",
                    onClick: clearTime
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.clear")), 1)
                    ]),
                    _: 1
                  }),
                  createVNode(_component_UButton, {
                    size: "xs",
                    onClick: close
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(_ctx.$t("messages.schedule.modal.timePicker.confirm")), 1)
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/TimePicker.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const TimePicker = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-08bfe35d"]]);
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "NewScheduleModal",
  __ssrInlineRender: true,
  props: {
    modelValue: { type: Boolean },
    teacherId: {}
  },
  emits: ["update:modelValue", "created"],
  setup(__props, { emit: __emit }) {
    const { t: t2 } = useI18n();
    const props = __props;
    const emit = __emit;
    const isOpen = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const isSubmitting = ref(false);
    const today = getTodayString();
    const courseTypeOptions = [
      {
        label: t2("messages.schedule.modal.form.courseTypes.oneToOne"),
        value: "oneToOne"
      },
      {
        label: t2("messages.schedule.modal.form.courseTypes.group"),
        value: "group"
      }
    ];
    const formData = reactive({
      scheduleDate: today,
      startTime: "",
      lessonsCount: 1,
      maxStudents: 1,
      lessonsLength: 30,
      breakLength: 10,
      remark: "",
      courseType: "oneToOne"
    });
    const maxStudentsTooltip = computed(() => {
      if (!formData.maxStudents) {
        return t2("messages.schedule.modal.validation.maxStudents");
      } else if (formData.maxStudents < 2) {
        return t2("messages.schedule.modal.validation.maxStudentsMin");
      } else if (formData.maxStudents > 6) {
        return t2("messages.schedule.modal.validation.maxStudentsMax");
      } else {
        return t2("messages.schedule.modal.validation.studentRange");
      }
    });
    watch(() => formData.courseType, (newType) => {
      formData.maxStudents = newType === "oneToOne" ? 1 : 2;
    });
    const validateForm = (state) => {
      const errors = [];
      if (!state.scheduleDate) {
        errors.push({ path: "scheduleDate", message: t2("messages.schedule.modal.validation.scheduleDate") });
      }
      if (!state.startTime) {
        errors.push({ path: "startTime", message: t2("messages.schedule.modal.validation.startTime") });
      }
      if (!state.lessonsCount || state.lessonsCount < 1) {
        errors.push({ path: "lessonsCount", message: t2("messages.schedule.modal.validation.lessonsCount") });
      }
      if (!state.lessonsLength || state.lessonsLength < 1) {
        errors.push({ path: "lessonsLength", message: t2("messages.schedule.modal.validation.lessonLength") });
      }
      if (state.breakLength < 0) {
        errors.push({ path: "breakLength", message: t2("messages.schedule.modal.validation.breakLength") });
      }
      if (state.courseType === "group") {
        if (!state.maxStudents) {
          errors.push({ path: "maxStudents", message: t2("messages.schedule.modal.validation.maxStudents") });
        } else if (state.maxStudents < 2) {
          errors.push({ path: "maxStudents", message: t2("messages.schedule.modal.validation.maxStudentsMin") });
        } else if (state.maxStudents > 6) {
          errors.push({ path: "maxStudents", message: t2("messages.schedule.modal.validation.maxStudentsMax") });
        }
      }
      return errors;
    };
    const handleSubmit = async () => {
      if (isSubmitting.value)
        return;
      isSubmitting.value = true;
      try {
        await batchCreateTeacherSchedules(props.teacherId, formData);
        useToast().add({
          title: t2("messages.schedule.modal.toast.success.title"),
          color: "green"
        });
        isOpen.value = false;
        emit("created");
      } catch (error) {
        useToast().add({
          title: t2("messages.schedule.modal.toast.error.title"),
          description: (error == null ? void 0 : error.message) || t2("messages.schedule.modal.toast.error.description"),
          color: "red"
        });
      } finally {
        isSubmitting.value = false;
      }
    };
    const updateTooltip = () => {
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UModal = __nuxt_component_6;
      const _component_UCard = __nuxt_component_7;
      const _component_UForm = __nuxt_component_2$2;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_URadio = __nuxt_component_5$1;
      const _component_UButton = __nuxt_component_2;
      _push(ssrRenderComponent(_component_UModal, mergeProps({
        modelValue: isOpen.value,
        "onUpdate:modelValue": ($event) => isOpen.value = $event
      }, _attrs), {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UCard, null, {
              header: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="text-xl font-bold" data-v-0dbf7b6c${_scopeId2}>${ssrInterpolate(unref(t2)("messages.schedule.modal.title"))}</div>`);
                } else {
                  return [
                    createVNode("div", { class: "text-xl font-bold" }, toDisplayString(unref(t2)("messages.schedule.modal.title")), 1)
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UForm, {
                    state: formData,
                    validate: validateForm,
                    onSubmit: handleSubmit,
                    class: "schedule-form"
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.scheduleDate"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "scheduleDate",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent(_component_UInput, {
                                modelValue: formData.scheduleDate,
                                "onUpdate:modelValue": ($event) => formData.scheduleDate = $event,
                                type: "date",
                                min: unref(today),
                                class: "w-full"
                              }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode(_component_UInput, {
                                  modelValue: formData.scheduleDate,
                                  "onUpdate:modelValue": ($event) => formData.scheduleDate = $event,
                                  type: "date",
                                  min: unref(today),
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "min"])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div><div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.startTime"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "startTime",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent(TimePicker, {
                                modelValue: formData.startTime,
                                "onUpdate:modelValue": ($event) => formData.startTime = $event,
                                placeholder: unref(t2)("messages.schedule.modal.form.startTime"),
                                class: "w-full"
                              }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode(TimePicker, {
                                  modelValue: formData.startTime,
                                  "onUpdate:modelValue": ($event) => formData.startTime = $event,
                                  placeholder: unref(t2)("messages.schedule.modal.form.startTime"),
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div><div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.lessonsCount"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "lessonsCount",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent(_component_UInput, {
                                modelValue: formData.lessonsCount,
                                "onUpdate:modelValue": ($event) => formData.lessonsCount = $event,
                                type: "number",
                                min: "1",
                                class: "w-full"
                              }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode(_component_UInput, {
                                  modelValue: formData.lessonsCount,
                                  "onUpdate:modelValue": ($event) => formData.lessonsCount = $event,
                                  type: "number",
                                  min: "1",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div><div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.lessonLength"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "lessonsLength",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`<div class="flex items-center gap-2" data-v-0dbf7b6c${_scopeId4}>`);
                              _push5(ssrRenderComponent(_component_UInput, {
                                modelValue: formData.lessonsLength,
                                "onUpdate:modelValue": ($event) => formData.lessonsLength = $event,
                                type: "number",
                                min: "1",
                                readonly: true,
                                disabled: "",
                                class: "w-full bg-gray-100 cursor-not-allowed"
                              }, null, _parent5, _scopeId4));
                              _push5(`<span class="text-gray-500 whitespace-nowrap" data-v-0dbf7b6c${_scopeId4}>min.</span></div>`);
                            } else {
                              return [
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode(_component_UInput, {
                                    modelValue: formData.lessonsLength,
                                    "onUpdate:modelValue": ($event) => formData.lessonsLength = $event,
                                    type: "number",
                                    min: "1",
                                    readonly: true,
                                    disabled: "",
                                    class: "w-full bg-gray-100 cursor-not-allowed"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                                ])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div><div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.breakLength"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "breakLength",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`<div class="flex items-center gap-2" data-v-0dbf7b6c${_scopeId4}>`);
                              _push5(ssrRenderComponent(_component_UInput, {
                                modelValue: formData.breakLength,
                                "onUpdate:modelValue": ($event) => formData.breakLength = $event,
                                type: "number",
                                min: "0",
                                class: "w-full"
                              }, null, _parent5, _scopeId4));
                              _push5(`<span class="text-gray-500 whitespace-nowrap" data-v-0dbf7b6c${_scopeId4}>min.</span></div>`);
                            } else {
                              return [
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode(_component_UInput, {
                                    modelValue: formData.breakLength,
                                    "onUpdate:modelValue": ($event) => formData.breakLength = $event,
                                    type: "number",
                                    min: "0",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                                ])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div><div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.courseType"))}</div>`);
                        _push4(ssrRenderComponent(_component_UFormGroup, {
                          name: "courseType",
                          class: "input-wrapper"
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`<div class="flex gap-4" data-v-0dbf7b6c${_scopeId4}><!--[-->`);
                              ssrRenderList(courseTypeOptions, (option) => {
                                _push5(ssrRenderComponent(_component_URadio, {
                                  key: option.value,
                                  modelValue: formData.courseType,
                                  "onUpdate:modelValue": ($event) => formData.courseType = $event,
                                  value: option.value,
                                  label: option.label,
                                  name: "courseType",
                                  class: "!m-0"
                                }, null, _parent5, _scopeId4));
                              });
                              _push5(`<!--]--></div>`);
                            } else {
                              return [
                                createVNode("div", { class: "flex gap-4" }, [
                                  (openBlock(), createBlock(Fragment, null, renderList(courseTypeOptions, (option) => {
                                    return createVNode(_component_URadio, {
                                      key: option.value,
                                      modelValue: formData.courseType,
                                      "onUpdate:modelValue": ($event) => formData.courseType = $event,
                                      value: option.value,
                                      label: option.label,
                                      name: "courseType",
                                      class: "!m-0"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue", "value", "label"]);
                                  }), 64))
                                ])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div>`);
                        if (formData.courseType === "group") {
                          _push4(`<div class="form-group" data-v-0dbf7b6c${_scopeId3}><div class="label-wrapper" data-v-0dbf7b6c${_scopeId3}>${ssrInterpolate(unref(t2)("messages.schedule.modal.form.maxStudents"))}</div>`);
                          _push4(ssrRenderComponent(_component_UFormGroup, {
                            name: "maxStudents",
                            class: "input-wrapper"
                          }, {
                            default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                _push5(ssrRenderComponent(_component_UInput, {
                                  modelValue: formData.maxStudents,
                                  "onUpdate:modelValue": ($event) => formData.maxStudents = $event,
                                  type: "number",
                                  class: "w-full",
                                  title: maxStudentsTooltip.value,
                                  onInput: updateTooltip
                                }, null, _parent5, _scopeId4));
                              } else {
                                return [
                                  createVNode(_component_UInput, {
                                    modelValue: formData.maxStudents,
                                    "onUpdate:modelValue": ($event) => formData.maxStudents = $event,
                                    type: "number",
                                    class: "w-full",
                                    title: maxStudentsTooltip.value,
                                    onInput: updateTooltip
                                  }, null, 8, ["modelValue", "onUpdate:modelValue", "title"])
                                ];
                              }
                            }),
                            _: 1
                          }, _parent4, _scopeId3));
                          _push4(`</div>`);
                        } else {
                          _push4(`<!---->`);
                        }
                        _push4(`<div class="flex justify-end gap-2 mt-4" data-v-0dbf7b6c${_scopeId3}>`);
                        _push4(ssrRenderComponent(_component_UButton, {
                          color: "gray",
                          onClick: ($event) => isOpen.value = false,
                          disabled: isSubmitting.value
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`${ssrInterpolate(unref(t2)("messages.schedule.modal.buttons.cancel"))}`);
                            } else {
                              return [
                                createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.cancel")), 1)
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(ssrRenderComponent(_component_UButton, {
                          type: "submit",
                          color: "primary",
                          loading: isSubmitting.value,
                          disabled: isSubmitting.value
                        }, {
                          default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`${ssrInterpolate(unref(t2)("messages.schedule.modal.buttons.submit"))}`);
                            } else {
                              return [
                                createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.submit")), 1)
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                        _push4(`</div>`);
                      } else {
                        return [
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.scheduleDate")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "scheduleDate",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_UInput, {
                                  modelValue: formData.scheduleDate,
                                  "onUpdate:modelValue": ($event) => formData.scheduleDate = $event,
                                  type: "date",
                                  min: unref(today),
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "min"])
                              ]),
                              _: 1
                            })
                          ]),
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.startTime")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "startTime",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode(TimePicker, {
                                  modelValue: formData.startTime,
                                  "onUpdate:modelValue": ($event) => formData.startTime = $event,
                                  placeholder: unref(t2)("messages.schedule.modal.form.startTime"),
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                              ]),
                              _: 1
                            })
                          ]),
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonsCount")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "lessonsCount",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_UInput, {
                                  modelValue: formData.lessonsCount,
                                  "onUpdate:modelValue": ($event) => formData.lessonsCount = $event,
                                  type: "number",
                                  min: "1",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ]),
                              _: 1
                            })
                          ]),
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonLength")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "lessonsLength",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode(_component_UInput, {
                                    modelValue: formData.lessonsLength,
                                    "onUpdate:modelValue": ($event) => formData.lessonsLength = $event,
                                    type: "number",
                                    min: "1",
                                    readonly: true,
                                    disabled: "",
                                    class: "w-full bg-gray-100 cursor-not-allowed"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                                ])
                              ]),
                              _: 1
                            })
                          ]),
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.breakLength")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "breakLength",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode(_component_UInput, {
                                    modelValue: formData.breakLength,
                                    "onUpdate:modelValue": ($event) => formData.breakLength = $event,
                                    type: "number",
                                    min: "0",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                                ])
                              ]),
                              _: 1
                            })
                          ]),
                          createVNode("div", { class: "form-group" }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.courseType")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "courseType",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode("div", { class: "flex gap-4" }, [
                                  (openBlock(), createBlock(Fragment, null, renderList(courseTypeOptions, (option) => {
                                    return createVNode(_component_URadio, {
                                      key: option.value,
                                      modelValue: formData.courseType,
                                      "onUpdate:modelValue": ($event) => formData.courseType = $event,
                                      value: option.value,
                                      label: option.label,
                                      name: "courseType",
                                      class: "!m-0"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue", "value", "label"]);
                                  }), 64))
                                ])
                              ]),
                              _: 1
                            })
                          ]),
                          formData.courseType === "group" ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "form-group"
                          }, [
                            createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.maxStudents")), 1),
                            createVNode(_component_UFormGroup, {
                              name: "maxStudents",
                              class: "input-wrapper"
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_UInput, {
                                  modelValue: formData.maxStudents,
                                  "onUpdate:modelValue": ($event) => formData.maxStudents = $event,
                                  type: "number",
                                  class: "w-full",
                                  title: maxStudentsTooltip.value,
                                  onInput: updateTooltip
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "title"])
                              ]),
                              _: 1
                            })
                          ])) : createCommentVNode("", true),
                          createVNode("div", { class: "flex justify-end gap-2 mt-4" }, [
                            createVNode(_component_UButton, {
                              color: "gray",
                              onClick: ($event) => isOpen.value = false,
                              disabled: isSubmitting.value
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.cancel")), 1)
                              ]),
                              _: 1
                            }, 8, ["onClick", "disabled"]),
                            createVNode(_component_UButton, {
                              type: "submit",
                              color: "primary",
                              loading: isSubmitting.value,
                              disabled: isSubmitting.value
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.submit")), 1)
                              ]),
                              _: 1
                            }, 8, ["loading", "disabled"])
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UForm, {
                      state: formData,
                      validate: validateForm,
                      onSubmit: handleSubmit,
                      class: "schedule-form"
                    }, {
                      default: withCtx(() => [
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.scheduleDate")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "scheduleDate",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode(_component_UInput, {
                                modelValue: formData.scheduleDate,
                                "onUpdate:modelValue": ($event) => formData.scheduleDate = $event,
                                type: "date",
                                min: unref(today),
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue", "min"])
                            ]),
                            _: 1
                          })
                        ]),
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.startTime")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "startTime",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode(TimePicker, {
                                modelValue: formData.startTime,
                                "onUpdate:modelValue": ($event) => formData.startTime = $event,
                                placeholder: unref(t2)("messages.schedule.modal.form.startTime"),
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                            ]),
                            _: 1
                          })
                        ]),
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonsCount")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "lessonsCount",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode(_component_UInput, {
                                modelValue: formData.lessonsCount,
                                "onUpdate:modelValue": ($event) => formData.lessonsCount = $event,
                                type: "number",
                                min: "1",
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue"])
                            ]),
                            _: 1
                          })
                        ]),
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonLength")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "lessonsLength",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode("div", { class: "flex items-center gap-2" }, [
                                createVNode(_component_UInput, {
                                  modelValue: formData.lessonsLength,
                                  "onUpdate:modelValue": ($event) => formData.lessonsLength = $event,
                                  type: "number",
                                  min: "1",
                                  readonly: true,
                                  disabled: "",
                                  class: "w-full bg-gray-100 cursor-not-allowed"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                              ])
                            ]),
                            _: 1
                          })
                        ]),
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.breakLength")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "breakLength",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode("div", { class: "flex items-center gap-2" }, [
                                createVNode(_component_UInput, {
                                  modelValue: formData.breakLength,
                                  "onUpdate:modelValue": ($event) => formData.breakLength = $event,
                                  type: "number",
                                  min: "0",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                              ])
                            ]),
                            _: 1
                          })
                        ]),
                        createVNode("div", { class: "form-group" }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.courseType")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "courseType",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode("div", { class: "flex gap-4" }, [
                                (openBlock(), createBlock(Fragment, null, renderList(courseTypeOptions, (option) => {
                                  return createVNode(_component_URadio, {
                                    key: option.value,
                                    modelValue: formData.courseType,
                                    "onUpdate:modelValue": ($event) => formData.courseType = $event,
                                    value: option.value,
                                    label: option.label,
                                    name: "courseType",
                                    class: "!m-0"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue", "value", "label"]);
                                }), 64))
                              ])
                            ]),
                            _: 1
                          })
                        ]),
                        formData.courseType === "group" ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "form-group"
                        }, [
                          createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.maxStudents")), 1),
                          createVNode(_component_UFormGroup, {
                            name: "maxStudents",
                            class: "input-wrapper"
                          }, {
                            default: withCtx(() => [
                              createVNode(_component_UInput, {
                                modelValue: formData.maxStudents,
                                "onUpdate:modelValue": ($event) => formData.maxStudents = $event,
                                type: "number",
                                class: "w-full",
                                title: maxStudentsTooltip.value,
                                onInput: updateTooltip
                              }, null, 8, ["modelValue", "onUpdate:modelValue", "title"])
                            ]),
                            _: 1
                          })
                        ])) : createCommentVNode("", true),
                        createVNode("div", { class: "flex justify-end gap-2 mt-4" }, [
                          createVNode(_component_UButton, {
                            color: "gray",
                            onClick: ($event) => isOpen.value = false,
                            disabled: isSubmitting.value
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.cancel")), 1)
                            ]),
                            _: 1
                          }, 8, ["onClick", "disabled"]),
                          createVNode(_component_UButton, {
                            type: "submit",
                            color: "primary",
                            loading: isSubmitting.value,
                            disabled: isSubmitting.value
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.submit")), 1)
                            ]),
                            _: 1
                          }, 8, ["loading", "disabled"])
                        ])
                      ]),
                      _: 1
                    }, 8, ["state"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UCard, null, {
                header: withCtx(() => [
                  createVNode("div", { class: "text-xl font-bold" }, toDisplayString(unref(t2)("messages.schedule.modal.title")), 1)
                ]),
                default: withCtx(() => [
                  createVNode(_component_UForm, {
                    state: formData,
                    validate: validateForm,
                    onSubmit: handleSubmit,
                    class: "schedule-form"
                  }, {
                    default: withCtx(() => [
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.scheduleDate")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "scheduleDate",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_UInput, {
                              modelValue: formData.scheduleDate,
                              "onUpdate:modelValue": ($event) => formData.scheduleDate = $event,
                              type: "date",
                              min: unref(today),
                              class: "w-full"
                            }, null, 8, ["modelValue", "onUpdate:modelValue", "min"])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.startTime")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "startTime",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode(TimePicker, {
                              modelValue: formData.startTime,
                              "onUpdate:modelValue": ($event) => formData.startTime = $event,
                              placeholder: unref(t2)("messages.schedule.modal.form.startTime"),
                              class: "w-full"
                            }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonsCount")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "lessonsCount",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_UInput, {
                              modelValue: formData.lessonsCount,
                              "onUpdate:modelValue": ($event) => formData.lessonsCount = $event,
                              type: "number",
                              min: "1",
                              class: "w-full"
                            }, null, 8, ["modelValue", "onUpdate:modelValue"])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.lessonLength")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "lessonsLength",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode("div", { class: "flex items-center gap-2" }, [
                              createVNode(_component_UInput, {
                                modelValue: formData.lessonsLength,
                                "onUpdate:modelValue": ($event) => formData.lessonsLength = $event,
                                type: "number",
                                min: "1",
                                readonly: true,
                                disabled: "",
                                class: "w-full bg-gray-100 cursor-not-allowed"
                              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                              createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                            ])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.breakLength")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "breakLength",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode("div", { class: "flex items-center gap-2" }, [
                              createVNode(_component_UInput, {
                                modelValue: formData.breakLength,
                                "onUpdate:modelValue": ($event) => formData.breakLength = $event,
                                type: "number",
                                min: "0",
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                              createVNode("span", { class: "text-gray-500 whitespace-nowrap" }, "min.")
                            ])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", { class: "form-group" }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.courseType")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "courseType",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode("div", { class: "flex gap-4" }, [
                              (openBlock(), createBlock(Fragment, null, renderList(courseTypeOptions, (option) => {
                                return createVNode(_component_URadio, {
                                  key: option.value,
                                  modelValue: formData.courseType,
                                  "onUpdate:modelValue": ($event) => formData.courseType = $event,
                                  value: option.value,
                                  label: option.label,
                                  name: "courseType",
                                  class: "!m-0"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "value", "label"]);
                              }), 64))
                            ])
                          ]),
                          _: 1
                        })
                      ]),
                      formData.courseType === "group" ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "form-group"
                      }, [
                        createVNode("div", { class: "label-wrapper" }, toDisplayString(unref(t2)("messages.schedule.modal.form.maxStudents")), 1),
                        createVNode(_component_UFormGroup, {
                          name: "maxStudents",
                          class: "input-wrapper"
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_UInput, {
                              modelValue: formData.maxStudents,
                              "onUpdate:modelValue": ($event) => formData.maxStudents = $event,
                              type: "number",
                              class: "w-full",
                              title: maxStudentsTooltip.value,
                              onInput: updateTooltip
                            }, null, 8, ["modelValue", "onUpdate:modelValue", "title"])
                          ]),
                          _: 1
                        })
                      ])) : createCommentVNode("", true),
                      createVNode("div", { class: "flex justify-end gap-2 mt-4" }, [
                        createVNode(_component_UButton, {
                          color: "gray",
                          onClick: ($event) => isOpen.value = false,
                          disabled: isSubmitting.value
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.cancel")), 1)
                          ]),
                          _: 1
                        }, 8, ["onClick", "disabled"]),
                        createVNode(_component_UButton, {
                          type: "submit",
                          color: "primary",
                          loading: isSubmitting.value,
                          disabled: isSubmitting.value
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(unref(t2)("messages.schedule.modal.buttons.submit")), 1)
                          ]),
                          _: 1
                        }, 8, ["loading", "disabled"])
                      ])
                    ]),
                    _: 1
                  }, 8, ["state"])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/NewScheduleModal.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const __nuxt_component_5 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-0dbf7b6c"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "schedule",
  __ssrInlineRender: true,
  setup(__props) {
    const { t: t2 } = useI18n();
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const schedules = ref([]);
    const teacherInfo = ref(null);
    ref(null);
    useRouter();
    useAuthStore();
    const teacherId = ref("");
    const showNewScheduleModal = ref(false);
    const showConfirmDialog = ref(false);
    const scheduleToCancel = ref("");
    const fetchSchedules = async () => {
      if (!teacherId.value)
        return;
      try {
        const response = await getTeacherSchedules({
          teacherId: teacherId.value,
          startDate: "",
          endDate: "",
          page: currentPage.value,
          pageSize: pageSize.value
        });
        schedules.value = response.list;
        total.value = response.totalCount;
      } catch (error) {
        const toast = useToast();
        toast.add({
          title: t2("messages.schedule.toast.fetchError.title"),
          description: t2("messages.schedule.toast.fetchError.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      }
    };
    watch(currentPage, () => {
      fetchSchedules();
    });
    const formatDate$1 = (date) => {
      return formatDate(date, "MMM DD");
    };
    const getStatusColor = (status) => {
      const colors = {
        [TeacherScheduleStatus.Cancelled]: "gray",
        [TeacherScheduleStatus.Available]: "green",
        [TeacherScheduleStatus.Booked]: "blue",
        [TeacherScheduleStatus.Completed]: "gray",
        [TeacherScheduleStatus.Unavailable]: "gray",
        [TeacherScheduleStatus.Started]: "green"
      };
      return colors[status];
    };
    const getStatusKey = (status) => {
      const keys = {
        [TeacherScheduleStatus.Cancelled]: "cancelled",
        [TeacherScheduleStatus.Available]: "available",
        [TeacherScheduleStatus.Booked]: "booked",
        [TeacherScheduleStatus.Completed]: "completed",
        [TeacherScheduleStatus.Unavailable]: "unavailable",
        [TeacherScheduleStatus.Started]: "started"
      };
      return keys[status];
    };
    const onConfirm = async () => {
      try {
        await cancelTeacherSchedule(scheduleToCancel.value);
        const toast = useToast();
        toast.add({
          title: t2("messages.schedule.toast.cancelSuccess.title"),
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        fetchSchedules();
      } catch (error) {
        const toast = useToast();
        toast.add({
          title: t2("messages.schedule.toast.cancelError.title"),
          description: t2("messages.schedule.toast.cancelError.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        showConfirmDialog.value = false;
        scheduleToCancel.value = "";
      }
    };
    const onCancel = () => {
      showConfirmDialog.value = false;
      scheduleToCancel.value = "";
    };
    const openNewScheduleModal = () => {
      showNewScheduleModal.value = true;
    };
    const handleScheduleCreated = () => {
      fetchSchedules();
    };
    ref("");
    ref("");
    ref("");
    ref(null);
    ref(0);
    ref(Date.now());
    const getStudentDisplayName = (student) => {
      if (student.nickname && student.nickname !== "\u672A\u8BBE\u7F6E\u6635\u79F0") {
        return student.nickname;
      }
      if (student.realName) {
        return student.realName;
      }
      return student.phone || "Unknown";
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c;
      const _component_ClientOnly = __nuxt_component_0$1;
      const _component_UButton = __nuxt_component_2;
      const _component_UIcon = __nuxt_component_0$2;
      const _component_UBadge = __nuxt_component_1;
      const _component_UPagination = __nuxt_component_4;
      const _component_NewScheduleModal = __nuxt_component_5;
      const _component_ConfirmationDialog = __nuxt_component_2$1;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.schedule.title"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6">`);
      _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
      _push(`<div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6"><div class="hidden sm:flex items-center justify-between mb-4 pb-3 border-b border-gray-100"><h2 class="text-lg font-semibold">${ssrInterpolate(_ctx.$t("messages.schedule.title"))}</h2></div><div class="flex justify-between items-center mb-4 sm:mb-6"><div class="flex items-center gap-4"><div class="w-14 h-14 sm:w-16 sm:h-16 rounded-full overflow-hidden flex-shrink-0 cursor-pointer">`);
      if ((_a = unref(teacherInfo)) == null ? void 0 : _a.avatarUrl) {
        _push(`<img${ssrRenderAttr("src", unref(teacherInfo).avatarUrl)}${ssrRenderAttr("alt", (_b = unref(teacherInfo)) == null ? void 0 : _b.nickname)} class="w-full h-full object-cover">`);
      } else {
        _push(`<img${ssrRenderAttr("src", "/images/logo/favicon.png")} alt="Default Avatar" class="w-full h-full object-cover">`);
      }
      _push(`</div><h3 class="text-base sm:text-lg font-medium">${ssrInterpolate((_c = unref(teacherInfo)) == null ? void 0 : _c.nickname)}</h3></div>`);
      _push(ssrRenderComponent(_component_UButton, {
        color: "primary",
        variant: "soft",
        size: "sm",
        onClick: openNewScheduleModal
      }, {
        leading: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, { name: "i-heroicons-plus" }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UIcon, { name: "i-heroicons-plus" })
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` ${ssrInterpolate(_ctx.$t("messages.schedule.addCourse"))}`);
          } else {
            return [
              createTextVNode(" " + toDisplayString(_ctx.$t("messages.schedule.addCourse")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="space-y-0">`);
      if (unref(schedules).length === 0) {
        _push(`<div class="text-center py-10 text-gray-500">${ssrInterpolate(_ctx.$t("messages.records.noData"))}</div>`);
      } else {
        _push(`<div><!--[-->`);
        ssrRenderList(unref(schedules), (schedule, index) => {
          _push(`<div class="py-3"><div class="flex justify-between items-start gap-4"><div class="flex-1"><div class="text-sm font-medium mb-2">${ssrInterpolate(formatDate$1(schedule.scheduleDate))} \xB7 ${ssrInterpolate(schedule.startTime)} - ${ssrInterpolate(schedule.endTime)} UTC+8 </div><div class="text-sm text-gray-500 mb-2">${ssrInterpolate(unref(convertUTC8ToLocalTime)(schedule.scheduleDate, schedule.startTime, schedule.endTime))}</div><div class="text-gray-600 text-sm space-y-1"><div class="flex items-center gap-2">`);
          _push(ssrRenderComponent(_component_UBadge, {
            color: schedule.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? "blue" : "purple",
            class: "!rounded-sm !px-2 !py-0.5 !text-xs",
            variant: "subtle"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(schedule.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? _ctx.$t("messages.schedule.modal.form.courseTypes.oneToOne") : _ctx.$t("messages.schedule.modal.form.courseTypes.group"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(schedule.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? _ctx.$t("messages.schedule.modal.form.courseTypes.oneToOne") : _ctx.$t("messages.schedule.modal.form.courseTypes.group")), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          if (schedule.classType === "\u4E00\u5BF9\u591A\u8BFE\u7A0B") {
            _push(`<span class="text-xs text-gray-500"> (${ssrInterpolate(schedule.currentStudents || 0)}/${ssrInterpolate(schedule.maxStudents)}) </span>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
          if (schedule.studentList && schedule.studentList.length > 0) {
            _push(`<div>${ssrInterpolate(_ctx.$t("messages.schedule.student"))}\uFF1A${ssrInterpolate(schedule.studentList.map((student) => getStudentDisplayName(student)).join("\u3001"))}</div>`);
          } else {
            _push(`<div>${ssrInterpolate(_ctx.$t("messages.schedule.student"))}\uFF1Anone </div>`);
          }
          _push(ssrRenderComponent(_component_UBadge, {
            color: getStatusColor(schedule.status),
            class: "!rounded-sm !px-2 !py-0.5 !text-xs",
            variant: "subtle"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.$t(`messages.schedule.status.${getStatusKey(schedule.status)}`))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.$t(`messages.schedule.status.${getStatusKey(schedule.status)}`)), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
          _push(`</div></div>`);
          _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
          _push(`</div>`);
          if (index !== unref(schedules).length - 1) {
            _push(`<div class="h-px bg-gray-200 mt-3"></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        });
        _push(`<!--]--></div>`);
      }
      _push(`</div>`);
      if (unref(total) > unref(pageSize)) {
        _push(`<div class="mt-6 flex justify-center" dir="ltr">`);
        _push(ssrRenderComponent(_component_UPagination, {
          modelValue: unref(currentPage),
          "onUpdate:modelValue": ($event) => isRef(currentPage) ? currentPage.value = $event : null,
          total: unref(total),
          "page-size": unref(pageSize),
          ui: { rounded: "rounded-full" }
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
      _push(ssrRenderComponent(_component_NewScheduleModal, {
        modelValue: unref(showNewScheduleModal),
        "onUpdate:modelValue": ($event) => isRef(showNewScheduleModal) ? showNewScheduleModal.value = $event : null,
        "teacher-id": unref(teacherId),
        onCreated: handleScheduleCreated
      }, null, _parent));
      if (unref(showConfirmDialog)) {
        _push(ssrRenderComponent(_component_ConfirmationDialog, {
          title: _ctx.$t("messages.schedule.confirmation.cancelTitle"),
          message: _ctx.$t("messages.schedule.confirmation.cancelMessage"),
          visible: unref(showConfirmDialog),
          onConfirm,
          onCancel
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/schedule.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=schedule-BhMqti3b.mjs.map
