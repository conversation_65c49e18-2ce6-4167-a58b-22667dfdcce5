import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_0$1 } from './nuxt-link-DAFz7xX6.mjs';
import { _ as __nuxt_component_2$1 } from './ConfirmationDialog-C6YtqB9_.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { defineComponent, computed, ref, unref, isRef, withCtx, createTextVNode, toDisplayString, createVNode, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderList, ssrRenderComponent, ssrRenderAttr } from 'vue/server-renderer';
import { B as useI18n, K as useAuthStore, f as useRouter, c as useToast } from './server.mjs';
import { s as studentApi } from './student-DtKAviut.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './useFormGroup-B3564yef.mjs';
import '@vueuse/core';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const authStore = useAuthStore();
    const user = computed(() => authStore.user);
    const router = useRouter();
    const totalLessons = computed(() => {
      var _a2;
      var _a;
      return (_a2 = (_a = studentInfo.value) == null ? void 0 : _a.totalLessons) != null ? _a2 : 0;
    });
    const monthlyLessons = computed(() => {
      var _a2;
      var _a;
      return (_a2 = (_a = studentInfo.value) == null ? void 0 : _a.monthlyLessons) != null ? _a2 : 0;
    });
    const availableCards = computed(() => {
      var _a2;
      var _a;
      return (_a2 = (_a = studentInfo.value) == null ? void 0 : _a.availableCards) != null ? _a2 : 0;
    });
    const studentInfo = ref(null);
    const loading = ref(false);
    const isEditingNickname = ref(false);
    const editedNickname = ref("");
    ref();
    const fetchStudentInfo = async () => {
      var _a, _b;
      if (loading.value || !((_b = (_a = user.value) == null ? void 0 : _a.student) == null ? void 0 : _b.id))
        return;
      try {
        loading.value = true;
        studentInfo.value = await studentApi.getStudentInfo(user.value.student.id);
      } catch (error) {
        console.error("\u83B7\u53D6\u5B66\u751F\u4FE1\u606F\u5931\u8D25:", error);
        const toast = useToast();
        toast.add({
          title: t("messages.account.toast.fetchError.title"),
          description: t("messages.account.toast.fetchError.description"),
          color: "red",
          timeout: 3e3
        });
      } finally {
        loading.value = false;
      }
    };
    const showConfirmDialog = ref(false);
    const showHotlineModal = ref(false);
    const onConfirm = () => {
      showConfirmDialog.value = false;
      handleLogout();
    };
    const handleLogout = async () => {
      try {
        await authStore.logout();
        const toast = useToast();
        toast.add({
          title: t("messages.account.toast.logoutSuccess.title"),
          description: t("messages.account.toast.logoutSuccess.description"),
          color: "green",
          timeout: 3e3
        });
        router.push("/");
      } catch (error) {
        console.error("\u6CE8\u9500\u5931\u8D25:", error);
        const toast = useToast();
        toast.add({
          title: t("messages.account.toast.logoutError.title"),
          description: t("messages.account.toast.logoutError.description"),
          color: "red",
          timeout: 3e3
        });
      }
    };
    const onCancel = () => {
      showConfirmDialog.value = false;
    };
    const saveNickname = async () => {
      var _a, _b;
      if (!((_b = (_a = user.value) == null ? void 0 : _a.student) == null ? void 0 : _b.id))
        return;
      try {
        await studentApi.updateStudentInfo(user.value.student.id, {
          nickname: editedNickname.value
        });
        await fetchStudentInfo();
        isEditingNickname.value = false;
        const toast = useToast();
        toast.add({
          title: t("messages.account.toast.nicknameUpdateSuccess.title"),
          color: "green",
          timeout: 3e3
        });
      } catch (error) {
        console.error("\u66F4\u65B0\u6635\u79F0\u5931\u8D25:", error);
        const toast = useToast();
        toast.add({
          title: t("messages.account.toast.nicknameUpdateError.title"),
          description: t("messages.account.toast.nicknameUpdateError.description"),
          color: "red",
          timeout: 3e3
        });
      }
    };
    const cancelEditNickname = () => {
      isEditingNickname.value = false;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d;
      const _component_UIcon = __nuxt_component_0;
      const _component_UInput = __nuxt_component_9;
      const _component_UButton = __nuxt_component_2;
      const _component_NuxtLink = __nuxt_component_0$1;
      const _component_ConfirmationDialog = __nuxt_component_2$1;
      const _component_UModal = __nuxt_component_6;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.menu.account"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6"><h2 class="hidden sm:block text-lg font-semibold mb-3">${ssrInterpolate(_ctx.$t("messages.menu.account"))}</h2>`);
      if (unref(loading)) {
        _push(`<!--[--><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4"><div class="flex items-center"><div class="w-16 h-16 rounded-full bg-gray-200 animate-pulse mr-4"></div><div class="flex-1"><div class="h-6 bg-gray-200 rounded animate-pulse w-32 mb-2"></div><div class="h-4 bg-gray-200 rounded animate-pulse w-24"></div></div></div></div><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4"><div class="grid grid-cols-2 gap-4"><div class="bg-gray-50 rounded-xl p-4 border border-gray-100"><div class="h-4 bg-gray-200 rounded animate-pulse w-16 mb-2"></div><div class="h-8 bg-gray-200 rounded animate-pulse w-20"></div></div><div class="bg-gray-50 rounded-xl p-4 border border-gray-100"><div class="h-4 bg-gray-200 rounded animate-pulse w-16 mb-2"></div><div class="h-8 bg-gray-200 rounded animate-pulse w-20"></div></div></div><div class="h-5 bg-gray-200 rounded animate-pulse w-40 mt-4"></div></div><div class="bg-white rounded-xl border border-gray-200 p-0 mb-4"><!--[-->`);
        ssrRenderList(5, (i) => {
          _push(`<div class="flex items-center justify-between p-4 border-b last:border-b-0"><div class="h-5 bg-gray-200 rounded animate-pulse w-24"></div><div class="h-5 bg-gray-200 rounded animate-pulse w-5"></div></div>`);
        });
        _push(`<!--]--></div><!--]-->`);
      } else {
        _push(`<!--[--><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4"><div class="flex items-center"><div class="w-16 h-16 rounded-full bg-gray-200 mr-4 overflow-hidden cursor-pointer hover:opacity-80 transition-opacity relative group">`);
        if (!((_a = unref(studentInfo)) == null ? void 0 : _a.avatarUrl)) {
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-user",
            class: "w-full h-full p-4 text-gray-400"
          }, null, _parent));
        } else {
          _push(`<img${ssrRenderAttr("src", unref(studentInfo).avatarUrl)}${ssrRenderAttr("alt", _ctx.$t("messages.account.profile.avatar"))} class="w-full h-full object-cover">`);
        }
        _push(`<div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-camera",
          class: "w-6 h-6 text-white"
        }, null, _parent));
        _push(`</div></div><input type="file" accept="image/*" class="hidden"><div>`);
        if (!unref(isEditingNickname)) {
          _push(`<div class="flex items-center gap-2"><h3 class="text-xl font-bold">${ssrInterpolate(((_b = unref(studentInfo)) == null ? void 0 : _b.nickname) || _ctx.$t("messages.account.profile.nicknameNotSet"))}</h3><button class="text-gray-400 hover:text-gray-600">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-pencil-square",
            class: "w-5 h-5"
          }, null, _parent));
          _push(`</button></div>`);
        } else {
          _push(`<div class="flex items-center gap-2">`);
          _push(ssrRenderComponent(_component_UInput, {
            modelValue: unref(editedNickname),
            "onUpdate:modelValue": ($event) => isRef(editedNickname) ? editedNickname.value = $event : null,
            size: "sm",
            placeholder: _ctx.$t("messages.account.profile.nicknamePlaceholder"),
            class: "w-40"
          }, null, _parent));
          _push(ssrRenderComponent(_component_UButton, {
            size: "sm",
            color: "primary",
            onClick: saveNickname
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.$t("messages.account.profile.save"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.$t("messages.account.profile.save")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(ssrRenderComponent(_component_UButton, {
            size: "sm",
            color: "gray",
            onClick: cancelEditNickname
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.$t("messages.account.profile.cancel"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.$t("messages.account.profile.cancel")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        }
        _push(`<p class="text-gray-500">${ssrInterpolate(((_c = unref(studentInfo)) == null ? void 0 : _c.phone) || ((_d = unref(user)) == null ? void 0 : _d.phone) || _ctx.$t("messages.account.profile.phoneNotSet"))}</p></div></div></div><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 mb-4"><div class="grid grid-cols-2 gap-4"><div class="bg-gray-50 rounded-xl p-4 border border-gray-100"><div class="text-gray-600">${ssrInterpolate(_ctx.$t("messages.account.stats.totalLessons"))}</div><div class="text-2xl font-bold mt-2">${ssrInterpolate(unref(totalLessons))} ${ssrInterpolate(_ctx.$t("messages.account.stats.lessonsUnit"))}</div></div><div class="bg-gray-50 rounded-xl p-4 border border-gray-100"><div class="text-gray-600">${ssrInterpolate(_ctx.$t("messages.account.stats.monthlyLessons"))}</div><div class="text-2xl font-bold mt-2">${ssrInterpolate(unref(monthlyLessons))} ${ssrInterpolate(_ctx.$t("messages.account.stats.lessonsUnit"))}</div></div></div><div class="flex items-center justify-between mt-4"><div class="text-gray-700"><span class="font-medium">${ssrInterpolate(_ctx.$t("messages.account.stats.myCards"))}</span><span class="text-gray-500 text-sm ml-3">${ssrInterpolate(_ctx.$t("messages.account.stats.availableCards"))}: ${ssrInterpolate(unref(availableCards))} ${ssrInterpolate(_ctx.$t("messages.account.stats.timesUnit"))}</span></div><div class="flex items-center gap-2">`);
        _push(ssrRenderComponent(_component_UButton, {
          to: "/student/course-cards",
          color: "gray",
          variant: "soft",
          size: "sm"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("messages.account.stats.view"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("messages.account.stats.view")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          to: "/student/course-cards/buy",
          color: "primary",
          variant: "soft",
          size: "sm"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("messages.account.stats.purchase"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("messages.account.stats.purchase")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div></div><div class="bg-white rounded-xl border border-gray-200 overflow-hidden">`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/student/transactions",
          class: "flex items-center justify-between p-4 border-b"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<span${_scopeId}>${ssrInterpolate(_ctx.$t("messages.account.menu.transactions"))}</span>`);
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-chevron-right",
                class: "w-5 h-5 text-gray-400"
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode("span", null, toDisplayString(_ctx.$t("messages.account.menu.transactions")), 1),
                createVNode(_component_UIcon, {
                  name: "i-heroicons-chevron-right",
                  class: "w-5 h-5 text-gray-400"
                })
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/student/service",
          class: "flex items-center justify-between p-4 border-b"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<span${_scopeId}>${ssrInterpolate(_ctx.$t("messages.account.menu.customerService"))}</span>`);
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-chevron-right",
                class: "w-5 h-5 text-gray-400"
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode("span", null, toDisplayString(_ctx.$t("messages.account.menu.customerService")), 1),
                createVNode(_component_UIcon, {
                  name: "i-heroicons-chevron-right",
                  class: "w-5 h-5 text-gray-400"
                })
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/student/password",
          class: "flex items-center justify-between p-4 border-b"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<span${_scopeId}>${ssrInterpolate(_ctx.$t("messages.account.menu.changePassword"))}</span>`);
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-chevron-right",
                class: "w-5 h-5 text-gray-400"
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode("span", null, toDisplayString(_ctx.$t("messages.account.menu.changePassword")), 1),
                createVNode(_component_UIcon, {
                  name: "i-heroicons-chevron-right",
                  class: "w-5 h-5 text-gray-400"
                })
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`<button class="flex items-center justify-between p-4 w-full text-left text-red-500 hover:bg-red-50"><span>${ssrInterpolate(_ctx.$t("messages.account.menu.logout"))}</span>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-right-on-rectangle",
          class: "w-5 h-5"
        }, null, _parent));
        _push(`</button></div><!--]-->`);
      }
      _push(`</div></div>`);
      if (unref(showConfirmDialog)) {
        _push(ssrRenderComponent(_component_ConfirmationDialog, {
          title: _ctx.$t("messages.account.confirmDialog.title"),
          message: _ctx.$t("messages.account.confirmDialog.message"),
          visible: unref(showConfirmDialog),
          onConfirm,
          onCancel
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: unref(showHotlineModal),
        "onUpdate:modelValue": ($event) => isRef(showHotlineModal) ? showHotlineModal.value = $event : null
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-4"${_scopeId}><p class="mb-2"${_scopeId}>${ssrInterpolate(unref(t)("messages.common.customerService.hotline.title"))} <a${ssrRenderAttr("href", "tel:" + unref(t)("messages.common.customerService.hotline.number").replace(/[^0-9]/g, ""))} class="text-primary-500 hover:text-primary-600"${_scopeId}>${ssrInterpolate(unref(t)("messages.common.customerService.hotline.number"))}</a></p><p class="text-gray-500"${_scopeId}>${ssrInterpolate(unref(t)("messages.common.customerService.hotline.workingHours"))}</p></div>`);
          } else {
            return [
              createVNode("div", { class: "p-4" }, [
                createVNode("p", { class: "mb-2" }, [
                  createTextVNode(toDisplayString(unref(t)("messages.common.customerService.hotline.title")) + " ", 1),
                  createVNode("a", {
                    href: "tel:" + unref(t)("messages.common.customerService.hotline.number").replace(/[^0-9]/g, ""),
                    class: "text-primary-500 hover:text-primary-600"
                  }, toDisplayString(unref(t)("messages.common.customerService.hotline.number")), 9, ["href"])
                ]),
                createVNode("p", { class: "text-gray-500" }, toDisplayString(unref(t)("messages.common.customerService.hotline.workingHours")), 1)
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/account/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-CSWWE4er.mjs.map
