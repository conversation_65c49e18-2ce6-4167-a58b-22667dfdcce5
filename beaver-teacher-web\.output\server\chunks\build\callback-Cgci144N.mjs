import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { useSSRContext, defineComponent, ref, mergeProps, unref, withCtx, createVNode, createTextVNode, toDisplayString } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { e as useRoute, f as useRouter, c as useToast, B as useI18n } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "callback",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    useToast();
    const { t } = useI18n();
    const loading = ref(true);
    const status = ref("processing");
    const errorMessage = ref("");
    ref(route.query.orderNo);
    const redirect = ref(route.query.redirect);
    const teacherName = ref(route.query.teacherName);
    const handlePrimaryAction = () => {
      if (redirect.value) {
        router.push({
          path: redirect.value,
          query: {
            teacherName: teacherName.value
          }
        });
      } else {
        router.push("/student/account");
      }
    };
    const handleSecondaryAction = () => {
      if (redirect.value) {
        router.push("/student/account");
      } else {
        router.push("/student/course-cards/buy");
      }
    };
    const handleBack = () => {
      if (redirect.value) {
        router.push({
          path: "/student/course-cards/buy",
          query: {
            redirect: redirect.value,
            teacherName: teacherName.value
          }
        });
      } else {
        router.push("/student/course-cards/buy");
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-gray-50 p-4" }, _attrs))} data-v-a80083ee><div class="max-w-md w-full bg-white rounded-xl border border-gray-200 p-8 text-center" data-v-a80083ee>`);
      if (unref(loading)) {
        _push(`<div class="py-8" data-v-a80083ee>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "w-12 h-12 mx-auto text-green-500 animate-spin"
        }, null, _parent));
        _push(`<h2 class="mt-4 text-xl font-semibold text-gray-800" data-v-a80083ee>${ssrInterpolate(unref(t)("messages.payment.callback.verifying"))}</h2><p class="mt-2 text-gray-600" data-v-a80083ee>${ssrInterpolate(unref(t)("messages.payment.callback.pleaseWait"))}</p></div>`);
      } else if (unref(status) === "success") {
        _push(`<div class="py-8" data-v-a80083ee>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-check-circle",
          class: "w-16 h-16 mx-auto text-green-500"
        }, null, _parent));
        _push(`<h2 class="mt-4 text-2xl font-bold text-green-600" data-v-a80083ee>${ssrInterpolate(unref(t)("messages.payment.callback.success"))}</h2><p class="mt-2 text-gray-600" data-v-a80083ee>${ssrInterpolate(unref(t)("messages.payment.callback.successDescription"))}</p><div class="mt-8 space-y-3" data-v-a80083ee>`);
        _push(ssrRenderComponent(_component_UButton, {
          block: "",
          size: "lg",
          color: "green",
          class: "font-semibold",
          onClick: handlePrimaryAction
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: unref(redirect) ? "i-heroicons-calendar-days" : "i-heroicons-user-circle",
                class: "mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(unref(redirect) ? unref(t)("messages.payment.callback.buttons.backToBooking") : unref(t)("messages.payment.callback.buttons.backToAccount"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: unref(redirect) ? "i-heroicons-calendar-days" : "i-heroicons-user-circle",
                  class: "mr-2"
                }, null, 8, ["name"]),
                createTextVNode(" " + toDisplayString(unref(redirect) ? unref(t)("messages.payment.callback.buttons.backToBooking") : unref(t)("messages.payment.callback.buttons.backToAccount")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          block: "",
          size: "lg",
          variant: "outline",
          color: "gray",
          class: "font-semibold",
          onClick: handleSecondaryAction
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: unref(redirect) ? "i-heroicons-user-circle" : "i-heroicons-shopping-cart",
                class: "mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(unref(redirect) ? unref(t)("messages.payment.callback.buttons.backToAccount") : unref(t)("messages.payment.callback.buttons.backToPurchase"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: unref(redirect) ? "i-heroicons-user-circle" : "i-heroicons-shopping-cart",
                  class: "mr-2"
                }, null, 8, ["name"]),
                createTextVNode(" " + toDisplayString(unref(redirect) ? unref(t)("messages.payment.callback.buttons.backToAccount") : unref(t)("messages.payment.callback.buttons.backToPurchase")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else {
        _push(`<div class="py-8" data-v-a80083ee>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-x-circle",
          class: "w-16 h-16 mx-auto text-red-500"
        }, null, _parent));
        _push(`<h2 class="mt-4 text-xl font-semibold text-red-600" data-v-a80083ee>${ssrInterpolate(unref(errorMessage) || unref(t)("messages.payment.callback.failed"))}</h2><p class="mt-2 text-gray-600" data-v-a80083ee>${ssrInterpolate(unref(t)("messages.payment.callback.failedDescription"))}</p><div class="mt-6 space-y-3" data-v-a80083ee>`);
        _push(ssrRenderComponent(_component_UButton, {
          to: "/student/course-cards/buy",
          block: "",
          size: "lg",
          color: "orange",
          class: "font-semibold"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-shopping-cart",
                class: "mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(unref(t)("messages.payment.callback.buttons.backToPurchase"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-shopping-cart",
                  class: "mr-2"
                }),
                createTextVNode(" " + toDisplayString(unref(t)("messages.payment.callback.buttons.backToPurchase")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          variant: "outline",
          block: "",
          size: "lg",
          color: "gray",
          class: "font-semibold",
          onClick: handleBack
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-arrow-left",
                class: "mr-2"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(unref(t)("messages.payment.callback.buttons.back"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-arrow-left",
                  class: "mr-2"
                }),
                createTextVNode(" " + toDisplayString(unref(t)("messages.payment.callback.buttons.back")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/payment/callback.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const callback = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-a80083ee"]]);

export { callback as default };
//# sourceMappingURL=callback-Cgci144N.mjs.map
