import { _ as __nuxt_component_0 } from './nuxt-link-DAFz7xX6.mjs';
import { _ as __nuxt_component_1$1 } from './LanguageToggle-iHF9iQqL.mjs';
import { useSSRContext, defineComponent, computed, mergeProps, unref, ref, withCtx, createVNode, createTextVNode, toDisplayString } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderSlot, ssrInterpolate, ssrRenderAttr, ssrRenderStyle, ssrRenderClass, ssrRenderList } from 'vue/server-renderer';
import { p as publicAssetsURL } from '../routes/renderer.mjs';
import { e as useRoute, K as useAuthStore, f as useRouter, B as useI18n, o as useAppConfig, c as useToast } from './server.mjs';
import { _ as __nuxt_component_0$1 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './ConfirmationDialog-C6YtqB9_.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:util';
import 'node:net';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './index-eP-xd45t.mjs';
import 'node:process';
import 'node:tty';
import './Button-3EsiVOgL.mjs';

const _imports_0 = publicAssetsURL("/images/logo/favicon.png");
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "MainMenu",
  __ssrInlineRender: true,
  setup(__props) {
    const authStore = useAuthStore();
    const router = useRouter();
    const showConfirmDialog = ref(false);
    const { t } = useI18n();
    const isTeacher = computed(() => {
      var _a;
      return (_a = authStore.user) == null ? void 0 : _a.teacher;
    });
    const isStudent = computed(() => {
      var _a;
      return (_a = authStore.user) == null ? void 0 : _a.student;
    });
    const accountPath = computed(() => {
      if (isTeacher.value) {
        return "/teacher/account";
      }
      if (isStudent.value) {
        return "/student/account";
      }
      return "/teacher/account";
    });
    async function handleLogout() {
      try {
        await authStore.logout();
        const toast = useToast();
        toast.add({
          title: t("messages.auth.logout.success.title"),
          description: t("messages.auth.logout.success.description"),
          color: "green",
          timeout: 3e3,
          icon: "i-heroicons-check-circle"
        });
        router.push("/");
      } catch (error) {
        console.error("Logout failed:", error);
      }
    }
    function onConfirm() {
      showConfirmDialog.value = false;
      handleLogout();
    }
    function onCancel() {
      showConfirmDialog.value = false;
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      const _component_UIcon = __nuxt_component_0$1;
      const _component_ConfirmationDialog = __nuxt_component_2;
      _push(`<!--[--><nav class="hidden md:flex items-center justify-between w-full px-4 py-2" data-v-51d102dd>`);
      if (unref(isTeacher)) {
        _push(`<div class="flex gap-8" data-v-51d102dd>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/teacher/schedule",
          class: "text-gray-700 hover:text-primary-600 transition-colors flex items-center gap-2 py-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-calendar-20-solid",
                class: "w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.menu.lessons"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-calendar-20-solid",
                  class: "w-5 h-5"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.menu.lessons")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/teacher/records",
          class: "text-gray-700 hover:text-primary-600 transition-colors flex items-center gap-2 py-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-presentation-chart-bar-20-solid",
                class: "w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.menu.done"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-presentation-chart-bar-20-solid",
                  class: "w-5 h-5"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.menu.done")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (unref(isStudent)) {
        _push(`<div class="flex gap-8" data-v-51d102dd>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/student/dashboard",
          class: "text-gray-700 hover:text-primary-600 transition-colors flex items-center gap-2 py-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-calendar-20-solid",
                class: "w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.menu.bookLesson"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-calendar-20-solid",
                  class: "w-5 h-5"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.menu.bookLesson")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/student/booked-courses",
          class: "text-gray-700 hover:text-primary-600 transition-colors flex items-center gap-2 py-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-academic-cap-20-solid",
                class: "w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(_ctx.$t("messages.menu.myLessons"))}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-academic-cap-20-solid",
                  class: "w-5 h-5"
                }),
                createTextVNode(" " + toDisplayString(_ctx.$t("messages.menu.myLessons")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (unref(authStore).isAuthenticated) {
        _push(`<div class="flex items-center gap-6" data-v-51d102dd>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: unref(accountPath),
          class: "text-gray-700 hover:text-primary-600 transition-colors flex items-center gap-2 py-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            var _a, _b;
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-user-20-solid",
                class: "w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate((_a = unref(authStore).user) == null ? void 0 : _a.username)}`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: "i-heroicons-user-20-solid",
                  class: "w-5 h-5"
                }),
                createTextVNode(" " + toDisplayString((_b = unref(authStore).user) == null ? void 0 : _b.username), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`<button class="text-gray-700 hover:text-red-600 transition-colors flex items-center gap-2 py-2" data-v-51d102dd>`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-right-on-rectangle",
          class: "w-5 h-5"
        }, null, _parent));
        _push(` ${ssrInterpolate(unref(t)("messages.auth.logout.button"))}</button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</nav>`);
      if (unref(showConfirmDialog)) {
        _push(ssrRenderComponent(_component_ConfirmationDialog, {
          title: unref(t)("messages.auth.logout.confirmTitle"),
          message: unref(t)("messages.auth.logout.confirmMessage"),
          visible: unref(showConfirmDialog),
          onConfirm,
          onCancel
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    };
  }
});
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/generalElements/MainMenu.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const MainMenu = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-51d102dd"]]);
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "AppHeader",
  __ssrInlineRender: true,
  setup(__props) {
    const { siteName } = useAppConfig();
    const authStore = useAuthStore();
    const route = useRoute();
    const linksDisabled = computed(() => route.path.startsWith("/signup"));
    const user = computed(() => authStore.user);
    const isTeacher = computed(() => {
      var _a;
      return (_a = user.value) == null ? void 0 : _a.teacher;
    });
    const isStudent = computed(() => {
      var _a;
      return (_a = user.value) == null ? void 0 : _a.student;
    });
    computed(() => {
      var _a, _b;
      if (isTeacher.value && ((_b = (_a = user.value) == null ? void 0 : _a.teacher) == null ? void 0 : _b.avatarUrl)) {
        return user.value.teacher.avatarUrl;
      }
      return "/images/logo/favicon.png";
    });
    computed(() => {
      if (isTeacher.value) {
        return "/teacher/account";
      }
      if (isStudent.value) {
        return "/student/account";
      }
      return "/teacher/account";
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      const _component_LanguageToggle = __nuxt_component_1$1;
      _push(`<header${ssrRenderAttrs(mergeProps({ class: "sticky top-0 md:static z-50 bg-white/90 supports-[backdrop-filter]:bg-white/70 backdrop-blur border-b border-gray-100" }, _attrs))}><div class="container mx-auto px-4 safe-area"><div class="flex items-center h-14 md:h-16"><div class="flex items-center gap-2 mx-auto lg:mx-0">`);
      if (!unref(linksDisabled)) {
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/",
          class: "flex items-center gap-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<img${ssrRenderAttr("src", _imports_0)} alt="logo" class="h-6 w-6 rounded"${_scopeId}><h1 class="text-lg sm:text-xl font-bold" style="${ssrRenderStyle({ "color": "#0098DB" })}"${_scopeId}>${ssrInterpolate(unref(siteName))}</h1>`);
            } else {
              return [
                createVNode("img", {
                  src: _imports_0,
                  alt: "logo",
                  class: "h-6 w-6 rounded"
                }),
                createVNode("h1", {
                  class: "text-lg sm:text-xl font-bold",
                  style: { "color": "#0098DB" }
                }, toDisplayString(unref(siteName)), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<div class="flex items-center gap-2 select-none cursor-default"><img${ssrRenderAttr("src", _imports_0)} alt="logo" class="h-6 w-6 rounded"><h1 class="text-lg sm:text-xl font-bold" style="${ssrRenderStyle({ "color": "#0098DB" })}">${ssrInterpolate(unref(siteName))}</h1></div>`);
      }
      _push(`</div><div class="hidden lg:flex flex-grow ml-6 items-center justify-between">`);
      _push(ssrRenderComponent(unref(MainMenu), null, null, _parent));
      _push(`<div class="ml-4 flex-shrink-0">`);
      _push(ssrRenderComponent(_component_LanguageToggle, { embedded: true }, null, _parent));
      _push(`</div></div><div class="lg:hidden absolute right-4 flex-shrink-0">`);
      _push(ssrRenderComponent(_component_LanguageToggle, { embedded: true }, null, _parent));
      _push(`</div></div></div></header>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/generalElements/AppHeader.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "BottomNav",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const route = useRoute();
    const authStore = useAuthStore();
    const user = computed(() => authStore.user);
    const isTeacher = computed(() => {
      var _a;
      return (_a = user.value) == null ? void 0 : _a.teacher;
    });
    const isStudent = computed(() => {
      var _a;
      return (_a = user.value) == null ? void 0 : _a.student;
    });
    const accountPath = computed(() => {
      if (isTeacher.value)
        return "/teacher/account";
      if (isStudent.value)
        return "/student/account";
      return "/teacher/account";
    });
    const items = computed(() => {
      if (isTeacher.value) {
        return [
          { to: "/teacher/schedule", label: t("messages.menu.bookLesson"), icon: "i-heroicons-calendar-20-solid" },
          { to: "/teacher/records", label: t("messages.menu.done"), icon: "i-heroicons-presentation-chart-bar-20-solid" },
          { to: accountPath.value, label: t("messages.menu.account"), icon: "i-heroicons-user-20-solid" }
        ];
      }
      if (isStudent.value) {
        return [
          { to: "/student/dashboard", label: t("messages.menu.bookLesson"), icon: "i-heroicons-calendar-20-solid" },
          { to: "/student/booked-courses", label: t("messages.menu.myLessons"), icon: "i-heroicons-academic-cap-20-solid" },
          { to: accountPath.value, label: t("messages.menu.account"), icon: "i-heroicons-user-20-solid" }
        ];
      }
      return [
        { to: "/join", label: t("messages.menu.bookLesson"), icon: "i-heroicons-calendar-20-solid" },
        { to: "/teacher", label: t("messages.menu.teacher"), icon: "i-heroicons-user-group-20-solid" },
        { to: accountPath.value, label: t("messages.menu.account"), icon: "i-heroicons-user-20-solid" }
      ];
    });
    const isActive = (to) => route.path === to || route.path.startsWith(`${to}/`);
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      const _component_UIcon = __nuxt_component_0$1;
      _push(`<nav${ssrRenderAttrs(mergeProps({ class: "fixed inset-x-0 bottom-0 z-50 border-t border-gray-100 bg-white/90 supports-[backdrop-filter]:bg-white/70 backdrop-blur safe-area" }, _attrs))} data-v-1da7a2eb><ul class="${ssrRenderClass([unref(items).length === 3 ? "grid-cols-3" : "grid-cols-4", "grid gap-1 h-16"])}" data-v-1da7a2eb><!--[-->`);
      ssrRenderList(unref(items), (item, i) => {
        _push(`<li data-v-1da7a2eb>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: item.to,
          class: ["flex flex-col items-center justify-center h-full text-xs", isActive(item.to) ? "text-primary" : "text-gray-500"]
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: item.icon,
                class: "w-5 h-5 mb-0.5"
              }, null, _parent2, _scopeId));
              _push2(`<span class="leading-none" data-v-1da7a2eb${_scopeId}>${ssrInterpolate(item.label)}</span>`);
            } else {
              return [
                createVNode(_component_UIcon, {
                  name: item.icon,
                  class: "w-5 h-5 mb-0.5"
                }, null, 8, ["name"]),
                createVNode("span", { class: "leading-none" }, toDisplayString(item.label), 1)
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(`</li>`);
      });
      _push(`<!--]--></ul></nav>`);
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/generalElements/BottomNav.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const __nuxt_component_1 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-1da7a2eb"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "default",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const shouldShowBottom = computed(() => {
      var _a;
      return ((_a = route.meta) == null ? void 0 : _a.showBottomNav) !== false;
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_AppHeader = _sfc_main$2;
      const _component_BottomNav = __nuxt_component_1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 flex flex-col" }, _attrs))}><div class="hidden md:block">`);
      _push(ssrRenderComponent(_component_AppHeader, null, null, _parent));
      _push(`</div><main class="flex-1 pb-16 md:pb-0">`);
      ssrRenderSlot(_ctx.$slots, "default", {}, null, _push, _parent);
      _push(`</main>`);
      if (unref(shouldShowBottom)) {
        _push(`<div class="md:hidden">`);
        _push(ssrRenderComponent(_component_BottomNav, null, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("layouts/default.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=default-CztzZ8wj.mjs.map
