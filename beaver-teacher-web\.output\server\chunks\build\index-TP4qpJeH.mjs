import { _ as __nuxt_component_2 } from './Form-CpNzGgY1.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { useSSRContext, defineComponent, ref, reactive, watch, mergeProps, unref, withCtx, createTextVNode, toDisplayString, createVNode, openBlock, createBlock, createCommentVNode, Fragment, renderList, withModifiers, isRef, nextTick } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderClass, ssrRenderList, ssrRenderAttr } from 'vue/server-renderer';
import { _ as __nuxt_component_2$1 } from './Button-3EsiVOgL.mjs';
import { B as useI18n, f as useRouter, c as useToast, N as request } from './server.mjs';
import { _ as __nuxt_component_10 } from './Textarea-Cu5vOIr8.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_0$1 } from './client-only-C3WHot0o.mjs';
import { c as checkTeacherEmail, s as submitTeacherAudit } from './teacher-BT-saGBd.mjs';
import '@vueuse/core';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './useFormGroup-B3564yef.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", file.type);
  return request({
    url: "/api/upload/file",
    method: "POST",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "UploadImage",
  __ssrInlineRender: true,
  props: {
    modelValue: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const imageUrl = ref("");
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "relative w-32 h-32 border-2 border-dashed rounded-lg cursor-pointer" }, _attrs))}><input type="file" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept="image/*">`);
      if (_ctx.modelValue) {
        _push(`<img${ssrRenderAttr("src", imageUrl.value)} class="w-full h-full object-cover rounded-lg">`);
      } else {
        _push(`<div class="flex items-center justify-center h-full">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-photo",
          class: "w-6 h-6 text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/UploadImage.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "UploadAudio",
  __ssrInlineRender: true,
  props: {
    modelValue: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    useI18n();
    ref();
    const audioUrl = ref("");
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2$1;
      const _component_UIcon = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(_attrs)}>`);
      _push(ssrRenderComponent(_component_UButton, {
        onClick: ($event) => _ctx.$refs.fileInput.click()
      }, {
        leading: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, { name: "i-heroicons-microphone" }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UIcon, { name: "i-heroicons-microphone" })
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` ${ssrInterpolate(_ctx.$t("messages.components.upload.audio.button"))}`);
          } else {
            return [
              createTextVNode(" " + toDisplayString(_ctx.$t("messages.components.upload.audio.button")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<input type="file" accept="audio/*" class="hidden">`);
      if (_ctx.modelValue) {
        _push(`<div class="mt-2"><audio controls${ssrRenderAttr("src", audioUrl.value)}></audio></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/UploadAudio.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const formRef = ref(null);
    const formData = reactive({
      realName: "",
      nickname: "",
      avatarKey: "",
      gender: 1,
      birthDate: "",
      email: "",
      phone: "",
      specialty: "",
      graduatedSchool: "",
      voiceKey: "",
      tags: []
    });
    const isCheckingEmail = ref(false);
    const emailMessage = ref("");
    const emailMessageType = ref(void 0);
    const checkEmail = async (email) => {
      emailMessage.value = "";
      emailMessageType.value = void 0;
      if (!email) {
        return;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        emailMessage.value = t("messages.signup.validation.emailInvalid");
        emailMessageType.value = "red";
        return;
      }
      try {
        isCheckingEmail.value = true;
        emailMessage.value = t("messages.signup.validation.emailChecking");
        const isRegistered = await checkTeacherEmail(email);
        if (isRegistered.status === 1) {
          emailMessage.value = t("messages.signup.validation.emailExists") + ' <a href="/signin" style="text-decoration-line: underline;">' + t("messages.signup.validation.emailExistsWithSignIn") + "</a>";
          emailMessageType.value = "red";
        } else if (isRegistered.status === 2) {
          emailMessage.value = t("messages.signup.validation.emailInReview");
          emailMessageType.value = "red";
        } else if (isRegistered.status === 0) {
          emailMessage.value = t("messages.signup.validation.emailAvailable");
          emailMessageType.value = "green";
        }
      } catch (error) {
        emailMessage.value = t("messages.signup.validation.emailCheckError");
        emailMessageType.value = "red";
      } finally {
        isCheckingEmail.value = false;
      }
    };
    watch(
      () => formData.email,
      (newEmail) => {
        if (!newEmail) {
          emailMessage.value = t("messages.signup.validation.emailRequired");
          emailMessageType.value = "red";
        }
      },
      { immediate: true }
    );
    const validateForm = (state) => {
      const errors = [];
      if (!state.realName)
        errors.push({ path: "realName", message: t("messages.signup.validation.realNameRequired") });
      if (!state.nickname)
        errors.push({ path: "nickname", message: t("messages.signup.validation.nicknameRequired") });
      if (!state.avatarKey)
        errors.push({ path: "avatarKey", message: t("messages.signup.validation.avatarRequired") });
      if (!state.email)
        errors.push({ path: "email", message: t("messages.signup.validation.emailRequired") });
      if (state.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(state.email)) {
        errors.push({ path: "email", message: t("messages.signup.validation.emailInvalid") });
      }
      if (!state.specialty)
        errors.push({ path: "specialty", message: t("messages.signup.validation.specialtyRequired") });
      else {
        const wordCount = state.specialty.trim().split(/\s+/).length;
        if (wordCount < 15) {
          errors.push({
            path: "specialty",
            message: t("messages.signup.validation.specialtyMinWords", { min: 15 })
          });
        }
      }
      if (!state.graduatedSchool)
        errors.push({
          path: "graduatedSchool",
          message: t("messages.signup.validation.graduatedSchoolRequired")
        });
      return errors;
    };
    const addTag = () => {
      formData.tags.push({
        id: "0",
        tagName: "",
        tagType: "certificate",
        tagContent: "",
        fileName: "",
        fileKey: "",
        fileUrl: "",
        fileSize: 0,
        remark: ""
      });
    };
    const removeTag = (index) => {
      formData.tags.splice(index, 1);
    };
    const router = useRouter();
    const isSubmitting = ref(false);
    const uploadingTags = reactive({});
    const uploadCertificateImage = async (index) => {
      currentUploadingIndex.value = index;
      const input = (void 0).createElement("input");
      input.type = "file";
      input.accept = "image/*";
      input.style.display = "none";
      input.onchange = async (e) => {
        const target = e.target;
        if (target.files && target.files[0]) {
          const file = target.files[0];
          try {
            uploadingTags[index] = true;
            const response = await uploadFile(file);
            formData.tags[index].fileKey = response.key;
            formData.tags[index].fileUrl = response.url;
            formData.tags[index].fileName = file.name;
            formData.tags[index].fileSize = file.size;
          } catch (error) {
            console.error("Upload failed:", error);
            const toast = useToast();
            toast.add({
              title: t("messages.upload.error.title"),
              description: t("messages.upload.error.description"),
              color: "red",
              timeout: 3e3
            });
          } finally {
            uploadingTags[index] = false;
          }
        }
        (void 0).body.removeChild(input);
      };
      (void 0).body.appendChild(input);
      input.click();
    };
    const removeCertificateImage = (index) => {
      formData.tags[index].fileKey = "";
      formData.tags[index].fileUrl = "";
      formData.tags[index].fileName = "";
      formData.tags[index].fileSize = 0;
    };
    const isPreviewOpen = ref(false);
    const previewImageUrl = ref("");
    const previewImage = (url) => {
      previewImageUrl.value = url;
      isPreviewOpen.value = true;
    };
    const scrollToField = (path) => {
      nextTick(() => {
        const firstErrorField = (void 0).querySelector(`[name="${path}"]`);
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      });
    };
    const handleSubmit = async () => {
      var _a, _b;
      const errors = await ((_b = (_a = formRef.value) == null ? void 0 : _a.validate) == null ? void 0 : _b.call(_a));
      if (errors && errors.length > 0) {
        scrollToField(errors[0].path);
        return;
      }
      if (isSubmitting.value)
        return;
      if (formData.email) {
        const isRegistered = await checkTeacherEmail(formData.email);
        if (isRegistered.status === 1) {
          emailMessage.value = t("messages.signup.validation.emailExists") + ' <a href="/signin" style="text-decoration-line: underline;">' + t("messages.signup.validation.emailExistsWithSignIn") + "</a>";
          emailMessageType.value = "red";
          return;
        }
        if (isRegistered.status === 2) {
          emailMessage.value = t("messages.signup.validation.emailInReview");
          emailMessageType.value = "red";
          return;
        }
      }
      try {
        isSubmitting.value = true;
        await submitTeacherAudit(formData);
        useToast().add({
          title: t("messages.signup.toast.success.title"),
          description: t("messages.signup.toast.success.description"),
          color: "green"
        });
        router.push({
          path: "/signup/success",
          query: {
            nickname: formData.nickname,
            email: formData.email
          }
        });
      } catch (error) {
        useToast().add({
          title: t("messages.signup.toast.error.title"),
          description: t("messages.signup.toast.error.description"),
          color: "red"
        });
      } finally {
        isSubmitting.value = false;
      }
    };
    ref(null);
    const currentUploadingIndex = ref(-1);
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UForm = __nuxt_component_2;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_UploadImage = _sfc_main$2;
      const _component_UploadAudio = _sfc_main$1;
      const _component_UTextarea = __nuxt_component_10;
      const _component_UButton = __nuxt_component_2$1;
      const _component_UIcon = __nuxt_component_0;
      const _component_UModal = __nuxt_component_6;
      const _component_ClientOnly = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 flex flex-col bg-gray-50" }, _attrs))}><div class="max-w-4xl mx-auto p-6 md:py-12 flex-1 w-full safe-area"><h1 class="text-2xl font-semibold mb-6 text-gray-800">${ssrInterpolate(_ctx.$t("messages.signup.title"))}</h1><div class="bg-white border border-gray-200 rounded-xl p-6 md:p-8">`);
      _push(ssrRenderComponent(_component_UForm, {
        ref_key: "formRef",
        ref: formRef,
        state: unref(formData),
        validate: validateForm,
        onSubmit: handleSubmit
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="grid grid-cols-1 gap-6"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.email"),
              name: "email",
              error: unref(emailMessageType) === "red" ? unref(emailMessage) : void 0
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.email"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.email")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              help: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  if (unref(emailMessage) && unref(emailMessageType) !== "red") {
                    _push3(`<p class="${ssrRenderClass({
                      "text-green-500": unref(emailMessageType) === "green",
                      "text-gray-500": unref(isCheckingEmail)
                    })}"${_scopeId2}>${ssrInterpolate(unref(emailMessage))}</p>`);
                  } else {
                    _push3(`<!---->`);
                  }
                } else {
                  return [
                    unref(emailMessage) && unref(emailMessageType) !== "red" ? (openBlock(), createBlock("p", {
                      key: 0,
                      class: {
                        "text-green-500": unref(emailMessageType) === "green",
                        "text-gray-500": unref(isCheckingEmail)
                      }
                    }, toDisplayString(unref(emailMessage)), 3)) : createCommentVNode("", true)
                  ];
                }
              }),
              error: withCtx((_2, _push3, _parent3, _scopeId2) => {
                var _a;
                if (_push3) {
                  if (unref(emailMessage) && unref(emailMessageType) === "red") {
                    _push3(`<p${_scopeId2}>${(_a = unref(emailMessage)) != null ? _a : ""}</p>`);
                  } else {
                    _push3(`<!---->`);
                  }
                } else {
                  return [
                    unref(emailMessage) && unref(emailMessageType) === "red" ? (openBlock(), createBlock("p", {
                      key: 0,
                      innerHTML: unref(emailMessage)
                    }, null, 8, ["innerHTML"])) : createCommentVNode("", true)
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(formData).email,
                    "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                    type: "email",
                    loading: unref(isCheckingEmail),
                    onBlur: ($event) => checkEmail(unref(formData).email),
                    color: unref(emailMessageType),
                    size: "lg"
                  }, null, _parent3, _scopeId2));
                  _push3(`<div size="sm" class="text-sm text-gray-500 mt-1"${_scopeId2}>${ssrInterpolate(_ctx.$t("messages.signup.form.emailHint"))}</div>`);
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).email,
                      "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                      type: "email",
                      loading: unref(isCheckingEmail),
                      onBlur: ($event) => checkEmail(unref(formData).email),
                      color: unref(emailMessageType),
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "loading", "onBlur", "color"]),
                    createVNode("div", {
                      size: "sm",
                      class: "text-sm text-gray-500 mt-1"
                    }, toDisplayString(_ctx.$t("messages.signup.form.emailHint")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.nickname"),
              name: "nickname"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.nickname"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.nickname")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(formData).nickname,
                    "onUpdate:modelValue": ($event) => unref(formData).nickname = $event,
                    size: "lg"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).nickname,
                      "onUpdate:modelValue": ($event) => unref(formData).nickname = $event,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.realName"),
              name: "realName"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.realName"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.realName")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(formData).realName,
                    "onUpdate:modelValue": ($event) => unref(formData).realName = $event,
                    size: "lg"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).realName,
                      "onUpdate:modelValue": ($event) => unref(formData).realName = $event,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.avatar"),
              name: "avatarKey"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.avatar"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.avatar")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UploadImage, {
                    modelValue: unref(formData).avatarKey,
                    "onUpdate:modelValue": ($event) => unref(formData).avatarKey = $event
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UploadImage, {
                      modelValue: unref(formData).avatarKey,
                      "onUpdate:modelValue": ($event) => unref(formData).avatarKey = $event
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.voiceIntro"),
              name: "voiceKey"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.voiceIntro"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.voiceIntro")), 1)
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UploadAudio, {
                    modelValue: unref(formData).voiceKey,
                    "onUpdate:modelValue": ($event) => unref(formData).voiceKey = $event
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UploadAudio, {
                      modelValue: unref(formData).voiceKey,
                      "onUpdate:modelValue": ($event) => unref(formData).voiceKey = $event
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.certificates"),
              name: "tags"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<!--[-->`);
                  ssrRenderList(unref(formData).tags, (tag, index) => {
                    _push3(`<div class="mb-6"${_scopeId2}><div class="space-y-4"${_scopeId2}><div class="space-y-2"${_scopeId2}>`);
                    _push3(ssrRenderComponent(_component_UInput, {
                      modelValue: tag.tagName,
                      "onUpdate:modelValue": ($event) => tag.tagName = $event,
                      placeholder: _ctx.$t("messages.signup.form.certificateName"),
                      size: "lg"
                    }, null, _parent3, _scopeId2));
                    _push3(`<div size="sm" class="text-sm text-gray-500 mt-1"${_scopeId2}>${ssrInterpolate(_ctx.$t("messages.signup.form.certificateNameHint"))}</div>`);
                    _push3(ssrRenderComponent(_component_UTextarea, {
                      modelValue: tag.tagContent,
                      "onUpdate:modelValue": ($event) => tag.tagContent = $event,
                      placeholder: _ctx.$t("messages.signup.form.certificateContent"),
                      rows: 2,
                      class: "min-h-[60px]",
                      size: "lg"
                    }, null, _parent3, _scopeId2));
                    _push3(`<div size="sm" class="text-sm text-gray-500 mt-1"${_scopeId2}>${ssrInterpolate(_ctx.$t("messages.signup.form.certificateContentHint"))}</div></div><div class="flex gap-4 items-center"${_scopeId2}><div class="flex-1"${_scopeId2}>`);
                    if (tag.fileKey) {
                      _push3(`<div class="relative w-[200px] h-[120px] group"${_scopeId2}><img${ssrRenderAttr("src", tag.fileUrl)} class="w-full h-full object-cover rounded-lg" alt="\u8BC1\u4E66\u56FE\u7247"${_scopeId2}><div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-lg"${_scopeId2}>`);
                      _push3(ssrRenderComponent(_component_UButton, {
                        color: "white",
                        variant: "solid",
                        icon: "i-heroicons-eye",
                        ui: { rounded: "rounded-full" },
                        onClick: ($event) => previewImage(tag.fileUrl)
                      }, null, _parent3, _scopeId2));
                      _push3(ssrRenderComponent(_component_UButton, {
                        color: "white",
                        variant: "solid",
                        icon: "i-heroicons-trash",
                        ui: { rounded: "rounded-full" },
                        onClick: ($event) => removeCertificateImage(index)
                      }, null, _parent3, _scopeId2));
                      _push3(`</div></div>`);
                    } else {
                      _push3(ssrRenderComponent(_component_UButton, {
                        size: "md",
                        color: "gray",
                        class: "flex items-center gap-2",
                        onClick: ($event) => uploadCertificateImage(index),
                        loading: unref(uploadingTags)[index],
                        disabled: unref(uploadingTags)[index]
                      }, {
                        default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                          if (_push4) {
                            _push4(ssrRenderComponent(_component_UIcon, {
                              name: "i-heroicons-photo",
                              class: "w-5 h-5"
                            }, null, _parent4, _scopeId3));
                            _push4(`<span${_scopeId3}>${ssrInterpolate(_ctx.$t("messages.components.upload.file.button"))}</span>`);
                          } else {
                            return [
                              createVNode(_component_UIcon, {
                                name: "i-heroicons-photo",
                                class: "w-5 h-5"
                              }),
                              createVNode("span", null, toDisplayString(_ctx.$t("messages.components.upload.file.button")), 1)
                            ];
                          }
                        }),
                        _: 2
                      }, _parent3, _scopeId2));
                    }
                    _push3(`</div>`);
                    _push3(ssrRenderComponent(_component_UButton, {
                      color: "red",
                      onClick: ($event) => removeTag(index)
                    }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(`${ssrInterpolate(_ctx.$t("messages.signup.form.delete"))}`);
                        } else {
                          return [
                            createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.delete")), 1)
                          ];
                        }
                      }),
                      _: 2
                    }, _parent3, _scopeId2));
                    _push3(`</div></div></div>`);
                  });
                  _push3(`<!--]-->`);
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "primary",
                    onClick: addTag,
                    size: "lg"
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate(_ctx.$t("messages.signup.form.addCertificate"))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.addCertificate")), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    (openBlock(true), createBlock(Fragment, null, renderList(unref(formData).tags, (tag, index) => {
                      return openBlock(), createBlock("div", {
                        key: index,
                        class: "mb-6"
                      }, [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("div", { class: "space-y-2" }, [
                            createVNode(_component_UInput, {
                              modelValue: tag.tagName,
                              "onUpdate:modelValue": ($event) => tag.tagName = $event,
                              placeholder: _ctx.$t("messages.signup.form.certificateName"),
                              size: "lg"
                            }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                            createVNode("div", {
                              size: "sm",
                              class: "text-sm text-gray-500 mt-1"
                            }, toDisplayString(_ctx.$t("messages.signup.form.certificateNameHint")), 1),
                            createVNode(_component_UTextarea, {
                              modelValue: tag.tagContent,
                              "onUpdate:modelValue": ($event) => tag.tagContent = $event,
                              placeholder: _ctx.$t("messages.signup.form.certificateContent"),
                              rows: 2,
                              class: "min-h-[60px]",
                              size: "lg"
                            }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                            createVNode("div", {
                              size: "sm",
                              class: "text-sm text-gray-500 mt-1"
                            }, toDisplayString(_ctx.$t("messages.signup.form.certificateContentHint")), 1)
                          ]),
                          createVNode("div", { class: "flex gap-4 items-center" }, [
                            createVNode("div", { class: "flex-1" }, [
                              tag.fileKey ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "relative w-[200px] h-[120px] group"
                              }, [
                                createVNode("img", {
                                  src: tag.fileUrl,
                                  class: "w-full h-full object-cover rounded-lg",
                                  alt: "\u8BC1\u4E66\u56FE\u7247"
                                }, null, 8, ["src"]),
                                createVNode("div", {
                                  class: "absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-lg",
                                  onClick: withModifiers(() => {
                                  }, ["stop"])
                                }, [
                                  createVNode(_component_UButton, {
                                    color: "white",
                                    variant: "solid",
                                    icon: "i-heroicons-eye",
                                    ui: { rounded: "rounded-full" },
                                    onClick: withModifiers(($event) => previewImage(tag.fileUrl), ["stop"])
                                  }, null, 8, ["onClick"]),
                                  createVNode(_component_UButton, {
                                    color: "white",
                                    variant: "solid",
                                    icon: "i-heroicons-trash",
                                    ui: { rounded: "rounded-full" },
                                    onClick: withModifiers(($event) => removeCertificateImage(index), ["stop"])
                                  }, null, 8, ["onClick"])
                                ], 8, ["onClick"])
                              ])) : (openBlock(), createBlock(_component_UButton, {
                                key: 1,
                                size: "md",
                                color: "gray",
                                class: "flex items-center gap-2",
                                onClick: ($event) => uploadCertificateImage(index),
                                loading: unref(uploadingTags)[index],
                                disabled: unref(uploadingTags)[index]
                              }, {
                                default: withCtx(() => [
                                  createVNode(_component_UIcon, {
                                    name: "i-heroicons-photo",
                                    class: "w-5 h-5"
                                  }),
                                  createVNode("span", null, toDisplayString(_ctx.$t("messages.components.upload.file.button")), 1)
                                ]),
                                _: 2
                              }, 1032, ["onClick", "loading", "disabled"]))
                            ]),
                            createVNode(_component_UButton, {
                              color: "red",
                              onClick: ($event) => removeTag(index)
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.delete")), 1)
                              ]),
                              _: 2
                            }, 1032, ["onClick"])
                          ])
                        ])
                      ]);
                    }), 128)),
                    createVNode(_component_UButton, {
                      color: "primary",
                      onClick: addTag,
                      size: "lg"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.addCertificate")), 1)
                      ]),
                      _: 1
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.graduatedSchool"),
              name: "graduatedSchool"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.graduatedSchool"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.graduatedSchool")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(formData).graduatedSchool,
                    "onUpdate:modelValue": ($event) => unref(formData).graduatedSchool = $event,
                    size: "lg"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).graduatedSchool,
                      "onUpdate:modelValue": ($event) => unref(formData).graduatedSchool = $event,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: _ctx.$t("messages.signup.form.specialty"),
              name: "specialty"
            }, {
              label: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.form.specialty"))} <span class="text-red-500"${_scopeId2}>*</span>`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.specialty")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UTextarea, {
                    modelValue: unref(formData).specialty,
                    "onUpdate:modelValue": ($event) => unref(formData).specialty = $event,
                    rows: 3,
                    size: "lg"
                  }, null, _parent3, _scopeId2));
                  _push3(`<div size="sm" class="text-sm text-gray-500 mt-1"${_scopeId2}>${ssrInterpolate(_ctx.$t("messages.signup.form.specialtyHint"))}</div>`);
                } else {
                  return [
                    createVNode(_component_UTextarea, {
                      modelValue: unref(formData).specialty,
                      "onUpdate:modelValue": ($event) => unref(formData).specialty = $event,
                      rows: 3,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                    createVNode("div", {
                      size: "sm",
                      class: "text-sm text-gray-500 mt-1"
                    }, toDisplayString(_ctx.$t("messages.signup.form.specialtyHint")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`<div class="flex justify-center mt-4"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UButton, {
              type: "submit",
              color: "primary",
              loading: unref(isSubmitting),
              disabled: unref(isSubmitting),
              size: "lg"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(_ctx.$t("messages.signup.submit"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.submit")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "grid grid-cols-1 gap-6" }, [
                createVNode(_component_UFormGroup, {
                  label: _ctx.$t("messages.signup.form.email"),
                  name: "email",
                  error: unref(emailMessageType) === "red" ? unref(emailMessage) : void 0
                }, {
                  label: withCtx(() => [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.email")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  help: withCtx(() => [
                    unref(emailMessage) && unref(emailMessageType) !== "red" ? (openBlock(), createBlock("p", {
                      key: 0,
                      class: {
                        "text-green-500": unref(emailMessageType) === "green",
                        "text-gray-500": unref(isCheckingEmail)
                      }
                    }, toDisplayString(unref(emailMessage)), 3)) : createCommentVNode("", true)
                  ]),
                  error: withCtx(() => [
                    unref(emailMessage) && unref(emailMessageType) === "red" ? (openBlock(), createBlock("p", {
                      key: 0,
                      innerHTML: unref(emailMessage)
                    }, null, 8, ["innerHTML"])) : createCommentVNode("", true)
                  ]),
                  default: withCtx(() => [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).email,
                      "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                      type: "email",
                      loading: unref(isCheckingEmail),
                      onBlur: ($event) => checkEmail(unref(formData).email),
                      color: unref(emailMessageType),
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "loading", "onBlur", "color"]),
                    createVNode("div", {
                      size: "sm",
                      class: "text-sm text-gray-500 mt-1"
                    }, toDisplayString(_ctx.$t("messages.signup.form.emailHint")), 1)
                  ]),
                  _: 1
                }, 8, ["label", "error"]),
                createVNode(_component_UFormGroup, {
                  label: _ctx.$t("messages.signup.form.nickname"),
                  name: "nickname"
                }, {
                  label: withCtx(() => [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.nickname")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  default: withCtx(() => [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).nickname,
                      "onUpdate:modelValue": ($event) => unref(formData).nickname = $event,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ]),
                  _: 1
                }, 8, ["label"]),
                createVNode(_component_UFormGroup, {
                  label: _ctx.$t("messages.signup.form.realName"),
                  name: "realName"
                }, {
                  label: withCtx(() => [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.realName")) + " ", 1),
                    createVNode("span", { class: "text-red-500" }, "*")
                  ]),
                  default: withCtx(() => [
                    createVNode(_component_UInput, {
                      modelValue: unref(formData).realName,
                      "onUpdate:modelValue": ($event) => unref(formData).realName = $event,
                      size: "lg"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ]),
                  _: 1
                }, 8, ["label"])
              ]),
              createVNode(_component_UFormGroup, {
                label: _ctx.$t("messages.signup.form.avatar"),
                name: "avatarKey"
              }, {
                label: withCtx(() => [
                  createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.avatar")) + " ", 1),
                  createVNode("span", { class: "text-red-500" }, "*")
                ]),
                default: withCtx(() => [
                  createVNode(_component_UploadImage, {
                    modelValue: unref(formData).avatarKey,
                    "onUpdate:modelValue": ($event) => unref(formData).avatarKey = $event
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: _ctx.$t("messages.signup.form.voiceIntro"),
                name: "voiceKey"
              }, {
                label: withCtx(() => [
                  createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.voiceIntro")), 1)
                ]),
                default: withCtx(() => [
                  createVNode(_component_UploadAudio, {
                    modelValue: unref(formData).voiceKey,
                    "onUpdate:modelValue": ($event) => unref(formData).voiceKey = $event
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: _ctx.$t("messages.signup.form.certificates"),
                name: "tags"
              }, {
                default: withCtx(() => [
                  (openBlock(true), createBlock(Fragment, null, renderList(unref(formData).tags, (tag, index) => {
                    return openBlock(), createBlock("div", {
                      key: index,
                      class: "mb-6"
                    }, [
                      createVNode("div", { class: "space-y-4" }, [
                        createVNode("div", { class: "space-y-2" }, [
                          createVNode(_component_UInput, {
                            modelValue: tag.tagName,
                            "onUpdate:modelValue": ($event) => tag.tagName = $event,
                            placeholder: _ctx.$t("messages.signup.form.certificateName"),
                            size: "lg"
                          }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                          createVNode("div", {
                            size: "sm",
                            class: "text-sm text-gray-500 mt-1"
                          }, toDisplayString(_ctx.$t("messages.signup.form.certificateNameHint")), 1),
                          createVNode(_component_UTextarea, {
                            modelValue: tag.tagContent,
                            "onUpdate:modelValue": ($event) => tag.tagContent = $event,
                            placeholder: _ctx.$t("messages.signup.form.certificateContent"),
                            rows: 2,
                            class: "min-h-[60px]",
                            size: "lg"
                          }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                          createVNode("div", {
                            size: "sm",
                            class: "text-sm text-gray-500 mt-1"
                          }, toDisplayString(_ctx.$t("messages.signup.form.certificateContentHint")), 1)
                        ]),
                        createVNode("div", { class: "flex gap-4 items-center" }, [
                          createVNode("div", { class: "flex-1" }, [
                            tag.fileKey ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "relative w-[200px] h-[120px] group"
                            }, [
                              createVNode("img", {
                                src: tag.fileUrl,
                                class: "w-full h-full object-cover rounded-lg",
                                alt: "\u8BC1\u4E66\u56FE\u7247"
                              }, null, 8, ["src"]),
                              createVNode("div", {
                                class: "absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-lg",
                                onClick: withModifiers(() => {
                                }, ["stop"])
                              }, [
                                createVNode(_component_UButton, {
                                  color: "white",
                                  variant: "solid",
                                  icon: "i-heroicons-eye",
                                  ui: { rounded: "rounded-full" },
                                  onClick: withModifiers(($event) => previewImage(tag.fileUrl), ["stop"])
                                }, null, 8, ["onClick"]),
                                createVNode(_component_UButton, {
                                  color: "white",
                                  variant: "solid",
                                  icon: "i-heroicons-trash",
                                  ui: { rounded: "rounded-full" },
                                  onClick: withModifiers(($event) => removeCertificateImage(index), ["stop"])
                                }, null, 8, ["onClick"])
                              ], 8, ["onClick"])
                            ])) : (openBlock(), createBlock(_component_UButton, {
                              key: 1,
                              size: "md",
                              color: "gray",
                              class: "flex items-center gap-2",
                              onClick: ($event) => uploadCertificateImage(index),
                              loading: unref(uploadingTags)[index],
                              disabled: unref(uploadingTags)[index]
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_UIcon, {
                                  name: "i-heroicons-photo",
                                  class: "w-5 h-5"
                                }),
                                createVNode("span", null, toDisplayString(_ctx.$t("messages.components.upload.file.button")), 1)
                              ]),
                              _: 2
                            }, 1032, ["onClick", "loading", "disabled"]))
                          ]),
                          createVNode(_component_UButton, {
                            color: "red",
                            onClick: ($event) => removeTag(index)
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.delete")), 1)
                            ]),
                            _: 2
                          }, 1032, ["onClick"])
                        ])
                      ])
                    ]);
                  }), 128)),
                  createVNode(_component_UButton, {
                    color: "primary",
                    onClick: addTag,
                    size: "lg"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.addCertificate")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: _ctx.$t("messages.signup.form.graduatedSchool"),
                name: "graduatedSchool"
              }, {
                label: withCtx(() => [
                  createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.graduatedSchool")) + " ", 1),
                  createVNode("span", { class: "text-red-500" }, "*")
                ]),
                default: withCtx(() => [
                  createVNode(_component_UInput, {
                    modelValue: unref(formData).graduatedSchool,
                    "onUpdate:modelValue": ($event) => unref(formData).graduatedSchool = $event,
                    size: "lg"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: _ctx.$t("messages.signup.form.specialty"),
                name: "specialty"
              }, {
                label: withCtx(() => [
                  createTextVNode(toDisplayString(_ctx.$t("messages.signup.form.specialty")) + " ", 1),
                  createVNode("span", { class: "text-red-500" }, "*")
                ]),
                default: withCtx(() => [
                  createVNode(_component_UTextarea, {
                    modelValue: unref(formData).specialty,
                    "onUpdate:modelValue": ($event) => unref(formData).specialty = $event,
                    rows: 3,
                    size: "lg"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("div", {
                    size: "sm",
                    class: "text-sm text-gray-500 mt-1"
                  }, toDisplayString(_ctx.$t("messages.signup.form.specialtyHint")), 1)
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode("div", { class: "flex justify-center mt-4" }, [
                createVNode(_component_UButton, {
                  type: "submit",
                  color: "primary",
                  loading: unref(isSubmitting),
                  disabled: unref(isSubmitting),
                  size: "lg"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(_ctx.$t("messages.signup.submit")), 1)
                  ]),
                  _: 1
                }, 8, ["loading", "disabled"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: unref(isPreviewOpen),
        "onUpdate:modelValue": ($event) => isRef(isPreviewOpen) ? isPreviewOpen.value = $event : null
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex items-center justify-center min-h-[300px]"${_scopeId}><img${ssrRenderAttr("src", unref(previewImageUrl))} alt="\u8BC1\u4E66\u56FE\u7247" class="max-w-full max-h-[80vh] object-contain"${_scopeId}></div>`);
          } else {
            return [
              createVNode("div", { class: "flex items-center justify-center min-h-[300px]" }, [
                createVNode("img", {
                  src: unref(previewImageUrl),
                  alt: "\u8BC1\u4E66\u56FE\u7247",
                  class: "max-w-full max-h-[80vh] object-contain"
                }, null, 8, ["src"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/signup/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-TP4qpJeH.mjs.map
