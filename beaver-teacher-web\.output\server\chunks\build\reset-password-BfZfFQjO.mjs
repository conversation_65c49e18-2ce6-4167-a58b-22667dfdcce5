import { _ as __nuxt_component_0 } from './index-eP-xd45t.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, toDisplayString, isRef, createVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { B as useI18n, c as useToast, f as useRouter, e as useRoute } from './server.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './useFormGroup-B3564yef.mjs';
import '@vueuse/core';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "reset-password",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    useToast();
    const router = useRouter();
    const route = useRoute();
    const password = ref("");
    const confirmPassword = ref("");
    const loading = ref(false);
    const isSuccess = ref(false);
    const errorMessage = ref("");
    const isTokenValid = ref(false);
    const isTokenChecking = ref(true);
    const token = route.query.token;
    if (!token) {
      router.push("/forgot-password");
    }
    function goToLogin() {
      router.push("/signin");
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Icon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 bg-gray-50 flex flex-col" }, _attrs))}><div class="container mx-auto px-4 py-6 md:py-12 flex-1 safe-area"><div class="max-w-md mx-auto w-full">`);
      if (unref(isTokenChecking)) {
        _push(`<div class="text-center"><div class="mb-6"><div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">`);
        _push(ssrRenderComponent(_component_Icon, {
          name: "fa6-solid:spinner",
          class: "w-8 h-8 text-blue-600 animate-spin"
        }, null, _parent));
        _push(`</div><h1 class="text-xl font-bold mb-2 text-blue-800">${ssrInterpolate(unref(t)("messages.resetPassword.verifying.title"))}</h1><p class="text-gray-600">${ssrInterpolate(unref(t)("messages.resetPassword.verifying.description"))}</p></div></div>`);
      } else if (!unref(isTokenValid)) {
        _push(`<div class="text-center"><div class="mb-6"><div class="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">`);
        _push(ssrRenderComponent(_component_Icon, {
          name: "fa6-solid:triangle-exclamation",
          class: "w-8 h-8 text-red-600"
        }, null, _parent));
        _push(`</div><h1 class="text-xl font-bold mb-2 text-red-800">${ssrInterpolate(unref(t)("messages.resetPassword.invalid.title"))}</h1><p class="text-gray-600 mb-6">${ssrInterpolate(unref(errorMessage) || unref(t)("messages.resetPassword.invalid.description"))}</p></div><div class="space-y-3">`);
        _push(ssrRenderComponent(_component_UButton, {
          to: "/forgot-password",
          block: "",
          size: "lg",
          color: "blue"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.resetPassword.invalid.requestNewLink"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.resetPassword.invalid.requestNewLink")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          to: "/signin",
          variant: "outline",
          block: "",
          size: "lg"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.resetPassword.invalid.backToLogin"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.resetPassword.invalid.backToLogin")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else if (unref(isSuccess)) {
        _push(`<div class="text-center"><div class="mb-6"><div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">`);
        _push(ssrRenderComponent(_component_Icon, {
          name: "fa6-solid:circle-check",
          class: "w-8 h-8 text-green-600"
        }, null, _parent));
        _push(`</div><h1 class="text-xl font-bold mb-2 text-green-800">${ssrInterpolate(unref(t)("messages.resetPassword.success.title"))}</h1><p class="text-gray-600 mb-6">${ssrInterpolate(unref(t)("messages.resetPassword.success.description"))}</p></div>`);
        _push(ssrRenderComponent(_component_UButton, {
          onClick: goToLogin,
          block: "",
          size: "lg",
          color: "blue"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.resetPassword.success.goToLogin"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.resetPassword.success.goToLogin")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else if (unref(isTokenValid) && !unref(isSuccess)) {
        _push(`<div><div class="text-center mb-8"><h1 class="text-xl font-bold mb-2">${ssrInterpolate(unref(t)("messages.resetPassword.title"))}</h1><p class="text-gray-600">${ssrInterpolate(unref(t)("messages.resetPassword.subtitle"))}</p></div><div class="bg-white p-6 rounded-xl border border-gray-200"><form class="space-y-4">`);
        _push(ssrRenderComponent(_component_UFormGroup, {
          label: unref(t)("messages.resetPassword.form.newPassword"),
          error: unref(errorMessage)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UInput, {
                modelValue: unref(password),
                "onUpdate:modelValue": ($event) => isRef(password) ? password.value = $event : null,
                type: "password",
                placeholder: unref(t)("messages.resetPassword.form.newPasswordPlaceholder"),
                error: !!unref(errorMessage)
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_UInput, {
                  modelValue: unref(password),
                  "onUpdate:modelValue": ($event) => isRef(password) ? password.value = $event : null,
                  type: "password",
                  placeholder: unref(t)("messages.resetPassword.form.newPasswordPlaceholder"),
                  error: !!unref(errorMessage)
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"])
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UFormGroup, {
          label: unref(t)("messages.resetPassword.form.confirmPassword")
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(_component_UInput, {
                modelValue: unref(confirmPassword),
                "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                type: "password",
                placeholder: unref(t)("messages.resetPassword.form.confirmPasswordPlaceholder")
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(_component_UInput, {
                  modelValue: unref(confirmPassword),
                  "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                  type: "password",
                  placeholder: unref(t)("messages.resetPassword.form.confirmPasswordPlaceholder")
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_UButton, {
          type: "submit",
          block: "",
          size: "lg",
          loading: unref(loading)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.resetPassword.submit"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.resetPassword.submit")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</form></div><div class="text-center mt-6">`);
        _push(ssrRenderComponent(_component_UButton, {
          variant: "link",
          color: "gray",
          to: "/signin"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(unref(t)("messages.resetPassword.backToLogin"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(unref(t)("messages.resetPassword.backToLogin")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/reset-password.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=reset-password-BfZfFQjO.mjs.map
