'use strict';

const { detachNodeFromParent } = require('../lib/xast.js');

exports.name = 'removeDesc';
exports.description = 'removes <desc>';

const standardDescs = /^(Created with|Created using)/;

/**
 * Removes <desc>.
 * Removes only standard editors content or empty elements 'cause it can be used for accessibility.
 * Enable parameter 'removeAny' to remove any description.
 *
 * https://developer.mozilla.org/docs/Web/SVG/Element/desc
 *
 * <AUTHOR>
 *
 * @type {import('./plugins-types').Plugin<'removeDesc'>}
 */
exports.fn = (root, params) => {
  const { removeAny = false } = params;
  return {
    element: {
      enter: (node, parentNode) => {
        if (node.name === 'desc') {
          if (
            removeAny ||
            node.children.length === 0 ||
            (node.children[0].type === 'text' &&
              standardDescs.test(node.children[0].value))
          ) {
            detachNodeFromParent(node, parentNode);
          }
        }
      },
    },
  };
};
