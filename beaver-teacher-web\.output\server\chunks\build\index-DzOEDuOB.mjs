import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { defineComponent, ref, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderList, ssrRenderComponent } from 'vue/server-renderer';
import { B as useI18n } from './server.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const cardDetails = ref([]);
    const loading = ref(false);
    const getStatusInfo = (status) => {
      const { t } = useI18n();
      switch (status) {
        case 0:
          return { text: t("messages.transactions.status.expired"), color: "red" };
        case 1:
          return { text: t("messages.transactions.status.active"), color: "green" };
        case 2:
          return { text: t("messages.transactions.status.exhausted"), color: "red" };
        case 3:
          return { text: t("messages.transactions.status.refunded"), color: "red" };
        default:
          return { text: t("messages.transactions.status.unknown"), color: "gray" };
      }
    };
    const formatDate = (date) => {
      return new Date(date).toLocaleString("zh-CN");
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UBadge = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.transactions.title"))}</h1></div><div class="max-w-4xl mx-auto px-4 md:px-6 pt-[calc(48px+env(safe-area-inset-top))] md:pt-6 pb-6">`);
      if (unref(loading)) {
        _push(`<div><div class="bg-white rounded-xl p-6 border border-gray-200 animate-pulse"><div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div><div class="h-4 bg-gray-200 rounded w-1/4 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div><div class="space-y-2"><div class="h-4 bg-gray-200 rounded w-full"></div><div class="h-4 bg-gray-200 rounded w-full"></div><div class="h-4 bg-gray-200 rounded w-full"></div></div></div></div>`);
      } else if (unref(cardDetails).length > 0) {
        _push(`<div class="space-y-4"><!--[-->`);
        ssrRenderList(unref(cardDetails), (card) => {
          _push(`<div class="bg-white rounded-xl p-6 border border-gray-200"><div class="flex justify-between items-start mb-4"><div><h3 class="text-lg font-bold">${ssrInterpolate(card.totalTimes)}\u6B21\u5361 <span class="text-gray-500">(${ssrInterpolate(_ctx.$t("messages.transactions.card.available"))}: ${ssrInterpolate(card.remainingTimes)}${ssrInterpolate(_ctx.$t("messages.transactions.card.timesUnit"))})</span></h3><p class="text-gray-500 text-sm mt-1">${ssrInterpolate(_ctx.$t("messages.transactions.card.cardNumber"))}: ${ssrInterpolate(card.cardNumber)}</p><p class="text-gray-500 text-sm mt-1">${ssrInterpolate(_ctx.$t("messages.transactions.card.purchase"))}: ${ssrInterpolate(formatDate(card.purchaseTime))}</p><p class="text-gray-500 text-sm mt-1">${ssrInterpolate(_ctx.$t("messages.transactions.card.validUntil"))}: ${ssrInterpolate(formatDate(card.endTime))}</p></div>`);
          _push(ssrRenderComponent(_component_UBadge, {
            color: getStatusInfo(card.status).color,
            variant: "solid"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(getStatusInfo(card.status).text)}`);
              } else {
                return [
                  createTextVNode(toDisplayString(getStatusInfo(card.status).text), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div>`);
          if (card.usageRecords && card.usageRecords.length > 0) {
            _push(`<div class="mt-4"><h4 class="font-medium mb-2">${ssrInterpolate(_ctx.$t("messages.transactions.card.usageRecords"))}:</h4><!--[-->`);
            ssrRenderList(card.usageRecords, (record) => {
              _push(`<div class="text-sm text-gray-600 mb-1">${ssrInterpolate(record.sequence)} - ${ssrInterpolate(formatDate(record.classTime))} - ${ssrInterpolate(record.teacherName)}\u7684${ssrInterpolate(record.classType || _ctx.$t("messages.transactions.card.courseType"))}</div>`);
            });
            _push(`<!--]--></div>`);
          } else {
            _push(`<div class="mt-4 text-sm text-gray-500">${ssrInterpolate(_ctx.$t("messages.transactions.card.noUsageRecords"))}</div>`);
          }
          _push(`</div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<div class="text-center py-12">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-ticket",
          class: "w-16 h-16 mx-auto text-gray-400"
        }, null, _parent));
        _push(`<p class="text-gray-500 mt-4">${ssrInterpolate(_ctx.$t("messages.transactions.card.noCards"))}</p></div>`);
      }
      _push(`</div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/transactions/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-DzOEDuOB.mjs.map
