import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_3 } from './Select-8rIeiWNF.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_4 } from './Pagination-JPpl4AYq.mjs';
import { defineComponent, ref, computed, watch, unref, isRef, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { g as getTeacherScheduleRecords } from './teacher-BT-saGBd.mjs';
import { B as useI18n, f as useRouter, K as useAuthStore, c as useToast } from './server.mjs';
import { j as getNow, f as formatDate, k as calculateDuration } from './datetime-BvKd-1hF.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './useFormGroup-B3564yef.mjs';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    useRouter();
    useAuthStore();
    const toast = useToast();
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const loading = ref(false);
    const year = ref(getNow().year());
    const month = ref(getNow().month() + 1);
    const currentYear = getNow().year();
    const yearOptions = computed(() => {
      const range = [];
      for (let y = currentYear - 5; y <= currentYear + 1; y++)
        range.push(y);
      return range.map((y) => ({ label: String(y), value: y }));
    });
    const monthOptions = computed(() => Array.from({ length: 12 }, (_, i) => i + 1).map((m) => ({ label: String(m).padStart(2, "0"), value: m })));
    const records = ref([]);
    const teacherId = ref("");
    const formatDateTime = (date, time) => {
      return `${formatDate(date, "YYYY-MM-DD")} ${time}`;
    };
    const calculateDuration$1 = (startTime, endTime) => {
      return calculateDuration(startTime, endTime);
    };
    const maskPhoneNumber = (phone) => {
      if (!phone)
        return "";
      return `****${phone.slice(-4)}`;
    };
    const fetchRecords = async () => {
      if (!teacherId.value)
        return;
      try {
        loading.value = true;
        const startDate = `${year.value}-${month.value.toString().padStart(2, "0")}-01`;
        const lastDay = getNow().set("year", year.value).set("month", month.value - 1).endOf("month").date();
        const endDate = `${year.value}-${month.value.toString().padStart(2, "0")}-${lastDay}`;
        const response = await getTeacherScheduleRecords(teacherId.value, {
          teacherId: teacherId.value,
          startDate,
          endDate,
          page: currentPage.value,
          pageSize: pageSize.value
        });
        records.value = response.list;
        total.value = response.totalCount;
      } catch (error) {
        toast.add({
          title: t("messages.records.toast.fetchError.title"),
          description: t("messages.records.toast.fetchError.description"),
          color: "red",
          timeout: 3e3,
          icon: "i-heroicons-x-circle"
        });
      } finally {
        loading.value = false;
      }
    };
    watch([year, month], () => {
      fetchRecords();
    });
    const goPrevMonth = () => {
      if (month.value === 1) {
        month.value = 12;
        year.value = year.value - 1;
      } else {
        month.value = month.value - 1;
      }
      currentPage.value = 1;
    };
    const goNextMonth = () => {
      if (month.value === 12) {
        month.value = 1;
        year.value = year.value + 1;
      } else {
        month.value = month.value + 1;
      }
      currentPage.value = 1;
    };
    watch(currentPage, () => {
      fetchRecords();
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      const _component_USelect = __nuxt_component_3;
      const _component_UBadge = __nuxt_component_1;
      const _component_UPagination = __nuxt_component_4;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(_ctx.$t("messages.menu.done"))}</h1></div><div class="container mx-auto px-0 md:px-4 pt-[calc(48px+env(safe-area-inset-top))] md:pt-8 pb-0 flex-1 safe-area"><div class="max-w-4xl mx-auto w-full py-4 sm:py-6"><div class="bg-white rounded-xl border border-gray-200 p-4 sm:p-6"><div class="hidden sm:flex items-center justify-between mb-4 pb-3 border-b border-gray-100"><h2 class="text-lg font-semibold">${ssrInterpolate(_ctx.$t("messages.menu.done"))}</h2>`);
      if (unref(loading)) {
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "w-4 h-4 animate-spin text-gray-400"
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="flex flex-wrap items-center gap-3 sm:gap-4 mb-4 sm:mb-6">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-chevron-left",
        variant: "ghost",
        color: "gray",
        size: "sm",
        onClick: goPrevMonth
      }, null, _parent));
      _push(ssrRenderComponent(_component_USelect, {
        modelValue: unref(year),
        "onUpdate:modelValue": ($event) => isRef(year) ? year.value = $event : null,
        options: unref(yearOptions),
        size: "sm",
        class: "w-[120px]"
      }, null, _parent));
      _push(ssrRenderComponent(_component_USelect, {
        modelValue: unref(month),
        "onUpdate:modelValue": ($event) => isRef(month) ? month.value = $event : null,
        options: unref(monthOptions),
        size: "sm",
        class: "w-[110px]"
      }, null, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-chevron-right",
        variant: "ghost",
        color: "gray",
        size: "sm",
        onClick: goNextMonth
      }, null, _parent));
      if (unref(loading)) {
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "sm:hidden w-4 h-4 animate-spin text-gray-400 ml-auto"
        }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="text-gray-600 text-sm mb-4">${ssrInterpolate(unref(year))}-${ssrInterpolate(unref(month).toString().padStart(2, "0"))}: ${ssrInterpolate(unref(total))} in total (UTC+8) </div>`);
      if (unref(records).length > 0) {
        _push(`<div><div class="hidden sm:block overflow-x-auto"><table class="w-full border-collapse text-sm"><thead><tr class="border-b"><th class="text-left py-2">\u2116</th><th class="text-left py-2">Time</th><th class="text-left py-2">Len. (min.)</th><th class="text-left py-2">Course Type</th><th class="text-left py-2">Student</th></tr></thead><tbody><!--[-->`);
        ssrRenderList(unref(records), (record, i) => {
          _push(`<tr class="border-b"><td class="py-2">${ssrInterpolate((unref(currentPage) - 1) * unref(pageSize) + i + 1)}</td><td class="py-2">${ssrInterpolate(formatDateTime(record.classDate, record.startTime))}</td><td class="py-2">${ssrInterpolate(calculateDuration$1(record.startTime, record.endTime))}</td><td class="py-2">${ssrInterpolate(record.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? _ctx.$t("messages.schedule.modal.form.courseTypes.oneToOne") : _ctx.$t("messages.schedule.modal.form.courseTypes.group"))}</td><td class="py-2">${ssrInterpolate(record.student.map((s) => `${s.nickname || "-"} (${maskPhoneNumber(s.phone || "")})`).join(" | ") || "-")}</td></tr>`);
        });
        _push(`<!--]--></tbody></table></div><div class="sm:hidden space-y-3"><!--[-->`);
        ssrRenderList(unref(records), (record) => {
          _push(`<div class="p-3 rounded-lg border border-gray-200 bg-gray-50"><div class="flex items-center justify-between gap-2"><div class="text-sm font-medium text-gray-900">${ssrInterpolate(formatDateTime(record.classDate, record.startTime))}</div>`);
          _push(ssrRenderComponent(_component_UBadge, {
            size: "xs",
            color: "blue",
            variant: "subtle"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(record.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? _ctx.$t("messages.schedule.modal.form.courseTypes.oneToOne") : _ctx.$t("messages.schedule.modal.form.courseTypes.group"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(record.classType === "\u4E00\u5BF9\u4E00\u8BFE\u7A0B" ? _ctx.$t("messages.schedule.modal.form.courseTypes.oneToOne") : _ctx.$t("messages.schedule.modal.form.courseTypes.group")), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div><div class="mt-1 text-xs text-gray-500">Len: ${ssrInterpolate(calculateDuration$1(record.startTime, record.endTime))} min</div><div class="mt-1 text-xs text-gray-600 break-words">${ssrInterpolate(record.student.map((s) => `${s.nickname || "-"} (${maskPhoneNumber(s.phone || "")})`).join(" | ") || "-")}</div></div>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<div class="text-center py-10 text-gray-500">${ssrInterpolate(_ctx.$t("messages.records.noData"))}</div>`);
      }
      if (unref(total) > unref(pageSize)) {
        _push(`<div class="mt-6 flex justify-center">`);
        _push(ssrRenderComponent(_component_UPagination, {
          modelValue: unref(currentPage),
          "onUpdate:modelValue": ($event) => isRef(currentPage) ? currentPage.value = $event : null,
          total: unref(total),
          "page-size": unref(pageSize),
          ui: { rounded: "rounded-full" }
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/records/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-CJjKYXL8.mjs.map
