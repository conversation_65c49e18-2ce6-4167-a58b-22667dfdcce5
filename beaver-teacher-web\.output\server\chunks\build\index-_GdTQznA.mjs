import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { c as chatWebSocketService, S as SessionStatus, W as WebSocketStatus, a as customerApi, M as MessageContentType, _ as _sfc_main$2, b as _sfc_main$1, d as _sfc_main$3 } from './websocket-0ZWWGGvI.mjs';
import { _ as __nuxt_component_6 } from './Modal-Bm5oOPTL.mjs';
import { _ as __nuxt_component_7 } from './Card-DSOtZzuw.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_10 } from './Textarea-Cu5vOIr8.mjs';
import { defineComponent, ref, computed, watch, mergeProps, unref, withCtx, createTextVNode, createVNode, useSSRContext, nextTick } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList } from 'vue/server-renderer';
import { B as useI18n, K as useAuthStore, c as useToast } from './server.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './Avatar-Cjyb3Ij_.mjs';
import './datetime-BvKd-1hF.mjs';
import 'dayjs';
import 'dayjs/plugin/utc.js';
import 'dayjs/plugin/timezone.js';
import './interval-gl53xdpR.mjs';
import './useFormGroup-B3564yef.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    useI18n();
    const authStore = useAuthStore();
    const toast = useToast();
    const loading = ref(false);
    const sending = ref(false);
    const sessions = ref([]);
    const messages = ref([]);
    const currentSessionId = ref(null);
    const showNewSession = ref(false);
    const newSessionTitle = ref("");
    const initialMessage = ref("");
    const wsStatus = computed(() => chatWebSocketService.status.value);
    const creatingSession = ref(false);
    const authChecked = ref(false);
    ref(0);
    const currentSession = computed(() => {
      if (!currentSessionId.value)
        return null;
      return sessions.value.find((s) => s.id === currentSessionId.value) || null;
    });
    const isSessionClosed = computed(() => {
      var _a;
      return ((_a = currentSession.value) == null ? void 0 : _a.status) === SessionStatus.CLOSED;
    });
    const fetchSessions = async () => {
      if (!isUserLoggedIn.value) {
        toast.add({
          title: "\u8BF7\u5148\u767B\u5F55",
          description: "\u9700\u8981\u767B\u5F55\u624D\u80FD\u8BBF\u95EE\u5BA2\u670D\u529F\u80FD",
          color: "orange"
        });
        return;
      }
      try {
        loading.value = true;
        const result = await customerApi.getMySessionList();
        sessions.value = result;
        if (sessions.value.length > 0 && !currentSessionId.value) {
          selectSession(sessions.value[0].id);
        }
      } catch (error) {
        console.error("\u83B7\u53D6\u4F1A\u8BDD\u5217\u8868\u5931\u8D25", error);
        toast.add({
          title: "\u83B7\u53D6\u4F1A\u8BDD\u5217\u8868\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red"
        });
      } finally {
        loading.value = false;
      }
    };
    const fetchMessages = async () => {
      if (!currentSessionId.value)
        return;
      try {
        loading.value = true;
        const result = await customerApi.getSessionMessages(currentSessionId.value);
        messages.value = result;
        if (currentSession.value && currentSession.value.unreadCount > 0) {
          markSessionRead();
        }
        await nextTick();
        scrollToBottom();
      } catch (error) {
        console.error("\u83B7\u53D6\u6D88\u606F\u5931\u8D25", error);
        toast.add({
          title: "\u83B7\u53D6\u6D88\u606F\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red"
        });
      } finally {
        loading.value = false;
      }
    };
    const selectSession = (sessionId) => {
      currentSessionId.value = sessionId;
      fetchMessages();
    };
    const createSession = async () => {
      if (!newSessionTitle.value.trim() || !initialMessage.value.trim()) {
        toast.add({
          title: "\u8BF7\u586B\u5199\u5B8C\u6574\u4FE1\u606F",
          description: "\u4F1A\u8BDD\u6807\u9898\u548C\u521D\u59CB\u6D88\u606F\u4E0D\u80FD\u4E3A\u7A7A",
          color: "orange"
        });
        return;
      }
      try {
        creatingSession.value = true;
        const sessionData = {
          sessionTitle: newSessionTitle.value.trim(),
          initialMessage: {
            receiverId: 1,
            // 固定为1，系统客服ID
            content: initialMessage.value.trim(),
            contentType: MessageContentType.TEXT
          }
        };
        const sessionId = await customerApi.createSession(sessionData);
        await fetchSessions();
        selectSession(sessionId);
        newSessionTitle.value = "";
        initialMessage.value = "";
        showNewSession.value = false;
        toast.add({
          title: "\u4F1A\u8BDD\u521B\u5EFA\u6210\u529F",
          color: "green"
        });
      } catch (error) {
        console.error("\u521B\u5EFA\u4F1A\u8BDD\u5931\u8D25", error);
        toast.add({
          title: "\u521B\u5EFA\u4F1A\u8BDD\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red"
        });
      } finally {
        creatingSession.value = false;
      }
    };
    const sendMessage = async (content, contentType = MessageContentType.TEXT) => {
      var _a, _b, _c;
      if (!currentSessionId.value || !content || isSessionClosed.value)
        return;
      try {
        sending.value = true;
        const messageData = {
          sessionId: currentSessionId.value,
          receiverId: 1,
          // 固定为1，系统客服ID
          content,
          contentType
        };
        await customerApi.sendMessage(messageData);
        const userId = (_a = authStore.user) == null ? void 0 : _a.id;
        console.debug("\u51C6\u5907\u6DFB\u52A0\u4E34\u65F6\u6D88\u606F\uFF0C\u7528\u6237\u4FE1\u606F:", {
          userId,
          username: (_b = authStore.user) == null ? void 0 : _b.username
        });
        const senderId = userId ? Number(userId) : 0;
        const tempMessage = {
          id: Date.now(),
          // 临时ID
          sessionId: currentSessionId.value,
          senderId,
          senderType: 0,
          // 0-用户，1-客服
          senderName: ((_c = authStore.user) == null ? void 0 : _c.username) || "\u6211",
          senderAvatar: "",
          // 暂无用户头像
          content,
          contentType,
          isRead: 0,
          readTime: null,
          createdTime: (/* @__PURE__ */ new Date()).toISOString()
        };
        console.debug("\u6DFB\u52A0\u4E34\u65F6\u6D88\u606F:", tempMessage);
        messages.value.push(tempMessage);
        await nextTick();
        scrollToBottom();
      } catch (error) {
        console.error("\u53D1\u9001\u6D88\u606F\u5931\u8D25", error);
        toast.add({
          title: "\u53D1\u9001\u6D88\u606F\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red"
        });
      } finally {
        sending.value = false;
      }
    };
    const markSessionRead = async () => {
      if (!currentSessionId.value)
        return;
      try {
        await customerApi.markSessionAllRead(currentSessionId.value);
        const session = sessions.value.find((s) => s.id === currentSessionId.value);
        if (session) {
          session.unreadCount = 0;
        }
      } catch (error) {
        console.error("\u6807\u8BB0\u6D88\u606F\u5DF2\u8BFB\u5931\u8D25", error);
      }
    };
    const scrollToBottom = () => {
      const messagesContainer = (void 0).querySelector(".messages-container");
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    };
    const isUserLoggedIn = computed(() => {
      return authStore.isAuthenticated;
    });
    const initWebSocketWithRetry = () => {
      var _a;
      console.debug("WebSocket\u521D\u59CB\u5316\u68C0\u67E5:", {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        userId: (_a = authStore.user) == null ? void 0 : _a.id
      });
      if (!authStore.token) {
        console.debug("WebSocket\u521D\u59CB\u5316\u5931\u8D25: token\u4E0D\u5B58\u5728\uFF0C\u53EF\u80FD\u9700\u8981\u91CD\u65B0\u767B\u5F55");
        toast.add({
          title: "WebSocket\u8FDE\u63A5\u5F02\u5E38",
          description: "\u767B\u5F55\u72B6\u6001\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55",
          color: "red"
        });
        return;
      }
      if (!authStore.isAuthenticated || !authStore.user || !authStore.user.id) {
        console.debug("WebSocket\u521D\u59CB\u5316\u5931\u8D25: \u7528\u6237\u4FE1\u606F\u4E0D\u5B8C\u6574");
        return;
      }
      console.debug("\u5F00\u59CB\u521D\u59CB\u5316WebSocket\u8FDE\u63A5:", {
        userId: authStore.user.id,
        hasToken: !!authStore.token
      });
      chatWebSocketService.disconnect();
      chatWebSocketService.onMessage((message) => {
        var _a2, _b, _c, _d, _e, _f, _g, _h;
        console.debug("\u6536\u5230WebSocket\u6D88\u606F:", {
          id: message.id,
          sessionId: message.sessionId,
          senderId: message.senderId,
          senderType: message.senderType,
          senderName: message.senderName,
          currentUserId: (_a2 = authStore.user) == null ? void 0 : _a2.id
        });
        if (message.sessionId === currentSessionId.value) {
          const exists = messages.value.some((m) => m.id === message.id);
          if (!exists) {
            const currentUserId = (_b = authStore.user) == null ? void 0 : _b.id;
            if (currentUserId && message.senderId) {
              const isCurrentUserMessage = currentUserId.toString().substring(0, 15) === message.senderId.toString().substring(0, 15);
              if (isCurrentUserMessage) {
                message.senderType = 0;
              }
            }
            messages.value.push(message);
            nextTick(() => scrollToBottom());
            const isSelfMessage = message.senderId.toString().substring(0, 15) === (((_e = (_d = (_c = authStore.user) == null ? void 0 : _c.id) == null ? void 0 : _d.toString()) == null ? void 0 : _e.substring(0, 15)) || "");
            if (!isSelfMessage) {
              markSessionRead();
            }
          }
        }
        const sessionIndex = sessions.value.findIndex((s) => s.id === message.sessionId);
        if (sessionIndex >= 0) {
          const session = { ...sessions.value[sessionIndex] };
          session.lastMessage = message.content;
          session.lastMessageTime = message.createdTime;
          const isSelfMessage = message.senderId.toString().substring(0, 15) === (((_h = (_g = (_f = authStore.user) == null ? void 0 : _f.id) == null ? void 0 : _g.toString()) == null ? void 0 : _h.substring(0, 15)) || "");
          if (message.sessionId !== currentSessionId.value && !isSelfMessage) {
            session.unreadCount = (session.unreadCount || 0) + 1;
          }
          sessions.value.splice(sessionIndex, 1);
          sessions.value.unshift(session);
        } else {
          fetchSessions();
        }
      });
      chatWebSocketService.onStatusChange((status) => {
        if (status === WebSocketStatus.CLOSED || status === WebSocketStatus.ERROR) {
          console.log("WebSocket\u5DF2\u65AD\u5F00\u6216\u51FA\u9519\uFF0C\u5C1D\u8BD5\u91CD\u8FDE");
        }
      });
      `/beaver/websocket/chat?token=${authStore.token}&userId=${authStore.user.id.toString()}&userType=user`;
      chatWebSocketService.connect(
        authStore.token || "",
        authStore.user.id.toString() || ""
      );
    };
    watch(() => authStore.isAuthenticated, (isAuthenticated) => {
      if (isAuthenticated && authStore.user && authStore.user.id) {
        fetchSessions();
        initWebSocketWithRetry();
      }
    });
    watch(currentSessionId, (newId) => {
      if (newId) {
        fetchMessages();
      } else {
        messages.value = [];
      }
    });
    const openNewSessionDialog = () => {
      showNewSession.value = true;
      newSessionTitle.value = "\u54A8\u8BE2\u95EE\u9898";
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_UBadge = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      const _component_ChatSessionItem = _sfc_main$2;
      const _component_ChatMessage = _sfc_main$1;
      const _component_ChatInputBox = _sfc_main$3;
      const _component_UModal = __nuxt_component_6;
      const _component_UCard = __nuxt_component_7;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_UTextarea = __nuxt_component_10;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "max-w-6xl mx-auto p-6" }, _attrs))}><div class="flex items-center justify-between mb-6"><div class="flex items-center">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        to: "/teacher/account"
      }, null, _parent));
      _push(`<h1 class="text-xl font-bold ml-2">${ssrInterpolate(_ctx.$t("messages.common.customerService.title"))}</h1></div><div>`);
      if (wsStatus.value === unref(WebSocketStatus).OPEN) {
        _push(ssrRenderComponent(_component_UBadge, {
          color: "green",
          variant: "subtle",
          class: "mr-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u5728\u7EBF `);
            } else {
              return [
                createTextVNode(" \u5728\u7EBF ")
              ];
            }
          }),
          _: 1
        }, _parent));
      } else if (wsStatus.value === unref(WebSocketStatus).CONNECTING || wsStatus.value === unref(WebSocketStatus).RECONNECTING) {
        _push(ssrRenderComponent(_component_UBadge, {
          color: "yellow",
          variant: "subtle",
          class: "mr-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u8FDE\u63A5\u4E2D `);
            } else {
              return [
                createTextVNode(" \u8FDE\u63A5\u4E2D ")
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(ssrRenderComponent(_component_UBadge, {
          color: "red",
          variant: "subtle",
          class: "mr-2"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u79BB\u7EBF `);
            } else {
              return [
                createTextVNode(" \u79BB\u7EBF ")
              ];
            }
          }),
          _: 1
        }, _parent));
      }
      _push(ssrRenderComponent(_component_UButton, {
        color: "primary",
        onClick: openNewSessionDialog,
        loading: creatingSession.value
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u65B0\u5EFA\u4F1A\u8BDD `);
          } else {
            return [
              createTextVNode(" \u65B0\u5EFA\u4F1A\u8BDD ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div>`);
      if (!authChecked.value) {
        _push(`<div class="flex justify-center items-center h-96">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin text-3xl text-gray-400"
        }, null, _parent));
        _push(`<span class="ml-2 text-gray-500">\u8BF7\u7A0D\u5019...</span></div>`);
      } else if (loading.value && sessions.value.length === 0) {
        _push(`<div class="flex justify-center items-center h-96">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-arrow-path",
          class: "animate-spin text-3xl text-gray-400"
        }, null, _parent));
        _push(`</div>`);
      } else if (!isUserLoggedIn.value) {
        _push(`<div class="text-center py-12">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-lock-closed",
          class: "w-16 h-16 mx-auto text-gray-400"
        }, null, _parent));
        _push(`<h2 class="text-xl font-bold text-gray-600 mt-4">\u8BF7\u5148\u767B\u5F55</h2><p class="text-gray-500 mt-2">\u60A8\u9700\u8981\u767B\u5F55\u624D\u80FD\u4F7F\u7528\u5728\u7EBF\u5BA2\u670D\u529F\u80FD</p>`);
        _push(ssrRenderComponent(_component_UButton, {
          to: "/login",
          color: "primary",
          class: "mt-4"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u53BB\u767B\u5F55 `);
            } else {
              return [
                createTextVNode(" \u53BB\u767B\u5F55 ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<div class="flex h-[calc(100vh-200px)] bg-white rounded-lg overflow-hidden shadow"><div class="w-80 border-r flex flex-col"><div class="p-3 border-b"><h2 class="font-medium">\u4F1A\u8BDD\u5217\u8868</h2></div><div class="flex-1 overflow-y-auto">`);
        if (sessions.value.length === 0) {
          _push(`<div class="p-4 text-center text-gray-500"> \u6682\u65E0\u4F1A\u8BDD\u8BB0\u5F55 </div>`);
        } else {
          _push(`<!--[-->`);
          ssrRenderList(sessions.value, (session) => {
            _push(ssrRenderComponent(_component_ChatSessionItem, {
              key: session.id,
              session,
              active: session.id === currentSessionId.value,
              onSelect: ($event) => selectSession(session.id)
            }, null, _parent));
          });
          _push(`<!--]-->`);
        }
        _push(`</div></div><div class="flex-1 flex flex-col">`);
        if (currentSession.value) {
          _push(`<div class="p-3 border-b"><div class="flex justify-between items-center"><h3 class="font-medium">${ssrInterpolate(currentSession.value.sessionTitle)}</h3>`);
          if (isSessionClosed.value) {
            _push(ssrRenderComponent(_component_UBadge, {
              color: "gray",
              size: "sm"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(`\u5DF2\u5173\u95ED`);
                } else {
                  return [
                    createTextVNode("\u5DF2\u5173\u95ED")
                  ];
                }
              }),
              _: 1
            }, _parent));
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        } else {
          _push(`<div class="p-3 border-b"><h3 class="font-medium">\u9009\u62E9\u6216\u521B\u5EFA\u4F1A\u8BDD</h3></div>`);
        }
        _push(`<div class="flex-1 overflow-y-auto p-4 messages-container">`);
        if (!currentSessionId.value) {
          _push(`<div class="h-full flex flex-col items-center justify-center text-gray-500">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-chat-bubble-left-right",
            class: "w-20 h-20 text-gray-300"
          }, null, _parent));
          _push(`<p class="mt-4">\u9009\u62E9\u4E00\u4E2A\u4F1A\u8BDD\u6216\u521B\u5EFA\u65B0\u4F1A\u8BDD\u5F00\u59CB\u804A\u5929</p>`);
          _push(ssrRenderComponent(_component_UButton, {
            color: "primary",
            variant: "soft",
            class: "mt-4",
            onClick: openNewSessionDialog,
            loading: creatingSession.value
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(` \u65B0\u5EFA\u4F1A\u8BDD `);
              } else {
                return [
                  createTextVNode(" \u65B0\u5EFA\u4F1A\u8BDD ")
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        } else if (messages.value.length === 0 && loading.value) {
          _push(`<div class="flex justify-center py-12">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-arrow-path",
            class: "animate-spin text-3xl text-gray-400"
          }, null, _parent));
          _push(`</div>`);
        } else if (messages.value.length === 0) {
          _push(`<div class="flex justify-center py-12 text-gray-500"> \u6682\u65E0\u6D88\u606F\u8BB0\u5F55 </div>`);
        } else {
          _push(`<!--[-->`);
          ssrRenderList(messages.value, (message) => {
            _push(ssrRenderComponent(_component_ChatMessage, {
              key: message.id,
              message
            }, null, _parent));
          });
          _push(`<!--]-->`);
        }
        _push(`</div>`);
        if (currentSessionId.value) {
          _push(ssrRenderComponent(_component_ChatInputBox, {
            sessionId: currentSessionId.value,
            disabled: isSessionClosed.value || sending.value,
            onSend: sendMessage
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      }
      _push(ssrRenderComponent(_component_UModal, {
        modelValue: showNewSession.value,
        "onUpdate:modelValue": ($event) => showNewSession.value = $event,
        ui: { width: "md:max-w-lg" }
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UCard, null, {
              header: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex items-center justify-between"${_scopeId2}><h3 class="text-lg font-medium"${_scopeId2}>\u65B0\u5EFA\u5BA2\u670D\u4F1A\u8BDD</h3>`);
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "gray",
                    variant: "ghost",
                    icon: "i-heroicons-x-mark",
                    class: "-my-1",
                    onClick: ($event) => showNewSession.value = false
                  }, null, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex items-center justify-between" }, [
                      createVNode("h3", { class: "text-lg font-medium" }, "\u65B0\u5EFA\u5BA2\u670D\u4F1A\u8BDD"),
                      createVNode(_component_UButton, {
                        color: "gray",
                        variant: "ghost",
                        icon: "i-heroicons-x-mark",
                        class: "-my-1",
                        onClick: ($event) => showNewSession.value = false
                      }, null, 8, ["onClick"])
                    ])
                  ];
                }
              }),
              footer: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex justify-end gap-2"${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "gray",
                    variant: "soft",
                    onClick: ($event) => showNewSession.value = false
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(` \u53D6\u6D88 `);
                      } else {
                        return [
                          createTextVNode(" \u53D6\u6D88 ")
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent(_component_UButton, {
                    color: "primary",
                    loading: creatingSession.value,
                    disabled: !newSessionTitle.value.trim() || !initialMessage.value.trim() || creatingSession.value,
                    onClick: createSession
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(` \u521B\u5EFA\u4F1A\u8BDD `);
                      } else {
                        return [
                          createTextVNode(" \u521B\u5EFA\u4F1A\u8BDD ")
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex justify-end gap-2" }, [
                      createVNode(_component_UButton, {
                        color: "gray",
                        variant: "soft",
                        onClick: ($event) => showNewSession.value = false
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" \u53D6\u6D88 ")
                        ]),
                        _: 1
                      }, 8, ["onClick"]),
                      createVNode(_component_UButton, {
                        color: "primary",
                        loading: creatingSession.value,
                        disabled: !newSessionTitle.value.trim() || !initialMessage.value.trim() || creatingSession.value,
                        onClick: createSession
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" \u521B\u5EFA\u4F1A\u8BDD ")
                        ]),
                        _: 1
                      }, 8, ["loading", "disabled"])
                    ])
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="space-y-4"${_scopeId2}><div${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UFormGroup, { label: "\u4F1A\u8BDD\u6807\u9898" }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_UInput, {
                          modelValue: newSessionTitle.value,
                          "onUpdate:modelValue": ($event) => newSessionTitle.value = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u4F1A\u8BDD\u6807\u9898\uFF0C\u5982\uFF1A\u8BFE\u7A0B\u54A8\u8BE2"
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_UInput, {
                            modelValue: newSessionTitle.value,
                            "onUpdate:modelValue": ($event) => newSessionTitle.value = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u4F1A\u8BDD\u6807\u9898\uFF0C\u5982\uFF1A\u8BFE\u7A0B\u54A8\u8BE2"
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div><div${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UFormGroup, { label: "\u521D\u59CB\u6D88\u606F" }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_UTextarea, {
                          modelValue: initialMessage.value,
                          "onUpdate:modelValue": ($event) => initialMessage.value = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u60A8\u60F3\u54A8\u8BE2\u7684\u95EE\u9898",
                          rows: 4,
                          class: "resize-none"
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_UTextarea, {
                            modelValue: initialMessage.value,
                            "onUpdate:modelValue": ($event) => initialMessage.value = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u60A8\u60F3\u54A8\u8BE2\u7684\u95EE\u9898",
                            rows: 4,
                            class: "resize-none"
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "space-y-4" }, [
                      createVNode("div", null, [
                        createVNode(_component_UFormGroup, { label: "\u4F1A\u8BDD\u6807\u9898" }, {
                          default: withCtx(() => [
                            createVNode(_component_UInput, {
                              modelValue: newSessionTitle.value,
                              "onUpdate:modelValue": ($event) => newSessionTitle.value = $event,
                              placeholder: "\u8BF7\u8F93\u5165\u4F1A\u8BDD\u6807\u9898\uFF0C\u5982\uFF1A\u8BFE\u7A0B\u54A8\u8BE2"
                            }, null, 8, ["modelValue", "onUpdate:modelValue"])
                          ]),
                          _: 1
                        })
                      ]),
                      createVNode("div", null, [
                        createVNode(_component_UFormGroup, { label: "\u521D\u59CB\u6D88\u606F" }, {
                          default: withCtx(() => [
                            createVNode(_component_UTextarea, {
                              modelValue: initialMessage.value,
                              "onUpdate:modelValue": ($event) => initialMessage.value = $event,
                              placeholder: "\u8BF7\u8F93\u5165\u60A8\u60F3\u54A8\u8BE2\u7684\u95EE\u9898",
                              rows: 4,
                              class: "resize-none"
                            }, null, 8, ["modelValue", "onUpdate:modelValue"])
                          ]),
                          _: 1
                        })
                      ])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UCard, null, {
                header: withCtx(() => [
                  createVNode("div", { class: "flex items-center justify-between" }, [
                    createVNode("h3", { class: "text-lg font-medium" }, "\u65B0\u5EFA\u5BA2\u670D\u4F1A\u8BDD"),
                    createVNode(_component_UButton, {
                      color: "gray",
                      variant: "ghost",
                      icon: "i-heroicons-x-mark",
                      class: "-my-1",
                      onClick: ($event) => showNewSession.value = false
                    }, null, 8, ["onClick"])
                  ])
                ]),
                footer: withCtx(() => [
                  createVNode("div", { class: "flex justify-end gap-2" }, [
                    createVNode(_component_UButton, {
                      color: "gray",
                      variant: "soft",
                      onClick: ($event) => showNewSession.value = false
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" \u53D6\u6D88 ")
                      ]),
                      _: 1
                    }, 8, ["onClick"]),
                    createVNode(_component_UButton, {
                      color: "primary",
                      loading: creatingSession.value,
                      disabled: !newSessionTitle.value.trim() || !initialMessage.value.trim() || creatingSession.value,
                      onClick: createSession
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" \u521B\u5EFA\u4F1A\u8BDD ")
                      ]),
                      _: 1
                    }, 8, ["loading", "disabled"])
                  ])
                ]),
                default: withCtx(() => [
                  createVNode("div", { class: "space-y-4" }, [
                    createVNode("div", null, [
                      createVNode(_component_UFormGroup, { label: "\u4F1A\u8BDD\u6807\u9898" }, {
                        default: withCtx(() => [
                          createVNode(_component_UInput, {
                            modelValue: newSessionTitle.value,
                            "onUpdate:modelValue": ($event) => newSessionTitle.value = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u4F1A\u8BDD\u6807\u9898\uFF0C\u5982\uFF1A\u8BFE\u7A0B\u54A8\u8BE2"
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ]),
                        _: 1
                      })
                    ]),
                    createVNode("div", null, [
                      createVNode(_component_UFormGroup, { label: "\u521D\u59CB\u6D88\u606F" }, {
                        default: withCtx(() => [
                          createVNode(_component_UTextarea, {
                            modelValue: initialMessage.value,
                            "onUpdate:modelValue": ($event) => initialMessage.value = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u60A8\u60F3\u54A8\u8BE2\u7684\u95EE\u9898",
                            rows: 4,
                            class: "resize-none"
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ]),
                        _: 1
                      })
                    ])
                  ])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/service/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-_GdTQznA.mjs.map
