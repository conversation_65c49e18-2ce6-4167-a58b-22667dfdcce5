import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_2$1 } from './AppFooter-DaKEVHRU.mjs';
import { defineComponent, mergeProps, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { e as useRoute } from './server.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "success",
  __ssrInlineRender: true,
  setup(__props) {
    useRoute();
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      const _component_AppFooter = __nuxt_component_2$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 bg-gray-50 flex flex-col" }, _attrs))}><div class="max-w-2xl mx-auto p-8 md:py-12 text-center flex-1 safe-area"><div class="mb-6">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-check-circle",
        class: "w-20 h-20 text-green-500 mx-auto"
      }, null, _parent));
      _push(`</div><h1 class="text-xl font-bold mb-4">${ssrInterpolate(_ctx.$t("messages.signup.success.title"))}</h1><p class="text-gray-600 mb-8">${ssrInterpolate(_ctx.$t("messages.signup.success.thx"))}${ssrInterpolate(_ctx.$route.query.nickname)}</p><p class="text-gray-600 mb-8">${(_a = _ctx.$t("messages.signup.success.description").replace("\n", "<br>")) != null ? _a : ""}</p><p class="text-gray-600 mb-8">\uFF08${ssrInterpolate(_ctx.$route.query.email)}\uFF09</p><div class="space-x-4">`);
      _push(ssrRenderComponent(_component_UButton, {
        to: "/",
        color: "gray"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("messages.signup.success.backHome"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("messages.signup.success.backHome")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div>`);
      _push(ssrRenderComponent(_component_AppFooter, null, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/signup/success.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=success-D6JpCLwJ.mjs.map
