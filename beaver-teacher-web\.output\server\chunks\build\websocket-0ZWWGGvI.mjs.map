{"version": 3, "file": "websocket-0ZWWGGvI.mjs", "sources": ["../../../../src/types/chat.ts", "../../../../src/components/chat/ChatSessionItem.vue", "../../../../src/components/chat/ChatMessage.vue", "../../../../src/components/chat/ChatInputBox.vue", "../../../../src/api/customer.ts", "../../../../src/utils/websocket.ts"], "sourcesContent": null, "names": ["MessageContentType", "SessionStatus", "WebSocketStatus"], "mappings": ";;;;;;;;;;;;;;AA2DY,IAAA,kBAAA,qBAAAA,mBAAL,KAAA;AACLA,EAAAA,mBAAAA,CAAAA,mBAAA,CAAA,MAAO,CAAA,GAAA,CAAP,CAAA,GAAA,MAAA;AACAA,EAAAA,mBAAAA,CAAAA,mBAAA,CAAA,OAAQ,CAAA,GAAA,CAAR,CAAA,GAAA,OAAA;AACAA,EAAAA,mBAAAA,CAAAA,mBAAA,CAAA,MAAO,CAAA,GAAA,CAAP,CAAA,GAAA,MAAA;AACAA,EAAAA,mBAAAA,CAAAA,mBAAA,CAAA,QAAS,CAAA,GAAA,CAAT,CAAA,GAAA,QAAA;AAJUA,EAAAA,OAAAA,mBAAAA;AAAA,CAAA,EAAA,kBAAA,IAAA,EAAA;AAQA,IAAA,aAAA,qBAAAC,cAAL,KAAA;AACLA,EAAAA,cAAAA,CAAAA,cAAA,CAAA,QAAS,CAAA,GAAA,CAAT,CAAA,GAAA,QAAA;AACAA,EAAAA,cAAAA,CAAAA,cAAA,CAAA,QAAS,CAAA,GAAA,CAAT,CAAA,GAAA,QAAA;AAFUA,EAAAA,OAAAA,cAAAA;AAAA,CAAA,EAAA,aAAA,IAAA,EAAA;;;;;;;;;;AC7DZ,IAAA,MAAM,KAAQ,GAAA,OAAA;AAUR,IAAA,MAAA,iBAAA,GAAoB,SAAS,MAAM;AACvC,MAAI,IAAA,CAAC,MAAM,OAAQ,CAAA,eAAA;AAAwB,QAAA,OAAA,EAAA;AAEpC,MAAA,OAAA,kBAAA,CAAmB,KAAM,CAAA,OAAA,CAAQ,eAAe,CAAA;AAAA,KACxD,CAAA;AAGD,IAAA,MAAM,WAAW,QAAS,CAAA,MAAM,MAAM,OAAQ,CAAA,MAAA,KAAW,cAAc,MAAM,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChB7E,IAAA,MAAM,KAAQ,GAAA,OAAA;AAId,IAAA,MAAM,YAAY,YAAa,EAAA;AAGzB,IAAA,MAAA,oBAAA,GAAuB,SAAS,MAAM;;AAC1C,MAAA,MAAM,aAAgB,GAAA,CAAA,CAAA,EAAA,GAAA,CAAU,EAAA,GAAA,SAAA,CAAA,IAAV,KAAA,IAAA,GAAgB,KAAA,CAAA,GAAA,EAAA,CAAA,EAAhB,KAAA,IAAA,eAAoB,UAAc,KAAA,EAAA;AACxD,MAAA,MAAM,eAAkB,GAAA,KAAA,CAAM,OAAQ,CAAA,QAAA,CAAS,QAAS,EAAA;AAIxD,MAAA,MAAM,mBAAsB,GAAA,aAAA,CAAc,SAAU,CAAA,CAAA,EAAG,EAAE,CAAA;AACzD,MAAA,MAAM,qBAAwB,GAAA,eAAA,CAAgB,SAAU,CAAA,CAAA,EAAG,EAAE,CAAA;AAE7D,MAAA,MAAM,SAAS,qBAA0B,KAAA,mBAAA;AAGzC,MAAA,OAAA,CAAQ,MAAM,kEAAkB,EAAA;AAAA,QAC9B,SAAA,EAAW,MAAM,OAAQ,CAAA,EAAA;AAAA,QACzB,UAAA,EAAY,MAAM,OAAQ,CAAA,UAAA;AAAA,QAC1B,eAAA;AAAA,QACA,aAAA;AAAA,QACA,qBAAA;AAAA,QACA,mBAAA;AAAA,QACA,aAAe,EAAA,MAAA;AAAA,QACf,OAAS,EAAA,KAAA,CAAM,OAAQ,CAAA,OAAA,CAAQ,SAAU,CAAA,CAAA,EAAG,EAAE,CAAA,IAAK,KAAM,CAAA,OAAA,CAAQ,OAAQ,CAAA,MAAA,GAAS,KAAK,KAAQ,GAAA,EAAA;AAAA,OAChG,CAAA;AAEM,MAAA,OAAA,MAAA;AAAA,KACR,CAAA;AAGK,IAAA,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAA,OAAO,MAAM,OAAQ,CAAA,WAAA;AAAA,KACtB,CAAA;AAGK,IAAA,MAAA,aAAA,GAAgB,SAAS,MAAM;AAC5B,MAAA,OAAA,UAAA,CAAW,KAAM,CAAA,OAAA,CAAQ,WAAW,CAAA;AAAA,KAC5C,CAAA;AAGK,IAAA,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAA,IAAI,qBAAqB,KAAO,EAAA;AACvB,QAAA,OAAA,QAAA;AAAA,OACF,MAAA;AACE,QAAA,OAAA,KAAA,CAAM,QAAQ,UAAc,IAAA,cAAA;AAAA;AAAA,KAEtC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpDD,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,IAAO,GAAA,MAAA;AAKU,IAAA,QAAA,EAAA;AACjB,IAAA,MAAA,OAAA,GAAU,IAAI,EAAE,CAAA;AAChB,IAAA,MAAA,WAAA,GAAc,IAAI,KAAK,CAAA;AAG7B,IAAA,MAAM,cAAc,MAAM;AACxB,MAAA,IAAI,CAAC,OAAA,CAAQ,KAAM,CAAA,IAAA,MAAU,KAAM,CAAA,QAAA;AAAU,QAAA;AAE7C,MAAA,IAAA,CAAK,QAAQ,OAAQ,CAAA,KAAA,CAAM,IAAK,EAAA,EAAG,mBAAmB,IAAI,CAAA;AAC1D,MAAA,OAAA,CAAQ,KAAQ,GAAA,EAAA;AAAA,KAClB;AAGM,IAAA,MAAA,SAAA,GAAY,CAAC,CAAqB,KAAA;AACtC,MAAA,IAAI,CAAE,CAAA,GAAA,KAAQ,OAAW,IAAA,CAAC,EAAE,QAAU,EAAA;AACpC,QAAA,CAAA,CAAE,cAAe,EAAA;AACL,QAAA,WAAA,EAAA;AAAA,OACP,MAAA;AACL,QAAA,IAAA,CAAK,QAAQ,CAAA;AAAA;AAAA,KAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1BO,MAAM,WAAc,GAAA;AAAA;AAAA;AAAA,EAIzB,MAAM,cAAc,IAA6C,EAAA;AAC/D,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,MAAA;AAAA,MACR,GAAK,EAAA,wBAAA;AAAA,MACL;AAAA,KACD,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,iBAAiB,SAA6C,EAAA;AAClE,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAA,EAAK,0BAA0B,SAAS,CAAA;AAAA,KACzC,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,gBAA+C,GAAA;AACnD,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAK,EAAA;AAAA,KACN,CAAA;AAAA,GACH;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,IAAuC,EAAA;AACvD,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,MAAA;AAAA,MACR,GAAK,EAAA,wBAAA;AAAA,MACL;AAAA,KACD,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,mBAAmB,SAA+C,EAAA;AACtE,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAA,EAAK,0BAA0B,SAAS,CAAA,SAAA;AAAA,KACzC,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,sBAAA,CAAuB,SAAmB,EAAA,IAAA,EAAc,QAAsD,EAAA;AAClH,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAA,EAAK,0BAA0B,SAAS,CAAA,cAAA,CAAA;AAAA,MACxC,MAAA,EAAQ,EAAE,IAAA,EAAM,QAAS;AAAA,KAC1B,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,gBAAgB,SAAqC,EAAA;AACzD,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAA,EAAK,0BAA0B,SAAS,CAAA,KAAA;AAAA,KACzC,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,mBAAmB,SAAqC,EAAA;AAC5D,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAA,EAAK,0BAA0B,SAAS,CAAA,KAAA;AAAA,KACzC,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,MAAM,cAAkC,GAAA;AACtC,IAAA,OAAO,MAAM,OAAQ,CAAA;AAAA,MACnB,MAAQ,EAAA,KAAA;AAAA,MACR,GAAK,EAAA;AAAA,KACN,CAAA;AAAA;AAEL;AC1EY,IAAA,eAAA,qBAAAC,gBAAL,KAAA;AACLA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,YAAa,CAAA,GAAA,CAAb,CAAA,GAAA,YAAA;AACAA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,MAAO,CAAA,GAAA,CAAP,CAAA,GAAA,MAAA;AACAA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,SAAU,CAAA,GAAA,CAAV,CAAA,GAAA,SAAA;AACAA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,QAAS,CAAA,GAAA,CAAT,CAAA,GAAA,QAAA;AACAA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,cAAe,CAAA,GAAA,CAAf,CAAA,GAAA,cAAA;AACAA,EAAAA,gBAAAA,CAAAA,gBAAA,CAAA,OAAQ,CAAA,GAAA,CAAR,CAAA,GAAA,OAAA;AANUA,EAAAA,OAAAA,gBAAAA;AAAA,CAAA,EAAA,eAAA,IAAA,EAAA;AAYL,MAAM,oBAAqB,CAAA;AAAA,EAgBhC,WAAc,GAAA;AAfqB,IAAA,aAAA,CAAA,IAAA,EAAA,UAAA,IAAA,CAAA;AACC,IAAA,aAAA,CAAA,IAAA,EAAA,qBAAA,GAAA,CAAA;AACA,IAAA,aAAA,CAAA,IAAA,EAAA,qBAAA,CAAA,CAAA;AACG,IAAA,aAAA,CAAA,IAAA,EAAA,wBAAA,CAAA,CAAA;AACD,IAAA,aAAA,CAAA,IAAA,EAAA,gBAAA,IAAA,CAAA;AACqB,IAAA,aAAA,CAAA,IAAA,EAAA,aAAA,IAAA,CAAA;AACrC,IAAA,aAAA,CAAA,IAAA,EAAA,OAAA,EAAA,CAAA;AACE,IAAA,aAAA,CAAA,IAAA,EAAA,SAAA,EAAA,CAAA;AACC,IAAA,aAAA,CAAA,IAAA,EAAA,UAAA,EAAA,CAAA;AAGT,IAAA,aAAA,CAAA,MAAA,QAAA,EAAA,GAAA;AAAA,MAAqB;AAAA;AAAA,KAAsB,CAAA;AACY,IAAA,aAAA,CAAA,IAAA,EAAA,mBAAA,IAAA,CAAA;AACI,IAAA,aAAA,CAAA,IAAA,EAAA,wBAAA,IAAA,CAAA;AAAA;AAE7D;AAAA,EAKP,OAAA,CAAQ,OAAe,MAAsB,EAAA;AAC9C,IAAA,IAAA,IAAA,CAAK,MACJ,KAAA,IAAA,CAAK,MAAO,CAAA,UAAA,KAAe,SAAU,CAAA,IAAA,IACrC,IAAK,CAAA,MAAA,CAAO,UAAe,KAAA,SAAA,CAAU,UAAa,CAAA,EAAA;AACrD,MAAA,OAAA,CAAQ,IAAI,2CAA2C,CAAA;AACvD,MAAA;AAAA;AAGF,IAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AACb,IAAA,IAAA,CAAK,MAAS,GAAA,MAAA;AACd,IAAA,IAAA,CAAK,GAAM,GAAA,CAAA,6BAAA,EAAgC,KAAK,CAAA,QAAA,EAAW,MAAM,CAAA,cAAA,CAAA;AAEjE,IAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,IAAA,IAAA,CAAK,kBAAmB,EAAA;AAEpB,IAAA,IAAA;AACF,MAAA,IAAA,CAAK,MAAS,GAAA,IAAI,SAAU,CAAA,IAAA,CAAK,UAAU,CAAA;AAC3C,MAAA,IAAA,CAAK,oBAAqB,EAAA;AAAA,aACnB,KAAO,EAAA;AACN,MAAA,OAAA,CAAA,KAAA,CAAM,+BAA+B,KAAK,CAAA;AAClD,MAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,MAAA,IAAA,CAAK,kBAAmB,EAAA;AACxB,MAAA,IAAA,CAAK,iBAAkB,EAAA;AAAA;AAAA;AACzB;AAAA,EAIK,UAAmB,GAAA;AACxB,IAAA,IAAA,CAAK,cAAe,EAAA;AAEpB,IAAA,IAAI,KAAK,MAAQ,EAAA;AACX,MAAA,IAAA;AACF,QAAA,IAAA,CAAK,OAAO,KAAM,EAAA;AAAA,eACX,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,4BAA4B,KAAK,CAAA;AAAA;AAGjD,MAAA,IAAA,CAAK,MAAS,GAAA,IAAA;AACd,MAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,MAAA,IAAA,CAAK,kBAAmB,EAAA;AAAA;AAAA;AAC1B;AAAA,EAIK,UAAU,QAAoD,EAAA;AACnE,IAAA,IAAA,CAAK,eAAkB,GAAA,QAAA;AAAA;AAAA;AAAA,EAIlB,eAAe,QAAmD,EAAA;AACvE,IAAA,IAAA,CAAK,oBAAuB,GAAA,QAAA;AAAA;AAAA;AAAA,EAItB,QAAmB,GAAA;AAMlB,IAAA;AAEL,MAAA,OAAA,CAAQ,IAAI,iCAAiC,CAAA;AAC7C,MAAA,MAAM,QAAW,GAAA,CAAA,KAAA,CAAA,EAAO,QAAS,CAAA,QAAA,KAAa,WAAW,MAAS,GAAA,KAAA;AAC5D,MAAA,MAAA,IAAA,YAAc,QAAS,CAAA,IAAA;AAC7B,MAAA,OAAO,GAAG,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAA,EAAG,KAAK,GAAG,CAAA,CAAA;AAAA;AAAA;AACxC;AAAA,EAIM,oBAA6B,GAAA;AAC/B,IAAA,IAAA,CAAC,IAAK,CAAA,MAAA;AAAQ,MAAA;AAEb,IAAA,IAAA,CAAA,MAAA,CAAO,SAAS,MAAM;AACzB,MAAA,OAAA,CAAQ,IAAI,qBAAqB,CAAA;AACjC,MAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,MAAA,IAAA,CAAK,kBAAmB,EAAA;AACxB,MAAA,IAAA,CAAK,iBAAoB,GAAA,CAAA;AACzB,MAAA,IAAA,CAAK,cAAe,EAAA;AAAA,KACtB;AAEK,IAAA,IAAA,CAAA,MAAA,CAAO,SAAY,GAAA,CAAC,KAAU,KAAA;AAC7B,MAAA,IAAA;AACF,QAAA,MAAM,OAAU,GAAA,IAAA,CAAK,KAAM,CAAA,KAAA,CAAM,IAAI,CAAA;AACrC,QAAA,IAAI,KAAK,eAAiB,EAAA;AACxB,UAAA,IAAA,CAAK,gBAAgB,OAAO,CAAA;AAAA;AAAA,eAEvB,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,oCAAoC,KAAK,CAAA;AAAA;AAAA,KAE3D;AAEK,IAAA,IAAA,CAAA,MAAA,CAAO,OAAU,GAAA,CAAC,KAAU,KAAA;AACvB,MAAA,OAAA,CAAA,GAAA,CAAI,qBAAqB,KAAK,CAAA;AACtC,MAAA,IAAA,CAAK,cAAe,EAAA;AACpB,MAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,MAAA,IAAA,CAAK,kBAAmB,EAAA;AACxB,MAAA,IAAA,CAAK,iBAAkB,EAAA;AAAA,KACzB;AAEK,IAAA,IAAA,CAAA,MAAA,CAAO,OAAU,GAAA,CAAC,KAAU,KAAA;AACvB,MAAA,OAAA,CAAA,KAAA,CAAM,oBAAoB,KAAK,CAAA;AACvC,MAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,MAAA,IAAA,CAAK,kBAAmB,EAAA;AAExB,MAAA,IAAI,KAAK,MAAQ,EAAA;AACf,QAAA,IAAA,CAAK,OAAO,KAAM,EAAA;AAAA;AAAA,KAEtB;AAAA;AAAA;AAAA,EAIM,iBAA0B,GAAA;AAC5B,IAAA,IAAA,IAAA,CAAK,iBAAqB,IAAA,IAAA,CAAK,oBAAsB,EAAA;AACvD,MAAA,OAAA,CAAQ,IAAI,gCAAgC,CAAA;AAC5C,MAAA;AAAA;AAGG,IAAA,IAAA,CAAA,iBAAA,EAAA;AACL,IAAA,IAAA,CAAK,OAAO,KAAQ,GAAA,CAAA;AACpB,IAAA,IAAA,CAAK,kBAAmB,EAAA;AAExB,IAAA,UAAA,CAAW,MAAM;AACf,MAAA,OAAA,CAAQ,GAAI,CAAA,CAAA,wBAAA,EAA2B,IAAK,CAAA,iBAAiB,CAAE,CAAA,CAAA;AAC/D,MAAA,IAAA,CAAK,OAAQ,CAAA,IAAA,CAAK,KAAO,EAAA,IAAA,CAAK,MAAM,CAAA;AAAA,KAAA,EACnC,KAAK,iBAAiB,CAAA;AAAA;AAAA;AAAA,EAInB,cAAuB,GAAA;AAC7B,IAAA,IAAA,CAAK,cAAe,EAAA;AAEpB,IAAA,IAAA,CAAK,YAAe,GAAA,GAAA;AACf,IAAA,IAAA,CAAA,SAAA,GAAY,YAAY,MAAM;AACjC,MAAA,IAAI,KAAK,MAAU,IAAA,IAAA,CAAK,MAAO,CAAA,UAAA,KAAe,UAAU,IAAM,EAAA;AACvD,QAAA,IAAA,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,SAAA,CAAU,EAAE,IAAM,EAAA,MAAA,EAAQ,CAAC,CAAA;AAAA;AAAA,KACnD,EACC,KAAK,YAAY,CAAA;AAAA;AAAA;AAAA,EAId,cAAuB,GAAA;AAC7B,IAAA,IAAI,KAAK,SAAW,EAAA;AAClB,MAAA,aAAA,CAAc,KAAK,SAAS,CAAA;AAC5B,MAAA,IAAA,CAAK,SAAY,GAAA,IAAA;AAAA;AAAA;AACnB;AAAA,EAIM,kBAA2B,GAAA;AACjC,IAAA,IAAI,KAAK,oBAAsB,EAAA;AACxB,MAAA,IAAA,CAAA,oBAAA,CAAqB,IAAK,CAAA,MAAA,CAAO,KAAK,CAAA;AAAA;AAAA;AAGjD;AAGa,MAAA,oBAAA,GAAuB,IAAI,oBAAqB;;;;"}