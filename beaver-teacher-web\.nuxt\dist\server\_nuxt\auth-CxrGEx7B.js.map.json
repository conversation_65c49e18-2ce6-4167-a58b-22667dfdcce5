{"file": "auth-CxrGEx7B.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAA,OAAe,0BAA0B,CAAC,OAAO;AAC/C,QAAM,YAAY,aAAa;AAG/B,QAAM,eAAe,CAAC,WAAW,WAAW,oBAAoB,iBAAiB;AAE7E,MAAA,CAAC,UAAU,mBAAmB,CAAC,aAAa,SAAS,GAAG,IAAI,GAAG;AACjE,WAAO,WAAW,SAAS;AAAA,EAAA;AAI7B,MAAI,UAAU,mBAAmB,aAAa,SAAS,GAAG,IAAI,GAAG;AAC/D,WAAO,WAAW,GAAG;AAAA,EAAA;AAEzB,CAAC;", "names": [], "sources": ["../../../../src/middleware/auth.ts"], "sourcesContent": ["import { useAuthStore } from '~/stores/useAuthStore'\r\n\r\nexport default defineNuxtRouteMiddleware((to) => {\r\n  const authStore = useAuthStore()\r\n  \r\n  // 公开路由列表\r\n  const publicRoutes = ['/signin', '/signup', '/forgot-password', '/reset-password']\r\n  \r\n  if (!authStore.isAuthenticated && !publicRoutes.includes(to.path)) {\r\n    return navigateTo('/signin')\r\n  }\r\n  \r\n  // 已登录用户不能访问登录/注册页\r\n  if (authStore.isAuthenticated && publicRoutes.includes(to.path)) {\r\n    return navigateTo('/')\r\n  }\r\n}) "], "version": 3}