import { N as request } from './server.mjs';

const submitTeacherAudit = async (params) => {
  return request({
    url: "/api/teacher/audit/submit",
    method: "POST",
    data: params
  });
};
const checkTeacherEmail = async (email) => {
  return request({
    url: "/api/teacher/audit/check/email",
    method: "GET",
    params: { email }
  });
};
const getTeacherSchedules = async (params) => {
  const { teacherId, ...queryParams } = params;
  return request({
    url: `/api/teacher/${teacherId}/schedules`,
    method: "GET",
    params: queryParams
  });
};
const cancelTeacherSchedule = async (scheduleId) => {
  return request({
    url: `/api/teacher/schedules/${scheduleId}/cancel`,
    method: "PUT"
  });
};
const batchCreateTeacherSchedules = async (teacherId, params) => {
  return request({
    url: `/api/teacher/${teacherId}/schedules/batch`,
    method: "POST",
    data: params
  });
};
const getTeacherScheduleRecords = async (teacherId, params) => {
  return request({
    url: `/api/teacher/${teacherId}/records`,
    method: "GET",
    params
  });
};

export { getTeacherSchedules as a, cancelTeacherSchedule as b, checkTeacherEmail as c, batchCreateTeacherSchedules as d, getTeacherScheduleRecords as g, submitTeacherAudit as s };
//# sourceMappingURL=teacher-BT-saGBd.mjs.map
