const interopDefault = r => r.default || r || [];
const styles = {
  "../node_modules/.pnpm/nuxt@3.13.2_@parcel+watcher@2.4.1_@types+node@22.8.2_ioredis@5.4.1_magicast@0.3.5_rollup@4.24_74ct76gnbbjoffztsptlpkok3m/node_modules/nuxt/dist/app/entry.js": () => import('./entry-styles.D_OkTFaH.mjs').then(interopDefault),
  "pages/index.vue": () => import('./index-styles.CaVmP1Cy.mjs').then(interopDefault),
  "pages/join.vue": () => import('./join-styles.BAUwB5oq.mjs').then(interopDefault),
  "pages/student/payment/success.vue": () => import('./success-styles.Dd-1Y-Qh.mjs').then(interopDefault),
  "pages/student/payment/callback.vue": () => import('./callback-styles.DVoYKSRk.mjs').then(interopDefault),
  "pages/student/terms.vue": () => import('./terms-styles.BNvrnzmx.mjs').then(interopDefault),
  "pages/student/payment/index.vue": () => import('./index-styles.DYNUWucL.mjs').then(interopDefault),
  "pages/student/payment/static-success.vue": () => import('./static-success-styles.DmyczUPo.mjs').then(interopDefault),
  "pages/teacher/account/index.vue": () => import('./index-styles.C7A7z-G5.mjs').then(interopDefault),
  "pages/teacher/terms.vue": () => import('./terms-styles.D6aJm0JG.mjs').then(interopDefault),
  "pages/join.vue?vue&type=style&index=0&scoped=0009e45f&lang.css": () => import('./join-styles.CMvFtHxH.mjs').then(interopDefault),
  "pages/student/payment/success.vue?vue&type=style&index=0&scoped=bc42ac82&lang.css": () => import('./success-styles.CBTBphGl.mjs').then(interopDefault),
  "pages/student/terms.vue?vue&type=style&index=0&scoped=45535a9e&lang.css": () => import('./terms-styles.C-HXxC2c.mjs').then(interopDefault),
  "pages/student/payment/callback.vue?vue&type=style&index=0&scoped=a80083ee&lang.css": () => import('./callback-styles.BCOfJ2hH.mjs').then(interopDefault),
  "pages/student/payment/index.vue?vue&type=style&index=0&scoped=f45cdfec&lang.css": () => import('./index-styles.CL_xKQgb.mjs').then(interopDefault),
  "pages/student/payment/static-success.vue?vue&type=style&index=0&scoped=f3011b87&lang.css": () => import('./static-success-styles.Cs2xQtlf.mjs').then(interopDefault),
  "pages/teacher/account/index.vue?vue&type=style&index=0&scoped=ab10c1f8&lang.css": () => import('./index-styles.CNoARKQW.mjs').then(interopDefault),
  "pages/teacher/terms.vue?vue&type=style&index=0&scoped=dd74f684&lang.css": () => import('./terms-styles.B33YXZJY.mjs').then(interopDefault),
  "../node_modules/.pnpm/nuxt@3.13.2_@parcel+watcher@2.4.1_@types+node@22.8.2_ioredis@5.4.1_magicast@0.3.5_rollup@4.24_74ct76gnbbjoffztsptlpkok3m/node_modules/nuxt/dist/app/components/error-404.vue": () => import('./error-404-styles.y6RRy-GX.mjs').then(interopDefault),
  "../node_modules/.pnpm/nuxt@3.13.2_@parcel+watcher@2.4.1_@types+node@22.8.2_ioredis@5.4.1_magicast@0.3.5_rollup@4.24_74ct76gnbbjoffztsptlpkok3m/node_modules/nuxt/dist/app/components/error-500.vue": () => import('./error-500-styles.DvOB4Q3e.mjs').then(interopDefault),
  "pages/index.vue?vue&type=style&index=0&scoped=b289defb&lang.scss": () => import('./index-styles.Cy-4jdLY.mjs').then(interopDefault),
  "components/ConfirmationDialog.vue": () => import('./ConfirmationDialog-styles.BM_H7oid.mjs').then(interopDefault),
  "components/generalElements/AppFooter.vue": () => import('./AppFooter-styles.PIQ9k2Rb.mjs').then(interopDefault),
  "../node_modules/.pnpm/nuxt@3.13.2_@parcel+watcher@2.4.1_@types+node@22.8.2_ioredis@5.4.1_magicast@0.3.5_rollup@4.24_74ct76gnbbjoffztsptlpkok3m/node_modules/nuxt/dist/app/components/error-404.vue?vue&type=style&index=0&scoped=e1006541&lang.css": () => import('./error-404-styles.jMRN_6bp.mjs').then(interopDefault),
  "../node_modules/.pnpm/nuxt@3.13.2_@parcel+watcher@2.4.1_@types+node@22.8.2_ioredis@5.4.1_magicast@0.3.5_rollup@4.24_74ct76gnbbjoffztsptlpkok3m/node_modules/nuxt/dist/app/components/error-500.vue?vue&type=style&index=0&scoped=3c9eff3e&lang.css": () => import('./error-500-styles.ChzUAYCC.mjs').then(interopDefault),
  "../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue": () => import('./Select-styles.QdIAV0cv.mjs').then(interopDefault),
  "components/ConfirmationDialog.vue?vue&type=style&index=0&scoped=57db56a4&lang.css": () => import('./ConfirmationDialog-styles.CUcIGyBR.mjs').then(interopDefault),
  "components/NoticePopup.vue": () => import('./NoticePopup-styles.BTo3wiqy.mjs').then(interopDefault),
  "components/generalElements/LanguageToggle.vue": () => import('./LanguageToggle-styles.zoyITJnv.mjs').then(interopDefault),
  "components/generalElements/AppFooter.vue?vue&type=style&index=0&scoped=4db3f0d4&lang.css": () => import('./AppFooter-styles.067syg0r.mjs').then(interopDefault),
  "../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue?vue&type=style&index=0&scoped=a40cf5e4&lang.css": () => import('./Select-styles.DZm7unfT.mjs').then(interopDefault),
  "components/NoticePopup.vue?vue&type=style&index=0&lang.css": () => import('./NoticePopup-styles.DeIymANo.mjs').then(interopDefault),
  "components/generalElements/LanguageToggle.vue?vue&type=style&index=0&scoped=e1d1dbca&lang.css": () => import('./LanguageToggle-styles.DmjMyc6f.mjs').then(interopDefault),
  "components/NewScheduleModal.vue": () => import('./NewScheduleModal-styles.BsRo0Ljn.mjs').then(interopDefault),
  "components/NewScheduleModal.vue?vue&type=style&index=0&scoped=0dbf7b6c&lang.postcss": () => import('./NewScheduleModal-styles.cqI5tPFN.mjs').then(interopDefault),
  "components/TimePicker.vue": () => import('./TimePicker-styles.D0npJaNd.mjs').then(interopDefault),
  "components/TimePicker.vue?vue&type=style&index=0&scoped=08bfe35d&lang.css": () => import('./TimePicker-styles.CS9J2QPP.mjs').then(interopDefault),
  "components/generalElements/BottomNav.vue": () => import('./BottomNav-styles.DcVDzwD8.mjs').then(interopDefault),
  "components/generalElements/BottomNav.vue?vue&type=style&index=0&scoped=1da7a2eb&lang.css": () => import('./BottomNav-styles.D6svUcd5.mjs').then(interopDefault),
  "components/generalElements/MainMenu.vue": () => import('./MainMenu-styles.CNX53OCh.mjs').then(interopDefault),
  "components/generalElements/MainMenu.vue?vue&type=style&index=0&scoped=51d102dd&lang.css": () => import('./MainMenu-styles.DZn67d25.mjs').then(interopDefault)
};

export { styles as default };
//# sourceMappingURL=styles.mjs.map
