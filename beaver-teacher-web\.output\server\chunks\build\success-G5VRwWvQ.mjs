import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { useSSRContext, defineComponent, ref, mergeProps, unref, withCtx, createTextVNode } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { e as useRoute, f as useRouter } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "success",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    ref(true);
    const orderNo = ref(route.query.orderNo);
    const amount = ref(route.query.amount);
    const cardName = ref(route.query.cardName);
    const countdown = ref(20);
    const timer = ref();
    const viewCourses = () => {
      if (timer.value) {
        clearInterval(timer.value);
      }
      router.push("/student/course-cards");
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-gray-50 p-4" }, _attrs))} data-v-bc42ac82><div class="max-w-md w-full" data-v-bc42ac82><div class="bg-white rounded-xl border border-gray-200 p-8 text-center mb-4" data-v-bc42ac82><div class="relative" data-v-bc42ac82><div class="absolute -top-16 left-1/2 transform -translate-x-1/2" data-v-bc42ac82><div class="w-32 h-32 bg-green-50 rounded-full flex items-center justify-center" data-v-bc42ac82>`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-check-circle",
        class: "w-20 h-20 text-green-500"
      }, null, _parent));
      _push(`</div></div></div><div class="mt-20" data-v-bc42ac82><h1 class="text-xl font-bold text-gray-900" data-v-bc42ac82>\u652F\u4ED8\u6210\u529F\uFF01</h1><div class="mt-6 space-y-4" data-v-bc42ac82><div class="bg-gray-50 rounded-lg p-4" data-v-bc42ac82><div class="text-sm text-gray-500" data-v-bc42ac82>\u8BA2\u5355\u91D1\u989D</div><div class="text-2xl font-bold text-orange-500 mt-1" data-v-bc42ac82>\xA5${ssrInterpolate(unref(amount) || "0.00")}</div></div><div class="bg-gray-50 rounded-lg p-4" data-v-bc42ac82><div class="text-sm text-gray-500" data-v-bc42ac82>\u8BFE\u5361\u540D\u79F0</div><div class="text-lg font-medium text-gray-900 mt-1" data-v-bc42ac82>${ssrInterpolate(unref(cardName) || "\u672A\u77E5\u8BFE\u5361")}</div></div><div class="bg-gray-50 rounded-lg p-4" data-v-bc42ac82><div class="text-sm text-gray-500" data-v-bc42ac82>\u8BA2\u5355\u7F16\u53F7</div><div class="text-sm font-medium text-gray-900 mt-1" data-v-bc42ac82>${ssrInterpolate(unref(orderNo))}</div></div></div><div class="mt-8 text-gray-500" data-v-bc42ac82>${ssrInterpolate(unref(countdown))}\u79D2\u540E\u81EA\u52A8\u8DF3\u8F6C\u5230\u8BFE\u5361\u9875\u9762 </div></div></div><div class="space-y-4" data-v-bc42ac82>`);
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        color: "orange",
        onClick: viewCourses
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u7ACB\u5373\u67E5\u770B\u8BFE\u5361 `);
          } else {
            return [
              createTextVNode(" \u7ACB\u5373\u67E5\u770B\u8BFE\u5361 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        variant: "ghost",
        to: "/student/course-cards/buy"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u7EE7\u7EED\u8D2D\u4E70 `);
          } else {
            return [
              createTextVNode(" \u7EE7\u7EED\u8D2D\u4E70 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/payment/success.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const success = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-bc42ac82"]]);

export { success as default };
//# sourceMappingURL=success-G5VRwWvQ.mjs.map
