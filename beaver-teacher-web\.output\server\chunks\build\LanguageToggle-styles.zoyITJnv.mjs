import { L as LanguageToggle_vue_vue_type_style_index_0_scoped_e1d1dbca_lang } from './LanguageToggle-styles-1.mjs-CG20NlIw.mjs';

const LanguageToggleStyles_zoyITJnv = [LanguageToggle_vue_vue_type_style_index_0_scoped_e1d1dbca_lang, LanguageToggle_vue_vue_type_style_index_0_scoped_e1d1dbca_lang];

export { LanguageToggleStyles_zoyITJnv as default };
//# sourceMappingURL=LanguageToggle-styles.zoyITJnv.mjs.map
