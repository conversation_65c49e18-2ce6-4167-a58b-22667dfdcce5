const index_vue_vue_type_style_index_0_scoped_f45cdfec_lang = ".min-h-screen[data-v-f45cdfec]{min-height:100vh;min-height:-webkit-fill-available}.glass[data-v-f45cdfec]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);background:hsla(0,0%,100%,.1);border:1px solid hsla(0,0%,100%,.2)}@media (max-width:768px){.glass[data-v-f45cdfec]{backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}}";

export { index_vue_vue_type_style_index_0_scoped_f45cdfec_lang as i };
//# sourceMappingURL=index-styles-1.mjs-C8e67uWH.mjs.map
