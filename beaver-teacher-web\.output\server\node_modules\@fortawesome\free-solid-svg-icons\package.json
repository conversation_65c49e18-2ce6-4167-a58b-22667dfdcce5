{"description": "The iconic font, CSS, and SVG framework", "keywords": ["font", "awesome", "fontawesome", "icon", "svg", "bootstrap"], "homepage": "https://fontawesome.com", "bugs": {"url": "https://github.com/FortAwesome/Font-Awesome/issues"}, "author": "The Font Awesome Team (https://github.com/orgs/FortAwesome/people)", "repository": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome"}, "engines": {"node": ">=6"}, "dependencies": {"@fortawesome/fontawesome-common-types": "6.6.0"}, "version": "6.6.0", "name": "@fortawesome/free-solid-svg-icons", "main": "index.js", "module": "index.mjs", "jsnext:main": "index.mjs", "license": "(CC-BY-4.0 AND MIT)", "types": "./index.d.ts", "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index.js": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./package.json": "./package.json", "./*": "./*.js"}}