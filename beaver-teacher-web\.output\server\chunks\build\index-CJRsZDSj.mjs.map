{"version": 3, "file": "index-CJRsZDSj.mjs", "sources": ["../../../../src/pages/student/service/index.vue"], "sourcesContent": null, "names": ["_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeM,IAAA,MAAA,EAAE,CAAE,EAAA,GAAI,OAAQ,EAAA;AACtB,IAAA,MAAM,YAAY,YAAa,EAAA;AAC/B,IAAA,MAAM,QAAQ,QAAS,EAAA;AAGjB,IAAA,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA;AACnB,IAAA,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA;AACnB,IAAA,MAAA,QAAA,GAAW,GAAuB,CAAA,EAAE,CAAA;AACpC,IAAA,MAAA,QAAA,GAAW,GAAuB,CAAA,EAAE,CAAA;AACpC,IAAA,MAAA,gBAAA,GAAmB,IAAmB,IAAI,CAAA;AAC1C,IAAA,MAAA,cAAA,GAAiB,IAAI,KAAK,CAAA;AAC1B,IAAA,MAAA,eAAA,GAAkB,IAAI,EAAE,CAAA;AACxB,IAAA,MAAA,cAAA,GAAiB,IAAI,EAAE,CAAA;AAC7B,IAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,oBAAA,CAAqB,OAAO,KAAK,CAAA;AAC3D,IAAA,MAAA,eAAA,GAAkB,IAAI,KAAK,CAAA;AAC3B,IAAA,MAAA,WAAA,GAAc,IAAI,KAAK,CAAA;AAEJ,IAAA,GAAA,CAAI,CAAC,CAAA;AAIxB,IAAA,MAAA,cAAA,GAAiB,SAAS,MAAM;AAChC,MAAA,IAAA,CAAC,gBAAiB,CAAA,KAAA;AAAc,QAAA,OAAA,IAAA;AAC7B,MAAA,OAAA,QAAA,CAAS,MAAM,IAAK,CAAA,CAAC,MAAW,CAAE,CAAA,EAAA,KAAO,gBAAiB,CAAA,KAAK,CAAK,IAAA,IAAA;AAAA,KAC5E,CAAA;AAEK,IAAA,MAAA,eAAA,GAAkB,SAAS,MAAM;;AAC9B,MAAA,OAAA,CAAA,CAAA,KAAe,cAAA,CAAA,KAAA,KAAf,OAAsB,KAAA,CAAA,GAAA,EAAA,CAAA,YAAW,aAAc,CAAA,MAAA;AAAA,KACvD,CAAA;AAGD,IAAA,MAAM,gBAAgB,YAAY;AAC5B,MAAA,IAAA,CAAC,eAAe,KAAO,EAAA;AACzB,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,4CAA4C,CAAA;AAAA,UACrD,WAAA,EAAa,EAAE,kDAAkD,CAAA;AAAA,UACjE,KAAO,EAAA;AAAA,SACR,CAAA;AACD,QAAA;AAAA;AAGE,MAAA,IAAA;AACF,QAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA;AACV,QAAA,MAAA,MAAA,GAAS,MAAM,WAAA,CAAY,gBAAiB,EAAA;AAClD,QAAA,QAAA,CAAS,KAAQ,GAAA,MAAA;AAGjB,QAAA,IAAI,SAAS,KAAM,CAAA,MAAA,GAAS,CAAK,IAAA,CAAC,iBAAiB,KAAO,EAAA;AACxD,UAAA,aAAA,CAAc,QAAS,CAAA,KAAA,CAAM,CAAC,CAAA,CAAE,EAAE,CAAA;AAAA;AAAA,eAE7B,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,oDAAY,KAAK,CAAA;AAC/B,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,iDAAiD,CAAA;AAAA,UAC1D,WAAA,EAAa,EAAE,uDAAuD,CAAA;AAAA,UACtE,KAAO,EAAA;AAAA,SACR,CAAA;AAAA,OACD,SAAA;AACA,QAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA;AAAA;AAAA,KAEpB;AAGA,IAAA,MAAM,gBAAgB,YAAY;AAC5B,MAAA,IAAA,CAAC,gBAAiB,CAAA,KAAA;AAAO,QAAA;AAEzB,MAAA,IAAA;AACF,QAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA;AAChB,QAAA,MAAM,MAAS,GAAA,MAAM,WAAY,CAAA,kBAAA,CAAmB,iBAAiB,KAAK,CAAA;AAC1E,QAAA,QAAA,CAAS,KAAQ,GAAA,MAAA;AAGjB,QAAA,IAAI,cAAe,CAAA,KAAA,IAAS,cAAe,CAAA,KAAA,CAAM,cAAc,CAAG,EAAA;AAChD,UAAA,eAAA,EAAA;AAAA;AAIlB,QAAA,MAAM,QAAS,EAAA;AACA,QAAA,cAAA,EAAA;AAAA,eACR,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,wCAAU,KAAK,CAAA;AAC7B,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,iDAAiD,CAAA;AAAA,UAC1D,WAAA,EAAa,EAAE,uDAAuD,CAAA;AAAA,UACtE,KAAO,EAAA;AAAA,SACR,CAAA;AAAA,OACD,SAAA;AACA,QAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA;AAAA;AAAA,KAEpB;AAGM,IAAA,MAAA,aAAA,GAAgB,CAAC,SAAsB,KAAA;AAC3C,MAAA,gBAAA,CAAiB,KAAQ,GAAA,SAAA;AACX,MAAA,aAAA,EAAA;AAAA,KAChB;AAGA,IAAA,MAAM,gBAAgB,YAAY;AAC5B,MAAA,IAAA,CAAC,gBAAgB,KAAM,CAAA,IAAA,MAAU,CAAC,cAAA,CAAe,KAAM,CAAA,IAAA,EAAQ,EAAA;AACjE,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,6CAA6C,CAAA;AAAA,UACtD,WAAA,EAAa,EAAE,mDAAmD,CAAA;AAAA,UAClE,KAAO,EAAA;AAAA,SACR,CAAA;AACD,QAAA;AAAA;AAGE,MAAA,IAAA;AACF,QAAA,eAAA,CAAgB,KAAQ,GAAA,IAAA;AACxB,QAAA,MAAM,WAAc,GAAA;AAAA,UAClB,YAAA,EAAc,eAAgB,CAAA,KAAA,CAAM,IAAK,EAAA;AAAA,UACzC,cAAgB,EAAA;AAAA,YACd,UAAY,EAAA,CAAA;AAAA;AAAA,YACZ,OAAA,EAAS,cAAe,CAAA,KAAA,CAAM,IAAK,EAAA;AAAA,YACnC,aAAa,kBAAmB,CAAA;AAAA;AAAA,SAEpC;AAEA,QAAA,MAAM,SAAY,GAAA,MAAM,WAAY,CAAA,aAAA,CAAc,WAAW,CAAA;AAG7D,QAAA,MAAM,aAAc,EAAA;AAGpB,QAAA,aAAA,CAAc,SAAS,CAAA;AAGvB,QAAA,eAAA,CAAgB,KAAQ,GAAA,EAAA;AACxB,QAAA,cAAA,CAAe,KAAQ,GAAA,EAAA;AACvB,QAAA,cAAA,CAAe,KAAQ,GAAA,KAAA;AAEvB,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,mDAAmD,CAAA;AAAA,UAC5D,KAAO,EAAA;AAAA,SACR,CAAA;AAAA,eACM,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,wCAAU,KAAK,CAAA;AAC7B,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,iDAAiD,CAAA;AAAA,UAC1D,WAAA,EAAa,EAAE,uDAAuD,CAAA;AAAA,UACtE,KAAO,EAAA;AAAA,SACR,CAAA;AAAA,OACD,SAAA;AACA,QAAA,eAAA,CAAgB,KAAQ,GAAA,KAAA;AAAA;AAAA,KAE5B;AAGA,IAAA,MAAM,WAAc,GAAA,OAAO,OAAiB,EAAA,WAAA,GAAsB,mBAAmB,IAAS,KAAA;;AAC5F,MAAA,IAAI,CAAC,gBAAA,CAAiB,KAAS,IAAA,CAAC,WAAW,eAAgB,CAAA,KAAA;AAAO,QAAA;AAE9D,MAAA,IAAA;AACF,QAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA;AAChB,QAAA,MAAM,WAAc,GAAA;AAAA,UAClB,WAAW,gBAAiB,CAAA,KAAA;AAAA,UAC5B,UAAY,EAAA,CAAA;AAAA;AAAA,UACZ,OAAA;AAAA,UACA;AAAA,SACF;AAEM,QAAA,MAAA,WAAA,CAAY,YAAY,WAAW,CAAA;AAInC,QAAA,MAAA,UAAS,EAAU,GAAA,SAAA,CAAA,IAAV,KAAA,IAAA,GAAgB,SAAA,EAAA,CAAA,EAAA;AAE/B,QAAA,OAAA,CAAQ,MAAM,iFAAkB,EAAA;AAAA,UAC9B,MAAA;AAAA,UACA,WAAU,EAAU,GAAA,SAAA,CAAA,IAAV,KAAA,IAAA,GAAgB,SAAA,EAAA,CAAA;AAAA,SAC3B,CAAA;AAID,QAAA,MAAM,QAAW,GAAA,MAAA,GAAS,MAAO,CAAA,MAAM,CAAI,GAAA,CAAA;AAE3C,QAAA,MAAM,WAA+B,GAAA;AAAA,UACnC,EAAA,EAAI,KAAK,GAAI,EAAA;AAAA;AAAA,UACb,WAAW,gBAAiB,CAAA,KAAA;AAAA,UAC5B,QAAA;AAAA,UACA,UAAY,EAAA,CAAA;AAAA;AAAA,UACZ,cAAY,EAAA,GAAA,SAAA,CAAU,SAAV,IAAA,GAAA,KAAA,CAAA,GAAA,GAAgB,QAAY,KAAA,QAAA;AAAA,UACxC,YAAc,EAAA,EAAA;AAAA;AAAA,UACd,OAAA;AAAA,UACA,WAAA;AAAA,UACA,MAAQ,EAAA,CAAA;AAAA,UACR,QAAU,EAAA,IAAA;AAAA,UACV,WAAa,EAAA,iBAAA,IAAI,IAAK,EAAA,EAAE,WAAY;AAAA,SACtC;AAEQ,QAAA,OAAA,CAAA,KAAA,CAAM,yCAAW,WAAW,CAAA;AAE3B,QAAA,QAAA,CAAA,KAAA,CAAM,KAAK,WAAW,CAAA;AAG/B,QAAA,MAAM,QAAS,EAAA;AACA,QAAA,cAAA,EAAA;AAAA,eACR,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,wCAAU,KAAK,CAAA;AAC7B,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,+CAA+C,CAAA;AAAA,UACxD,WAAA,EAAa,EAAE,qDAAqD,CAAA;AAAA,UACpE,KAAO,EAAA;AAAA,SACR,CAAA;AAAA,OACD,SAAA;AACA,QAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA;AAAA;AAAA,KAEpB;AAGA,IAAA,MAAM,kBAAkB,YAAY;AAC9B,MAAA,IAAA,CAAC,gBAAiB,CAAA,KAAA;AAAO,QAAA;AAEzB,MAAA,IAAA;AACI,QAAA,MAAA,WAAA,CAAY,kBAAmB,CAAA,gBAAA,CAAiB,KAAK,CAAA;AAGrD,QAAA,MAAA,OAAA,GAAU,SAAS,KAAM,CAAA,IAAA,CAAK,CAAC,CAAW,KAAA,CAAA,CAAE,EAAO,KAAA,gBAAA,CAAiB,KAAK,CAAA;AAC/E,QAAA,IAAI,OAAS,EAAA;AACX,UAAA,OAAA,CAAQ,WAAc,GAAA,CAAA;AAAA;AAAA,eAEjB,KAAO,EAAA;AACN,QAAA,OAAA,CAAA,KAAA,CAAM,oDAAY,KAAK,CAAA;AAAA;AAAA,KAEnC;AAGA,IAAA,MAAM,iBAAiB,MAAM;AACrB,MAAA,MAAA,iBAAA,GAA6B,CAAA,KAAA,CAAA,EAAA,aAAA,CAAc,qBAAqB,CAAA;AACtE,MAAA,IAAI,iBAAmB,EAAA;AACrB,QAAA,iBAAA,CAAkB,YAAY,iBAAkB,CAAA,YAAA;AAAA;AAAA,KAEpD;AAGM,IAAA,MAAA,cAAA,GAAiB,SAAS,MAAM;AACpC,MAAA,OAAO,SAAU,CAAA,eAAA;AAAA,KAClB,CAAA;AA8JD,IAAA,MAAM,yBAAyB,MAAM;;AACnC,MAAA,OAAA,CAAQ,MAAM,0CAAmB,EAAA;AAAA,QAC/B,iBAAiB,SAAU,CAAA,eAAA;AAAA,QAC3B,QAAA,EAAU,CAAC,CAAC,SAAU,CAAA,KAAA;AAAA,QACtB,OAAA,EAAS,CAAC,CAAC,SAAU,CAAA,IAAA;AAAA,QACrB,SAAQ,EAAU,GAAA,SAAA,CAAA,IAAV,KAAA,IAAA,GAAgB,SAAA,EAAA,CAAA;AAAA,OACzB,CAAA;AAGG,MAAA,IAAA,CAAC,UAAU,KAAO,EAAA;AACpB,QAAA,OAAA,CAAQ,MAAM,wHAAmC,CAAA;AACjD,QAAA,KAAA,CAAM,GAAI,CAAA;AAAA,UACR,KAAA,EAAO,EAAE,6CAA6C,CAAA;AAAA,UACtD,WAAA,EAAa,EAAE,mDAAmD,CAAA;AAAA,UAClE,KAAO,EAAA;AAAA,SACR,CAAA;AACD,QAAA;AAAA;AAIE,MAAA,IAAA,CAAC,UAAU,eAAmB,IAAA,CAAC,UAAU,IAAQ,IAAA,CAAC,SAAU,CAAA,IAAA,CAAK,EAAI,EAAA;AACvE,QAAA,OAAA,CAAQ,MAAM,qFAAyB,CAAA;AACvC,QAAA;AAAA;AAGF,MAAA,OAAA,CAAQ,MAAM,sDAAqB,EAAA;AAAA,QACjC,MAAA,EAAQ,UAAU,IAAK,CAAA,EAAA;AAAA,QACvB,QAAA,EAAU,CAAC,CAAC,SAAU,CAAA;AAAA,OACvB,CAAA;AAED,MAAA,oBAAA,CAAqB,UAAW,EAAA;AAGb,MAAA,oBAAA,CAAA,SAAA,CAAU,CAAC,OAAiB,KAAA;;AAC7C,QAAA,OAAA,CAAQ,MAAM,oCAAkB,EAAA;AAAA,UAC9B,IAAI,OAAQ,CAAA,EAAA;AAAA,UACZ,WAAW,OAAQ,CAAA,SAAA;AAAA,UACnB,UAAU,OAAQ,CAAA,QAAA;AAAA,UAClB,YAAY,OAAQ,CAAA,UAAA;AAAA,UACpB,YAAY,OAAQ,CAAA,UAAA;AAAA,UACpB,gBAAeA,GAAA,GAAA,SAAA,CAAU,IAAV,KAAA,IAAA,YAAAA,GAAgB,CAAA;AAAA,SAChC,CAAA;AAGG,QAAA,IAAA,OAAA,CAAQ,SAAc,KAAA,gBAAA,CAAiB,KAAO,EAAA;AAE1C,UAAA,MAAA,MAAA,GAAS,SAAS,KAAM,CAAA,IAAA,CAAK,CAAC,CAAW,KAAA,CAAA,CAAE,EAAO,KAAA,OAAA,CAAQ,EAAE,CAAA;AAClE,UAAA,IAAI,CAAC,MAAQ,EAAA;AAEL,YAAA,MAAA,iBAAgB,EAAU,GAAA,SAAA,CAAA,IAAV,KAAA,IAAA,GAAgB,SAAA,EAAA,CAAA,EAAA;AAElC,YAAA,IAAA,aAAA,IAAiB,QAAQ,QAAU,EAAA;AAErC,cAAA,MAAM,oBACJ,GAAA,aAAA,CAAc,QAAS,EAAA,CAAE,UAAU,CAAG,EAAA,EAAE,CACxC,KAAA,OAAA,CAAQ,QAAS,CAAA,QAAA,EAAW,CAAA,SAAA,CAAU,GAAG,EAAE,CAAA;AAE7C,cAAA,IAAI,oBAAsB,EAAA;AAExB,gBAAA,OAAA,CAAQ,UAAa,GAAA,CAAA;AAAA;AAAA;AAIhB,YAAA,QAAA,CAAA,KAAA,CAAM,KAAK,OAAO,CAAA;AAClB,YAAA,QAAA,CAAA,MAAM,gBAAgB,CAAA;AAG/B,YAAA,MAAM,aAAgB,GAAA,OAAA,CAAQ,QAAS,CAAA,QAAA,EAAW,CAAA,SAAA,CAAU,CAAG,EAAA,EAAE,CAC3C,MAAA,CAAA,CAAA,EAAA,GAAA,CAAU,EAAA,GAAA,CAAA,EAAA,GAAA,SAAA,CAAA,IAAA,KAAV,kBAAgB,CAAA,EAAA,KAAhB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAoB,CAAA,QAAA,EAApB,KAAA,IAAA,GAAgC,KAAA,CAAA,GAAA,EAAA,CAAA,SAAU,CAAA,CAAA,EAAG,EAAO,CAAA,KAAA,EAAA,CAAA;AAE1E,YAAA,IAAI,CAAC,aAAe,EAAA;AACF,cAAA,eAAA,EAAA;AAAA;AAAA;AAClB;AAKE,QAAA,MAAA,YAAA,GAAe,SAAS,KAAM,CAAA,SAAA,CAAU,CAAC,CAAW,KAAA,CAAA,CAAE,EAAO,KAAA,OAAA,CAAQ,SAAS,CAAA;AACpF,QAAA,IAAI,gBAAgB,CAAG,EAAA;AACrB,UAAA,MAAM,UAAe,EAAE,GAAG,QAAS,CAAA,KAAA,CAAM,YAAY,CAAE,EAAA;AACvD,UAAA,OAAA,CAAQ,cAAc,OAAQ,CAAA,OAAA;AAC9B,UAAA,OAAA,CAAQ,kBAAkB,OAAQ,CAAA,WAAA;AAGlC,UAAA,MAAM,aAAgB,GAAA,OAAA,CAAQ,QAAS,CAAA,QAAA,EAAW,CAAA,SAAA,CAAU,CAAG,EAAA,EAAE,CAC1C,MAAA,CAAA,CAAA,EAAA,GAAA,CAAU,EAAA,GAAA,CAAA,EAAA,GAAA,SAAA,CAAA,IAAA,KAAV,kBAAgB,CAAA,EAAA,KAAhB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAoB,CAAA,QAAA,EAApB,KAAA,IAAA,GAAgC,KAAA,CAAA,GAAA,EAAA,CAAA,SAAU,CAAA,CAAA,EAAG,EAAO,CAAA,KAAA,EAAA,CAAA;AAE3E,UAAA,IAAI,OAAQ,CAAA,SAAA,KAAc,gBAAiB,CAAA,KAAA,IAAS,CAAC,aAAe,EAAA;AAC1D,YAAA,OAAA,CAAA,WAAA,GAAA,CAAe,OAAQ,CAAA,WAAA,IAAe,CAAK,IAAA,CAAA;AAAA;AAI5C,UAAA,QAAA,CAAA,KAAA,CAAM,MAAO,CAAA,YAAA,EAAc,CAAC,CAAA;AAC5B,UAAA,QAAA,CAAA,KAAA,CAAM,QAAQ,OAAO,CAAA;AAAA,SACzB,MAAA;AAES,UAAA,aAAA,EAAA;AAAA;AAAA,OAEjB,CAAA;AAGoB,MAAA,oBAAA,CAAA,cAAA,CAAe,CAAC,MAAW,KAAA;AAE9C,QAAA,IAAI,MAAW,KAAA,eAAA,CAAgB,MAAU,IAAA,MAAA,KAAW,gBAAgB,KAAO,EAAA;AACzE,UAAA,OAAA,CAAQ,IAAI,6EAAsB,CAAA;AAAA;AAAA,OAErC,CAAA;AAGoB,MAAA,oBAAA,CAAA,OAAA;AAAA,QACnB,UAAU,KAAS,IAAA,EAAA;AAAA,QACnB,SAAU,CAAA,IAAA,CAAK,EAAG,CAAA,QAAA,EAAc,IAAA;AAAA,OAClC;AAAA,KACF;AAGA,IAAA,KAAA,CAAM,MAAM,SAAA,CAAU,eAAiB,EAAA,CAAC,eAA6B,KAAA;AACnE,MAAA,IAAI,eAAmB,IAAA,SAAA,CAAU,IAAQ,IAAA,SAAA,CAAU,KAAK,EAAI,EAAA;AAC5C,QAAA,aAAA,EAAA;AACS,QAAA,sBAAA,EAAA;AAAA;AAAA,KAE1B,CAAA;AAGK,IAAA,KAAA,CAAA,gBAAA,EAAkB,CAAC,KAAyB,KAAA;AAChD,MAAA,IAAI,KAAO,EAAA;AACK,QAAA,aAAA,EAAA;AAAA,OACT,MAAA;AACL,QAAA,QAAA,CAAS,QAAQ,EAAC;AAAA;AAAA,KAErB,CAAA;AAQD,IAAA,MAAM,uBAAuB,MAAM;AACjC,MAAA,cAAA,CAAe,KAAQ,GAAA,IAAA;AAEP,MAAA,eAAA,CAAA,KAAA,GAAQ,EAAE,uCAAuC,CAAA;AAAA,KACnE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}