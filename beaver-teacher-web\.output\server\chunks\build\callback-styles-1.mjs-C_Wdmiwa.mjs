const callback_vue_vue_type_style_index_0_scoped_a80083ee_lang = ".min-h-screen[data-v-a80083ee]{min-height:100vh;min-height:-webkit-fill-available}button[data-v-a80083ee]{min-height:44px;min-width:44px}@media (max-width:768px){.max-w-md[data-v-a80083ee]{margin:1rem;width:calc(100% - 2rem)}button[data-v-a80083ee]{min-height:48px}}@media (min-width:320px) and (max-width:768px){.py-8[data-v-a80083ee]{padding-bottom:2rem;padding-top:2rem}.mt-8[data-v-a80083ee]{margin-top:2rem}.space-y-3[data-v-a80083ee]>*+*{margin-top:.75rem}}";

export { callback_vue_vue_type_style_index_0_scoped_a80083ee_lang as c };
//# sourceMappingURL=callback-styles-1.mjs-C_Wdmiwa.mjs.map
