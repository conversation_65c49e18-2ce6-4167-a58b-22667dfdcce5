import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { defineComponent, ref, resolveComponent, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrInterpolate } from 'vue/server-renderer';
import { s as studentApi } from './student-DtKAviut.mjs';
import { e as useRoute, f as useRouter, g as navigateTo, c as useToast } from './server.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "buy",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    const courseCards = ref([]);
    const loading = ref(false);
    const selectedCard = ref(null);
    const selectedType = ref(0);
    const fetchCourseCards = async () => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const response = await studentApi.getCourseCardsList({
          name: "",
          cardNumber: "",
          cardType: selectedType.value,
          page: 1,
          pageSize: 50
        });
        courseCards.value = response.list;
      } catch (error) {
        console.error("\u83B7\u53D6\u8BFE\u5361\u5217\u8868\u5931\u8D25:", error);
        const toast = useToast();
        toast.add({
          title: "\u83B7\u53D6\u8BFE\u5361\u5931\u8D25",
          description: "\u8BF7\u7A0D\u540E\u91CD\u8BD5",
          color: "red",
          timeout: 3e3
        });
      } finally {
        loading.value = false;
      }
    };
    const switchCardType = (type) => {
      selectedType.value = type;
      selectedCard.value = null;
      fetchCourseCards();
    };
    const purchaseCard = async () => {
      if (!selectedCard.value)
        return;
      navigateTo({
        path: "/student/payment",
        query: {
          title: `${selectedCard.value.cardName}`,
          cardId: selectedCard.value.id.toString(),
          redirect: route.query.redirect,
          teacherName: route.query.teacherName
        }
      });
    };
    const handleBack = () => {
      const redirect = route.query.redirect;
      if (redirect) {
        router.push({
          path: redirect,
          query: {
            teacherName: route.query.teacherName
          }
        });
      } else {
        router.push("/student/account");
      }
    };
    const getCardTypeText = (cardType) => {
      return cardType === 1 ? "\u4E00\u5BF9\u4E00" : "\u5C0F\u7EC4\u8BFE";
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UButton = __nuxt_component_2;
      const _component_ULoadingIcon = resolveComponent("ULoadingIcon");
      const _component_UBadge = __nuxt_component_1;
      const _component_UIcon = __nuxt_component_0;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><div class="absolute left-2">`);
      _push(ssrRenderComponent(_component_UButton, {
        icon: "i-heroicons-arrow-left",
        variant: "ghost",
        onClick: handleBack,
        class: "text-gray-600"
      }, null, _parent));
      _push(`</div><h1 class="text-base font-semibold">\u8D2D\u4E70\u8BFE\u5361</h1></div><div class="max-w-4xl mx-auto px-4 md:px-6 pt-[calc(48px+env(safe-area-inset-top))] md:pt-6 pb-6"><div class="hidden sm:flex items-center mb-6"><h1 class="text-xl">\u8D2D\u4E70\u8BFE\u5361</h1></div><div class="flex gap-2 mb-6">`);
      _push(ssrRenderComponent(_component_UButton, {
        size: "sm",
        color: unref(selectedType) === 0 ? "primary" : "gray",
        variant: unref(selectedType) === 0 ? "solid" : "ghost",
        onClick: ($event) => switchCardType(0)
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u5168\u90E8\u8BFE\u7A0B `);
          } else {
            return [
              createTextVNode(" \u5168\u90E8\u8BFE\u7A0B ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        size: "sm",
        color: unref(selectedType) === 1 ? "primary" : "gray",
        variant: unref(selectedType) === 1 ? "solid" : "ghost",
        onClick: ($event) => switchCardType(1)
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u4E00\u5BF9\u4E00\u8BFE\u7A0B `);
          } else {
            return [
              createTextVNode(" \u4E00\u5BF9\u4E00\u8BFE\u7A0B ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        size: "sm",
        color: unref(selectedType) === 2 ? "primary" : "gray",
        variant: unref(selectedType) === 2 ? "solid" : "ghost",
        onClick: ($event) => switchCardType(2)
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u5C0F\u7EC4\u8BFE\u7A0B `);
          } else {
            return [
              createTextVNode(" \u5C0F\u7EC4\u8BFE\u7A0B ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      if (unref(loading)) {
        _push(`<div class="p-4">`);
        _push(ssrRenderComponent(_component_ULoadingIcon, null, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<div class="grid gap-4"><!--[-->`);
        ssrRenderList(unref(courseCards), (card) => {
          var _a, _b;
          _push(`<div class="${ssrRenderClass([{ "ring-2 ring-primary ring-offset-2": ((_a = unref(selectedCard)) == null ? void 0 : _a.id) === card.id }, "bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 cursor-pointer transition-all duration-300 border border-blue-100"])}"><div class="relative"><div class="flex items-center justify-between mb-4"><h3 class="text-2xl font-bold text-gray-800">${ssrInterpolate(card.cardName)}</h3>`);
          _push(ssrRenderComponent(_component_UBadge, {
            color: card.cardType === 1 ? "primary" : "purple",
            class: "text-sm font-medium px-3 py-1",
            variant: "solid"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(getCardTypeText(card.cardType))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(getCardTypeText(card.cardType)), 1)
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div><div class="space-y-3 text-gray-700"><div class="flex items-center space-x-2">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-clock",
            class: "w-5 h-5 text-primary"
          }, null, _parent));
          _push(`<span>\u53EF\u7528\u8BFE\u65F6\uFF1A${ssrInterpolate(card.classTimes)} \u6B21</span></div><div class="flex items-center space-x-2">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-calendar",
            class: "w-5 h-5 text-primary"
          }, null, _parent));
          _push(`<span>\u6709\u6548\u671F\uFF1A${ssrInterpolate(card.validDays)} \u5929</span></div><div class="flex items-center space-x-2">`);
          _push(ssrRenderComponent(_component_UIcon, {
            name: "i-heroicons-currency-yen",
            class: "w-5 h-5 text-primary"
          }, null, _parent));
          _push(`<span class="text-lg font-semibold text-primary">\xA5${ssrInterpolate(card.price)}</span><span class="text-sm text-gray-500 line-through">\xA5${ssrInterpolate(card.originalPrice)}</span></div></div>`);
          if (card.description) {
            _push(`<div class="mt-4 text-sm text-gray-600 bg-white bg-opacity-50 rounded-lg p-3">${ssrInterpolate(card.description)}</div>`);
          } else {
            _push(`<!---->`);
          }
          if (((_b = unref(selectedCard)) == null ? void 0 : _b.id) === card.id) {
            _push(ssrRenderComponent(_component_UButton, {
              class: "mt-4 w-full",
              color: "primary",
              variant: "solid",
              onClick: purchaseCard
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(` \u786E\u8BA4\u8D2D\u4E70 `);
                } else {
                  return [
                    createTextVNode(" \u786E\u8BA4\u8D2D\u4E70 ")
                  ];
                }
              }),
              _: 2
            }, _parent));
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        });
        _push(`<!--]--></div>`);
      }
      if (unref(courseCards).length === 0 && !unref(loading)) {
        _push(`<div class="text-center py-12">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-ticket",
          class: "w-16 h-16 mx-auto text-gray-400"
        }, null, _parent));
        _push(`<p class="text-gray-500 mt-4">\u6682\u65E0\u53EF\u8D2D\u4E70\u7684\u8BFE\u5361</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/course-cards/buy.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=buy-Bzg3a_TX.mjs.map
