import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { defineComponent, ref, mergeProps, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { e as useRoute } from './server.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const teacherId = route.params.id;
    const teacher = ref({
      id: teacherId,
      name: "\u738B\u8001\u5E08",
      title: "\u8D44\u6DF1\u82F1\u8BED\u6559\u5E08",
      experience: "10\u5E74\u6559\u5B66\u7ECF\u9A8C",
      speciality: ["\u5C11\u513F\u82F1\u8BED", "\u8BED\u97F3\u6559\u5B66", "\u8DE8\u6587\u5316\u4EA4\u9645"],
      education: [
        {
          degree: "\u82F1\u8BED\u6559\u80B2\u7855\u58EB",
          school: "\u5317\u4EAC\u5E08\u8303\u5927\u5B66",
          year: "2013"
        },
        {
          degree: "TESOL\u8BA4\u8BC1",
          school: "\u5251\u6865\u5927\u5B66",
          year: "2014"
        }
      ],
      achievements: [
        '\u83B7\u5F97"\u5E74\u5EA6\u4F18\u79C0\u6559\u5E08"\u79F0\u53F7',
        "\u7F16\u5199\u5C11\u513F\u82F1\u8BED\u6559\u6750\u300A\u5FEB\u4E50\u82F1\u8BED\u300B",
        "\u6307\u5BFC\u5B66\u751F\u83B7\u5F97\u591A\u9879\u82F1\u8BED\u7ADE\u8D5B\u5956\u9879"
      ],
      teachingStyle: "\u6CE8\u91CD\u4E92\u52A8\uFF0C\u5584\u4E8E\u8C03\u52A8\u8BFE\u5802\u6C14\u6C1B\uFF0C\u8BA9\u5B66\u4E60\u53D8\u5F97\u6709\u8DA3\u800C\u9AD8\u6548\u3002\u91C7\u7528\u591A\u5A92\u4F53\u6559\u5B66\uFF0C\u7ED3\u5408\u5B9E\u9645\u751F\u6D3B\u573A\u666F\uFF0C\u5E2E\u52A9\u5B66\u751F\u5EFA\u7ACB\u8BED\u8A00\u73AF\u5883\u3002",
      avatar: "/images/teachers/teacher.png",
      gallery: [
        "/images/teachers/class-1.jpg",
        "/images/teachers/class-2.jpg",
        "/images/teachers/class-3.jpg"
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UBadge = __nuxt_component_1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "container mx-auto px-4 py-8" }, _attrs))}><div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12"><div><img${ssrRenderAttr("src", unref(teacher).avatar)}${ssrRenderAttr("alt", unref(teacher).name)} class="w-full aspect-square object-cover rounded-lg shadow-lg"></div><div class="lg:col-span-2 space-y-6"><div><h1 class="text-3xl font-bold mb-2">${ssrInterpolate(unref(teacher).name)}</h1><p class="text-xl text-gray-600">${ssrInterpolate(unref(teacher).title)}</p></div><div class="space-y-4"><div class="flex items-center gap-2">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-academic-cap",
        class: "text-gray-600"
      }, null, _parent));
      _push(`<span>${ssrInterpolate(unref(teacher).education[0].degree)}</span></div><div class="flex items-center gap-2">`);
      _push(ssrRenderComponent(_component_UIcon, {
        name: "i-heroicons-clock",
        class: "text-gray-600"
      }, null, _parent));
      _push(`<span>${ssrInterpolate(unref(teacher).experience)}</span></div></div><div><h2 class="text-xl font-bold mb-3">\u6559\u5B66\u7279\u8272</h2><p class="text-gray-600">${ssrInterpolate(unref(teacher).teachingStyle)}</p></div><div><h2 class="text-xl font-bold mb-3">\u4E13\u4E1A\u7279\u957F</h2><div class="flex flex-wrap gap-2"><!--[-->`);
      ssrRenderList(unref(teacher).speciality, (skill) => {
        _push(ssrRenderComponent(_component_UBadge, { key: skill }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(skill)}`);
            } else {
              return [
                createTextVNode(toDisplayString(skill), 1)
              ];
            }
          }),
          _: 2
        }, _parent));
      });
      _push(`<!--]--></div></div></div></div><div class="mb-12"><h2 class="text-2xl font-bold mb-6">\u6559\u80B2\u80CC\u666F</h2><div class="space-y-4"><!--[-->`);
      ssrRenderList(unref(teacher).education, (edu) => {
        _push(`<div class="bg-gray-50 p-6 rounded-lg"><div class="flex justify-between items-center mb-2"><h3 class="text-xl font-bold">${ssrInterpolate(edu.degree)}</h3><span class="text-gray-600">${ssrInterpolate(edu.year)}</span></div><p class="text-gray-600">${ssrInterpolate(edu.school)}</p></div>`);
      });
      _push(`<!--]--></div></div><div class="mb-12"><h2 class="text-2xl font-bold mb-6">\u6559\u5B66\u6210\u5C31</h2><ul class="space-y-4"><!--[-->`);
      ssrRenderList(unref(teacher).achievements, (achievement) => {
        _push(`<li class="flex items-start gap-3">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-trophy",
          class: "text-yellow-600 flex-shrink-0 mt-1"
        }, null, _parent));
        _push(`<span>${ssrInterpolate(achievement)}</span></li>`);
      });
      _push(`<!--]--></ul></div><div><h2 class="text-2xl font-bold mb-6">\u8BFE\u5802\u63A0\u5F71</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-4"><!--[-->`);
      ssrRenderList(unref(teacher).gallery, (photo, index) => {
        _push(`<img${ssrRenderAttr("src", photo)}${ssrRenderAttr("alt", `${unref(teacher).name}\u7684\u8BFE\u5802\u7167\u7247${index + 1}`)} class="w-full aspect-video object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow">`);
      });
      _push(`<!--]--></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_id_-CGzuYRF8.mjs.map
