import { _ as __nuxt_component_2 } from './Form-CpNzGgY1.mjs';
import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { _ as __nuxt_component_2$1 } from './Button-3EsiVOgL.mjs';
import { defineComponent, ref, computed, unref, withCtx, createVNode, createTextVNode, toDisplayString, isRef, useSSRContext } from 'vue';
import { ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { B as useI18n, f as useRouter, K as useAuthStore, c as useToast, M as authApi } from './server.mjs';
import { s as setInterval } from './interval-gl53xdpR.mjs';
import '@vueuse/core';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './Icon-BLi68qcp.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import './useFormGroup-B3564yef.mjs';
import './nuxt-link-DAFz7xX6.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    var _a;
    const { t } = useI18n();
    const router = useRouter();
    const authStore = useAuthStore();
    const loading = ref(false);
    const countdown = ref(0);
    const form = ref({
      phone: ((_a = authStore.user) == null ? void 0 : _a.phone) || "",
      code: "",
      newPassword: ""
    });
    const confirmPassword = ref("");
    const rules = {
      phone: [
        { required: true, message: t("messages.signin.student.form.phone.required") },
        { pattern: /^1[3-9]\d{9}$/, message: t("messages.signin.student.form.phone.invalid") }
      ],
      code: [
        { required: true, message: t("messages.signin.validation.rules.captcha.required") },
        { length: 6, message: t("messages.signin.validation.rules.captcha.length") }
      ],
      newPassword: [
        { required: true, message: t("messages.auth.updatePassword.validation.newPasswordRequired") },
        { min: 6, message: t("messages.auth.updatePassword.validation.newPasswordLength") },
        { max: 20, message: t("messages.auth.updatePassword.validation.newPasswordMaxLength") },
        {
          pattern: /^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]*$/,
          message: t("messages.auth.updatePassword.validation.newPasswordFormat")
        }
      ],
      confirmPassword: [
        { required: true, message: t("messages.auth.updatePassword.validation.confirmPasswordRequired") }
      ]
    };
    const sendCode = async () => {
      if (!form.value.phone) {
        const toast = useToast();
        toast.add({
          title: t("messages.signin.student.form.phone.required"),
          color: "red",
          timeout: 3e3
        });
        return;
      }
      try {
        loading.value = true;
        await authApi.sendSmsCode(form.value.phone, 2);
        countdown.value = 60;
        const timer = setInterval(() => {
          countdown.value--;
          if (countdown.value <= 0) {
            clearInterval(timer);
          }
        }, 1e3);
        const toast = useToast();
        toast.add({
          title: t("messages.toast.sms.success.title"),
          description: t("messages.toast.sms.success.description"),
          color: "green",
          timeout: 3e3
        });
      } catch (error) {
        const toast = useToast();
        toast.add({
          title: t("messages.toast.sms.error.title"),
          description: error.message || t("messages.toast.sms.error.description"),
          color: "red",
          timeout: 3e3
        });
      } finally {
        loading.value = false;
      }
    };
    const handleSubmit = async () => {
      if (form.value.newPassword !== confirmPassword.value) {
        const toast = useToast();
        toast.add({
          title: t("messages.auth.updatePassword.validation.passwordMismatch"),
          color: "red",
          timeout: 3e3
        });
        return;
      }
      try {
        loading.value = true;
        await authApi.updatePasswordSms(form.value);
        const toast = useToast();
        toast.add({
          title: t("messages.auth.updatePassword.toast.success.title"),
          description: t("messages.auth.updatePassword.toast.success.description"),
          color: "green",
          timeout: 3e3
        });
        router.push("/student/account");
      } catch (error) {
        const toast = useToast();
        toast.add({
          title: t("messages.auth.updatePassword.toast.error.title"),
          description: error.message || t("messages.auth.updatePassword.toast.error.description"),
          color: "red",
          timeout: 3e3
        });
      } finally {
        loading.value = false;
      }
    };
    const canSendCode = computed(() => {
      return !loading.value && countdown.value === 0;
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UForm = __nuxt_component_2;
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_UButton = __nuxt_component_2$1;
      _push(`<!--[--><div class="sm:hidden fixed top-0 left-0 right-0 z-20 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b h-12 flex items-center justify-center"><h1 class="text-base font-semibold">${ssrInterpolate(unref(t)("messages.auth.updatePassword.title"))}</h1></div><div class="max-w-4xl mx-auto px-4 md:px-6 pt-[calc(48px+env(safe-area-inset-top))] md:pt-6 pb-6"><div class="bg-white rounded-xl border border-gray-200 p-6">`);
      _push(ssrRenderComponent(_component_UForm, {
        state: unref(form),
        rules,
        class: "space-y-6",
        onSubmit: handleSubmit
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: unref(t)("messages.signin.student.form.phone"),
              name: "phone"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                var _a2, _b;
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(form).phone,
                    "onUpdate:modelValue": ($event) => unref(form).phone = $event,
                    type: "tel",
                    placeholder: unref(t)("messages.signin.student.form.phonePlaceholder"),
                    disabled: !!((_a2 = unref(authStore).user) == null ? void 0 : _a2.phone)
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(form).phone,
                      "onUpdate:modelValue": ($event) => unref(form).phone = $event,
                      type: "tel",
                      placeholder: unref(t)("messages.signin.student.form.phonePlaceholder"),
                      disabled: !!((_b = unref(authStore).user) == null ? void 0 : _b.phone)
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "disabled"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: unref(t)("messages.signin.student.form.verificationCode"),
              name: "code"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex space-x-4"${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(form).code,
                    "onUpdate:modelValue": ($event) => unref(form).code = $event,
                    class: "flex-1",
                    placeholder: unref(t)("messages.signin.student.form.verificationCodePlaceholder")
                  }, null, _parent3, _scopeId2));
                  _push3(ssrRenderComponent(_component_UButton, {
                    disabled: !unref(canSendCode),
                    loading: unref(loading),
                    onClick: sendCode,
                    color: "primary"
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate(unref(countdown) > 0 ? unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) }) : unref(t)("messages.signin.student.form.sendCode"))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString(unref(countdown) > 0 ? unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) }) : unref(t)("messages.signin.student.form.sendCode")), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex space-x-4" }, [
                      createVNode(_component_UInput, {
                        modelValue: unref(form).code,
                        "onUpdate:modelValue": ($event) => unref(form).code = $event,
                        class: "flex-1",
                        placeholder: unref(t)("messages.signin.student.form.verificationCodePlaceholder")
                      }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                      createVNode(_component_UButton, {
                        disabled: !unref(canSendCode),
                        loading: unref(loading),
                        onClick: sendCode,
                        color: "primary"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(unref(countdown) > 0 ? unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) }) : unref(t)("messages.signin.student.form.sendCode")), 1)
                        ]),
                        _: 1
                      }, 8, ["disabled", "loading"])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: unref(t)("messages.auth.updatePassword.form.newPassword"),
              name: "newPassword"
            }, {
              help: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<p class="text-xs text-gray-500"${_scopeId2}>${ssrInterpolate(unref(t)("messages.auth.updatePassword.form.passwordHint"))}</p>`);
                } else {
                  return [
                    createVNode("p", { class: "text-xs text-gray-500" }, toDisplayString(unref(t)("messages.auth.updatePassword.form.passwordHint")), 1)
                  ];
                }
              }),
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(form).newPassword,
                    "onUpdate:modelValue": ($event) => unref(form).newPassword = $event,
                    type: "password",
                    placeholder: unref(t)("messages.auth.updatePassword.form.newPasswordPlaceholder")
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(form).newPassword,
                      "onUpdate:modelValue": ($event) => unref(form).newPassword = $event,
                      type: "password",
                      placeholder: unref(t)("messages.auth.updatePassword.form.newPasswordPlaceholder")
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UFormGroup, {
              label: unref(t)("messages.auth.updatePassword.form.confirmPassword"),
              name: "confirmPassword"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_UInput, {
                    modelValue: unref(confirmPassword),
                    "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                    type: "password",
                    placeholder: unref(t)("messages.auth.updatePassword.form.confirmPasswordPlaceholder")
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(confirmPassword),
                      "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                      type: "password",
                      placeholder: unref(t)("messages.auth.updatePassword.form.confirmPasswordPlaceholder")
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`<div class="flex justify-end space-x-4"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UButton, {
              variant: "ghost",
              to: "/student/account"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(unref(t)("messages.auth.updatePassword.buttons.cancel"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(unref(t)("messages.auth.updatePassword.buttons.cancel")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_UButton, {
              type: "submit",
              color: "primary",
              loading: unref(loading)
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate(unref(t)("messages.auth.updatePassword.buttons.confirm"))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString(unref(t)("messages.auth.updatePassword.buttons.confirm")), 1)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode(_component_UFormGroup, {
                label: unref(t)("messages.signin.student.form.phone"),
                name: "phone"
              }, {
                default: withCtx(() => {
                  var _a2;
                  return [
                    createVNode(_component_UInput, {
                      modelValue: unref(form).phone,
                      "onUpdate:modelValue": ($event) => unref(form).phone = $event,
                      type: "tel",
                      placeholder: unref(t)("messages.signin.student.form.phonePlaceholder"),
                      disabled: !!((_a2 = unref(authStore).user) == null ? void 0 : _a2.phone)
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "disabled"])
                  ];
                }),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: unref(t)("messages.signin.student.form.verificationCode"),
                name: "code"
              }, {
                default: withCtx(() => [
                  createVNode("div", { class: "flex space-x-4" }, [
                    createVNode(_component_UInput, {
                      modelValue: unref(form).code,
                      "onUpdate:modelValue": ($event) => unref(form).code = $event,
                      class: "flex-1",
                      placeholder: unref(t)("messages.signin.student.form.verificationCodePlaceholder")
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"]),
                    createVNode(_component_UButton, {
                      disabled: !unref(canSendCode),
                      loading: unref(loading),
                      onClick: sendCode,
                      color: "primary"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(unref(countdown) > 0 ? unref(t)("messages.signin.student.form.resendCode", { countdown: unref(countdown) }) : unref(t)("messages.signin.student.form.sendCode")), 1)
                      ]),
                      _: 1
                    }, 8, ["disabled", "loading"])
                  ])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: unref(t)("messages.auth.updatePassword.form.newPassword"),
                name: "newPassword"
              }, {
                help: withCtx(() => [
                  createVNode("p", { class: "text-xs text-gray-500" }, toDisplayString(unref(t)("messages.auth.updatePassword.form.passwordHint")), 1)
                ]),
                default: withCtx(() => [
                  createVNode(_component_UInput, {
                    modelValue: unref(form).newPassword,
                    "onUpdate:modelValue": ($event) => unref(form).newPassword = $event,
                    type: "password",
                    placeholder: unref(t)("messages.auth.updatePassword.form.newPasswordPlaceholder")
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_UFormGroup, {
                label: unref(t)("messages.auth.updatePassword.form.confirmPassword"),
                name: "confirmPassword"
              }, {
                default: withCtx(() => [
                  createVNode(_component_UInput, {
                    modelValue: unref(confirmPassword),
                    "onUpdate:modelValue": ($event) => isRef(confirmPassword) ? confirmPassword.value = $event : null,
                    type: "password",
                    placeholder: unref(t)("messages.auth.updatePassword.form.confirmPasswordPlaceholder")
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode("div", { class: "flex justify-end space-x-4" }, [
                createVNode(_component_UButton, {
                  variant: "ghost",
                  to: "/student/account"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(t)("messages.auth.updatePassword.buttons.cancel")), 1)
                  ]),
                  _: 1
                }),
                createVNode(_component_UButton, {
                  type: "submit",
                  color: "primary",
                  loading: unref(loading)
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(t)("messages.auth.updatePassword.buttons.confirm")), 1)
                  ]),
                  _: 1
                }, 8, ["loading"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><!--]-->`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/student/password/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-BwEXCgfl.mjs.map
