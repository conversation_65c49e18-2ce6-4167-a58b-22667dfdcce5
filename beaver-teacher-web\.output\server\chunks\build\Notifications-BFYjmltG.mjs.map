{"version": 3, "file": "Notifications-BFYjmltG.mjs", "sources": ["../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/ui.config/overlays/notification.js", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/ui.config/overlays/notifications.js", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/composables/useTimer.js", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/overlays/Notification.vue", "../../../../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/overlays/Notifications.vue"], "sourcesContent": null, "names": ["config", "_sfc_main", "UIcon", "UAvat<PERSON>", "UButton", "_ssrRenderAttrs", "_mergeProps", "_ssrRenderClass", "_ssrRenderComponent", "_ssrInterpolate", "_ssrRenderList", "_ssrRenderStyle", "UNotification", "_push", "notification", "_createSlots", "_renderList", "_withCtx", "_parent", "_ssrRenderSlot", "_renderSlot"], "mappings": "", "x_google_ignoreList": [0, 1, 2, 3, 4]}