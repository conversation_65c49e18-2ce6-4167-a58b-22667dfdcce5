{"name": "consola", "version": "3.2.3", "description": "<PERSON><PERSON><PERSON> Con<PERSON>e <PERSON>", "keywords": ["console", "logger", "reporter", "elegant", "cli", "universal", "unified", "prompt", "clack", "format", "error", "stacktrace"], "repository": "unjs/consola", "license": "MIT", "type": "module", "exports": {".": {"node": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./lib/index.cjs"}, "default": {"types": "./dist/browser.d.ts", "import": "./dist/browser.mjs"}}, "./browser": {"types": "./dist/browser.d.ts", "import": "./dist/browser.mjs"}, "./basic": {"node": {"types": "./dist/basic.d.ts", "import": "./dist/basic.mjs", "require": "./dist/basic.cjs"}, "default": {"types": "./dist/browser.d.ts", "import": "./dist/browser.mjs"}}, "./core": {"types": "./dist/core.d.ts", "import": "./dist/core.mjs", "require": "./dist/core.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "main": "./lib/index.cjs", "module": "./dist/index.mjs", "browser": "./dist/browser.mjs", "types": "./dist/index.d.ts", "files": ["dist", "lib", "*.d.ts"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src examples test", "lint:fix": "eslint . --fix && prettier -w src examples test", "release": "pnpm test && pnpm build && changelogen --release --push && npm publish", "test": "pnpm lint && pnpm vitest run --coverage"}, "devDependencies": {"@clack/core": "^0.3.2", "@types/node": "^20.3.3", "@vitest/coverage-v8": "^0.32.2", "changelogen": "^0.5.3", "defu": "^6.1.2", "eslint": "^8.44.0", "eslint-config-unjs": "^0.2.1", "is-unicode-supported": "^1.3.0", "jiti": "^1.18.2", "lodash": "^4.17.21", "prettier": "^3.0.0", "sentencer": "^0.2.1", "sisteransi": "^1.0.5", "std-env": "^3.3.3", "string-width": "^6.1.0", "typescript": "^5.1.6", "unbuild": "^1.2.1", "vitest": "^0.32.2"}, "engines": {"node": "^14.18.0 || >=16.10.0"}, "packageManager": "pnpm@8.6.5"}