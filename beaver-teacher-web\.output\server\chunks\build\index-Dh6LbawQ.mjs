import { _ as __nuxt_component_1 } from './Badge-BbAwiPBc.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { defineComponent, ref, mergeProps, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderList, ssrRenderAttr, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import './server.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:util';
import 'node:url';
import 'node:net';
import 'node:fs';
import 'node:path';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './Icon-BLi68qcp.mjs';
import './index-eP-xd45t.mjs';
import 'node:process';
import 'node:tty';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const courses = ref([
      {
        id: 1,
        title: "\u5C11\u513F\u82F1\u8BED\u542F\u8499\u8BFE\u7A0B",
        level: "\u5165\u95E8\u7EA7",
        ageRange: "3-6\u5C81",
        description: "\u901A\u8FC7\u6E38\u620F\u3001\u6B4C\u66F2\u548C\u4E92\u52A8\u6D3B\u52A8\u57F9\u517B\u5B69\u5B50\u5BF9\u82F1\u8BED\u7684\u5174\u8DA3",
        duration: "45\u5206\u949F/\u8BFE",
        image: "/images/courses/english.png"
      },
      {
        id: 2,
        title: "\u9752\u5C11\u5E74\u82F1\u8BED\u8FDB\u9636\u8BFE\u7A0B",
        level: "\u4E2D\u7EA7",
        ageRange: "7-12\u5C81",
        description: "\u7CFB\u7EDF\u6027\u8BED\u6CD5\u5B66\u4E60\uFF0C\u63D0\u5347\u53E3\u8BED\u548C\u5199\u4F5C\u80FD\u529B",
        duration: "60\u5206\u949F/\u8BFE",
        image: "/images/courses/english.png"
      }
    ]);
    const router = useRouter();
    function viewCourseDetails(courseId) {
      router.push(`/course/${courseId}`);
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UBadge = __nuxt_component_1;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "container mx-auto px-4 py-8" }, _attrs))}><h1 class="text-3xl font-bold mb-8">\u7CBE\u54C1\u8BFE\u7A0B</h1><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><!--[-->`);
      ssrRenderList(courses.value, (course) => {
        _push(`<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"><img${ssrRenderAttr("src", course.image)}${ssrRenderAttr("alt", course.title)} class="w-full h-48 object-cover"><div class="p-6"><h2 class="text-xl font-bold mb-2">${ssrInterpolate(course.title)}</h2><div class="flex gap-2 mb-2">`);
        _push(ssrRenderComponent(_component_UBadge, null, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(course.level)}`);
            } else {
              return [
                createTextVNode(toDisplayString(course.level), 1)
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(ssrRenderComponent(_component_UBadge, null, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(course.ageRange)}`);
            } else {
              return [
                createTextVNode(toDisplayString(course.ageRange), 1)
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(`</div><p class="text-gray-600 mb-4">${ssrInterpolate(course.description)}</p><div class="flex justify-between items-center"><span class="text-sm text-gray-500">${ssrInterpolate(course.duration)}</span>`);
        _push(ssrRenderComponent(_component_UButton, {
          color: "gray",
          variant: "solid",
          onClick: ($event) => viewCourseDetails(course.id)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u4E86\u89E3\u8BE6\u60C5 `);
            } else {
              return [
                createTextVNode(" \u4E86\u89E3\u8BE6\u60C5 ")
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(`</div></div></div>`);
      });
      _push(`<!--]--></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/course/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-Dh6LbawQ.mjs.map
