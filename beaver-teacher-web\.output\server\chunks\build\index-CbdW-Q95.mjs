import { _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { defineComponent, ref, mergeProps, withCtx, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderList, ssrRenderAttr, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import './server.mjs';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:util';
import 'node:url';
import 'node:net';
import 'node:fs';
import 'node:path';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './index-eP-xd45t.mjs';
import 'node:process';
import 'node:tty';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './nuxt-link-DAFz7xX6.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const teachers = ref([
      {
        id: 1,
        name: "\u738B\u8001\u5E08",
        title: "\u8D44\u6DF1\u82F1\u8BED\u6559\u5E08",
        experience: "10\u5E74\u6559\u5B66\u7ECF\u9A8C",
        speciality: "\u5C11\u513F\u82F1\u8BED\u3001\u8BED\u97F3\u6559\u5B66",
        education: "\u82F1\u8BED\u6559\u80B2\u7855\u58EB",
        avatar: "/images/teachers/teacher.png",
        description: "\u4E13\u6CE8\u5C11\u513F\u82F1\u8BED\u6559\u5B66\uFF0C\u5584\u4E8E\u7528\u751F\u52A8\u6709\u8DA3\u7684\u65B9\u5F0F\u6FC0\u53D1\u5B66\u751F\u5B66\u4E60\u5174\u8DA3\u3002"
      },
      {
        id: 2,
        name: "Sarah Johnson",
        title: "\u5916\u7C4D\u82F1\u8BED\u6559\u5E08",
        experience: "8\u5E74\u6559\u5B66\u7ECF\u9A8C",
        speciality: "\u53E3\u8BED\u8BAD\u7EC3\u3001\u8DE8\u6587\u5316\u4EA4\u9645",
        education: "TESOL\u8BA4\u8BC1",
        avatar: "/images/teachers/teacher.png",
        description: "\u6765\u81EA\u7F8E\u56FD\uFF0C\u5177\u6709\u4E30\u5BCC\u7684ESL\u6559\u5B66\u7ECF\u9A8C\uFF0C\u64C5\u957F\u8425\u9020\u8F7B\u677E\u6109\u5FEB\u7684\u8BFE\u5802\u6C1B\u56F4\u3002"
      }
    ]);
    const router = useRouter();
    function viewTeacherProfile(teacherId) {
      router.push(`/teacher/${teacherId}`);
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UIcon = __nuxt_component_0;
      const _component_UButton = __nuxt_component_2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "container mx-auto px-4 py-8" }, _attrs))}><h1 class="text-3xl font-bold mb-8">\u5E08\u8D44\u529B\u91CF</h1><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><!--[-->`);
      ssrRenderList(teachers.value, (teacher) => {
        _push(`<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"><div class="relative"><img${ssrRenderAttr("src", teacher.avatar)}${ssrRenderAttr("alt", teacher.name)} class="w-full h-64 object-cover"><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4"><h2 class="text-white text-xl font-bold">${ssrInterpolate(teacher.name)}</h2><p class="text-white/90">${ssrInterpolate(teacher.title)}</p></div></div><div class="p-6"><div class="mb-4"><div class="flex items-center gap-2 mb-2">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-academic-cap",
          class: "text-gray-600"
        }, null, _parent));
        _push(`<span>${ssrInterpolate(teacher.education)}</span></div><div class="flex items-center gap-2 mb-2">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-clock",
          class: "text-gray-600"
        }, null, _parent));
        _push(`<span>${ssrInterpolate(teacher.experience)}</span></div><div class="flex items-center gap-2">`);
        _push(ssrRenderComponent(_component_UIcon, {
          name: "i-heroicons-star",
          class: "text-gray-600"
        }, null, _parent));
        _push(`<span>${ssrInterpolate(teacher.speciality)}</span></div></div><p class="text-gray-600">${ssrInterpolate(teacher.description)}</p>`);
        _push(ssrRenderComponent(_component_UButton, {
          class: "mt-4 w-full",
          color: "gray",
          variant: "solid",
          onClick: ($event) => viewTeacherProfile(teacher.id)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` \u67E5\u770B\u8BE6\u60C5 `);
            } else {
              return [
                createTextVNode(" \u67E5\u770B\u8BE6\u60C5 ")
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(`</div></div>`);
      });
      _push(`<!--]--></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/teacher/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-CbdW-Q95.mjs.map
