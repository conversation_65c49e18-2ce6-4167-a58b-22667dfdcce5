const resource = {
  "languageToggle": {
    "switchTo": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Switch to" } }
  },
  "messages": {
    "records": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Lessons" } },
      "datePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Select Date Range" } },
      "student": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Student" } },
      "courseType": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Type" } },
      "content": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Content" } },
      "homework": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Homework" } },
      "teacherComment": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teacher Comment" } },
      "studentFeedback": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Student Feedback" } },
      "remark": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remark" } },
      "noData": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No Data" } },
      "toast": {
        "fetchError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch records" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      }
    },
    "general": {
      "home": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Home" } }
    },
    "menu": {
      "course": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Courses" } },
      "teacher": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teaching Team" } },
      "account": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Account" } },
      "bookLesson": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Book Lesson" } },
      "myLessons": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "My Lessons" } },
      "lessons": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Lesson Schedule" } },
      "done": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Lesson Records" } }
    },
    "course": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Featured Courses" } },
      "learnMore": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Learn More" } },
      "level": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Level" } },
      "duration": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Duration" } },
      "ageRange": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Age Range" } }
    },
    "date": {
      "weekdays": {
        "sunday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sun" } },
        "monday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Mon" } },
        "tuesday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Tue" } },
        "wednesday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Wed" } },
        "thursday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Thu" } },
        "friday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Fri" } },
        "saturday": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sat" } }
      },
      "filter": {
        "all": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "All" } },
        "noLimit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No Limit" } }
      }
    },
    "teacher": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Our Teachers" } },
      "experience": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teaching Experience" } },
      "education": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Educational Background" } },
      "speciality": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Specialties" } },
      "viewProfile": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "View Profile" } }
    },
    "error": {
      "network": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Network error, please try again later" } },
      "invalidUsername": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Username not found. Please register a new account." } },
      "incorrectPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Incorrect password. Please try again." } },
      "passwordMismatch": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Passwords do not match. Please try again." } },
      "invalidPasswordResetLink": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password reset link is invalid." } },
      "noOrder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "We couldn't find your order. Please try again later." } },
      "invalidRequest": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Invalid request parameters" } },
      "unauthorized": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unauthorized, please login again" } },
      "forbidden": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Access denied" } },
      "notFound": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Requested resource not found" } },
      "serverError": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Server error, please try again later" } }
    },
    "join": {
      "title": {
        "curriculum": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Scientifically Designed Curriculum" } },
        "ageDesign": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Age-Appropriate Development Design" } }
      },
      "description": {
        "curriculum": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Progressive teaching approach that revisits themes across different grade levels, building connections and advancing step by step." } },
        "ageDesign": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course design based on students' language development patterns, from language learning to practical application, comprehensively supporting students' English learning growth." } }
      },
      "buttons": {
        "join": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Join Us" } },
        "joinUs": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Join us to be online English teacher" } },
        "signIn": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign in" } },
        "hasAccount": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Already have an account" } },
        "studentSignIn": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Student Login" } },
        "terms": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "View Terms of Service" } }
      }
    },
    "signin": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign in" } },
      "subtitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please sign in to your account" } },
      "form": {
        "email": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Email Address" } },
        "emailPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your email address" } },
        "password": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password" } },
        "passwordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your password" } },
        "rememberMe": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remember Me" } },
        "forgotPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Forgot Password?" } },
        "captcha": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Verification Code" } },
        "captchaPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter verification code" } }
      },
      "buttons": {
        "login": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign in" } },
        "noAccount": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Don't have an account?" } },
        "signup": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign Up Now" } }
      },
      "toast": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login Successful" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Welcome back, " } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign in failed" } },
          "student": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please check your phone number and verification code" } },
          "teacher": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please check your email and password" } },
          "captchaError": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Incorrect verification code" } },
          "smsCaptchaError": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Incorrect SMS verification code" } }
        }
      },
      "student": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Student Login" } },
        "subtitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login with phone number and verification code" } },
        "form": {
          "phone": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Phone Number" } },
          "phonePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your phone number" } },
          "verificationCode": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Verification Code" } },
          "verificationCodePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter verification code" } },
          "sendCode": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Send Code" } },
          "resendCode": { "t": 0, "b": { "t": 2, "i": [{ "t": 3, "v": "Retry in " }, { "t": 4, "k": "countdown" }, { "t": 3, "v": "s" }] } },
          "imageCaptcha": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Image Captcha" } },
          "imageCaptchaPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter image captcha" } },
          "switchToPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login with Password" } },
          "switchToSms": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login with SMS Code" } },
          "password": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password" } },
          "passwordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your password" } }
        },
        "loginHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "For first-time users, please login with SMS code, then you can set your password in account page" } }
      },
      "studentForgotPassword": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Message" } },
        "message": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please login with SMS code and change your password in the Account page." } }
      },
      "validation": {
        "required": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please fill in all required fields" } },
        "rules": {
          "email": {
            "required": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your email address" } },
            "invalid": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter a valid email address" } }
          },
          "password": {
            "required": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your password" } },
            "minLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password must be at least 6 characters" } }
          },
          "captcha": {
            "required": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter verification code" } },
            "length": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Verification code must be 4 characters" } }
          }
        }
      }
    },
    "forgotPassword": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset Password" } },
      "subtitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your email address and we'll send you a password reset link" } },
      "submit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Send Reset Link" } },
      "backToLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Login" } },
      "errors": {
        "emailRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Email address is required" } },
        "emailInvalid": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter a valid email address" } }
      },
      "toast": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Email Sent" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please check your inbox" } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Send Failed" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      },
      "success": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset Link Sent Successfully" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "We've sent a password reset link to your email address" } },
        "checkEmail": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Check Your Email" } },
        "emailInstructions": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please check your email inbox and click the reset link to create a new password. The link will expire in 30 minutes" } },
        "backToLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Login" } },
        "sendAnother": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Send Another Email" } }
      }
    },
    "resetPassword": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset Password" } },
      "subtitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your new password" } },
      "submit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset Password" } },
      "backToLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Login" } },
      "verifying": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Verifying Reset Link" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait while we verify your password reset link..." } }
      },
      "invalid": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Invalid Reset Link" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "This password reset link is invalid or has expired." } },
        "requestNewLink": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Request New Link" } },
        "backToLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Login" } }
      },
      "form": {
        "newPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "New Password" } },
        "newPasswordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Enter your new password" } },
        "confirmPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Password" } },
        "confirmPasswordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm your new password" } }
      },
      "errors": {
        "passwordRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password is required" } },
        "confirmPasswordRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please confirm your password" } },
        "passwordTooShort": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password must be at least 8 characters" } },
        "passwordComplexity": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password must contain uppercase letters, lowercase letters, and numbers" } },
        "passwordMismatch": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Passwords do not match" } },
        "invalidToken": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset link is invalid or has expired" } },
        "missing": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Missing" } },
        "missingTypes": {
          "uppercase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "uppercase letters" } },
          "lowercase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "lowercase letters" } },
          "numbers": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "numbers" } }
        }
      },
      "toast": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password Reset Successfully" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Your password has been reset successfully" } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Reset Failed" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to reset password. Please try again." } }
        }
      },
      "success": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password Reset Successfully" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Your password has been reset successfully. You can now log in with your new password." } },
        "goToLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Go to Login" } }
      }
    },
    "signup": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign up" } },
      "basicInfo": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Basic Information" } },
      "form": {
        "realName": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Full Name" } },
        "nickname": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Nick name" } },
        "avatar": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Avatar" } },
        "gender": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Gender" } },
        "male": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Male" } },
        "female": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Female" } },
        "birthDate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "BirthDate" } },
        "email": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Email Address" } },
        "phone": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Phone Number" } },
        "specialty": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Self intro." } },
        "specialtyHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "15 minimum words (education background, teaching experience\u2026)" } },
        "graduatedSchool": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Graduation Background" } },
        "voiceIntro": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Voice intro" } },
        "certificates": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Tags" } },
        "certificateName": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Tag Name" } },
        "certificateContent": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Tag Content" } },
        "addCertificate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Add Tag" } },
        "delete": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Delete" } },
        "emailHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Initial password will be sent to this mail address." } },
        "certificateNameHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "e.q. TESOL" } },
        "certificateContentHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "e.g. Certification for Teaching English to Speakers of Other Languages" } }
      },
      "submit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign up" } },
      "validation": {
        "realNameRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your real name" } },
        "nicknameRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your nickname" } },
        "avatarRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please upload your avatar" } },
        "birthDateRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please select your date of birth" } },
        "emailRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your email" } },
        "emailInvalid": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter a valid email address" } },
        "emailExists": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "This email address is already associated with an account, please " } },
        "emailExistsWithSignIn": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "sign in." } },
        "emailInReview": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "This email address is already applied, please wait a while and check the e-mail box." } },
        "emailAvailable": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "This email is available" } },
        "emailChecking": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Checking email..." } },
        "emailCheckError": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to check email, please try again" } },
        "phoneRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your phone number" } },
        "specialtyRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your specialty" } },
        "specialtyMinWords": { "t": 0, "b": { "t": 2, "i": [{ "t": 3, "v": "Please enter at least " }, { "t": 4, "k": "min" }, { "t": 3, "v": " words for your specialty" }] } },
        "graduatedSchoolRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your Graduation Background" } },
        "voiceRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please upload voice introduction" } },
        "phone": {
          "required": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your phone number" } },
          "invalid": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter a valid phone number" } }
        }
      },
      "success": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Applied successfully!" } },
        "thx": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Thank you " } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait a while and check the e-mail box" } },
        "backHome": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Home" } },
        "viewTeachers": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "View Teachers" } }
      },
      "toast": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Submission Successful" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait for review" } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Submission Failed" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again" } }
        }
      }
    },
    "components": {
      "upload": {
        "file": {
          "button": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Take cert. photo" } },
          "uploaded": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Uploaded: " } },
          "toast": {
            "error": {
              "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Upload Failed" } },
              "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again" } }
            }
          }
        },
        "audio": {
          "button": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Rec. voice" } },
          "toast": {
            "error": {
              "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Upload Failed" } },
              "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again" } }
            }
          }
        }
      }
    },
    "schedule": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "My Lesson Schedule" } },
      "addCourse": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Plan new lessons" } },
      "currentTime": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Current Time" } },
        "localTime": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Local Time" } },
        "utc8Time": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "UTC+8 Time" } },
        "lastUpdate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Last Update" } }
      },
      "courseType": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Type" } },
      "student": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Student" } },
      "remark": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remark" } },
      "status": {
        "cancelled": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancelled" } },
        "available": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Available" } },
        "booked": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Booked" } },
        "completed": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Completed" } },
        "started": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Started" } }
      },
      "countdown": {
        "canEnterIn5Min": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "(Classroom opens 5 minutes before start)" } },
        "canEnterNow": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "(You can enter now)" } }
      },
      "actions": {
        "book": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Book" } },
        "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remove" } },
        "enterClass": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Enter" } },
        "wait": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait" } }
      },
      "confirmation": {
        "cancelTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Cancellation" } },
        "cancelMessage": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Are you sure you want to cancel this course?" } }
      },
      "toast": {
        "fetchError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch course list" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "cancelSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancelled successfully" } }
        },
        "cancelError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to cancel" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again" } }
        },
        "enterClassError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to enter classroom" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to get classroom information, please try again later" } }
        },
        "enteringClass": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Entering classroom" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait..." } }
        },
        "preparingClass": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Preparing to enter class..." } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait" } }
        },
        "enteringClassSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Entering classroom" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Classroom will open in this page" } }
        },
        "enteringClassPopup": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Entering classroom" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Classroom will open in new window" } }
        }
      },
      "modal": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Plan New Lessons" } },
        "form": {
          "scheduleDate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Date" } },
          "startTime": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Start time (UTC+8)" } },
          "lessonsCount": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Lessons count" } },
          "lessonLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Lesson length" } },
          "breakLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Break length" } },
          "courseType": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Course Type" } },
          "maxStudents": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Max students" } },
          "courseTypes": {
            "oneToOne": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "One-to-One Course" } },
            "group": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Group Course" } }
          },
          "remark": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remark" } },
          "remarkPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter course remarks" } }
        },
        "buttons": {
          "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancel" } },
          "submit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Submit" } }
        },
        "timePicker": {
          "hour": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Hour" } },
          "minute": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Minute" } },
          "hourPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Hour" } },
          "minutePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Minute" } },
          "clear": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Clear" } },
          "confirm": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm" } }
        },
        "validation": {
          "scheduleDate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please select date" } },
          "startTime": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please select start time" } },
          "startTimeFormat": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter correct time format (HH:MM)" } },
          "lessonsCount": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter lessons count" } },
          "lessonLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter lesson length" } },
          "breakLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter valid break length" } },
          "maxStudents": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Max. students must be no more than 6." } },
          "studentRange": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Max. students must be between 2 and 6." } },
          "maxStudentsMin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Max. students must be no less than 2." } },
          "maxStudentsMax": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Max. students must be no more than 6." } }
        },
        "toast": {
          "success": {
            "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Created Successfully" } }
          },
          "error": {
            "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Creation Failed" } },
            "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again" } }
          }
        }
      }
    },
    "bookedCourses": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Booked Courses" } },
      "empty": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No booked courses" } }
    },
    "accessDenied": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Access Restricted" } },
      "studentOnly": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Only students can access this page" } }
    },
    "auth": {
      "logout": {
        "button": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign Out" } },
        "confirmTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Sign Out" } },
        "confirmMessage": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Are you sure you want to sign out?" } },
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign Out Successful" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "You have been signed out successfully" } }
        }
      },
      "updatePassword": {
        "button": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Change Password" } },
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Change Password" } },
        "form": {
          "oldPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Current Password" } },
          "oldPasswordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Enter current password" } },
          "newPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "New Password" } },
          "newPasswordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Enter new password" } },
          "confirmPassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Password" } },
          "confirmPasswordPlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Re-enter new password" } },
          "passwordHint": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password must be 6-20 characters and can contain letters, numbers, and special characters" } }
        },
        "validation": {
          "oldPasswordRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your current password" } },
          "newPasswordRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter a new password" } },
          "newPasswordLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password must be at least 6 characters" } },
          "newPasswordMaxLength": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password cannot exceed 20 characters" } },
          "newPasswordFormat": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password can only contain letters, numbers, and special characters" } },
          "confirmPasswordRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please confirm your new password" } },
          "passwordMismatch": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Passwords do not match" } },
          "sameAsOld": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "New password cannot be the same as current password" } }
        },
        "toast": {
          "success": {
            "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Password Updated" } },
            "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please sign in with your new password" } }
          },
          "error": {
            "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Update Failed" } },
            "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
          }
        },
        "buttons": {
          "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancel" } },
          "confirm": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Update Password" } }
        }
      }
    },
    "toast": {
      "error": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Error" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Operation failed, please try again later" } }
      },
      "sms": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "SMS Code Sent" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please check your phone for the verification code" } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to Send SMS" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      },
      "captcha": {
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to get verification code" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      }
    },
    "common": {
      "confirm": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm" } },
      "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancel" } },
      "comingSoon": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Coming Soon" } },
      "customerService": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Customer Service" } },
        "button": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Customer Service" } },
        "comingSoonMessage": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Online customer service will be available soon. Please stay tuned!" } },
        "hotline": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Customer Service Hotline: " } },
          "number": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "(020) 87654321" } },
          "workingHours": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Working Days 9:00-21:00" } }
        }
      },
      "footer": {
        "icp": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "\u7CA4ICP\u59072025416359\u53F7-1" } },
        "icpTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "ICP Registration Information" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Professional Language Learning Platform" } },
        "copyright": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "All rights reserved" } }
      }
    },
    "payment": {
      "callback": {
        "verifying": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Verifying payment result..." } },
        "pleaseWait": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait, do not close this page" } },
        "success": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Payment Successful!" } },
        "successDescription": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Your payment has been completed successfully" } },
        "failed": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Payment verification failed" } },
        "failedDescription": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please go back and try payment again" } },
        "buttons": {
          "backToBooking": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Booking" } },
          "backToAccount": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Account" } },
          "backToPurchase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back to Purchase" } },
          "back": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back" } }
        }
      },
      "success": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Payment Successful!" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Your order has been paid successfully" } },
        "buttons": {
          "viewCards": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "View Course Cards" } },
          "continuePurchase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Continue Purchase" } }
        }
      },
      "amountNotice": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Payment Notice" } },
        "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "The final payment amount may differ slightly from the listed price in decimal places due to normal rounding errors caused by currency conversion" } }
      }
    },
    "terms": {
      "student": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Terms of Service" } }
      },
      "loading": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Loading terms content..." } },
      "buttons": {
        "agree": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "I Agree" } },
        "decline": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "I Decline" } }
      },
      "toast": {
        "success": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Agreement Updated" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "You have successfully agreed to the terms of service" } }
        },
        "error": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Update Failed" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to update agreement status" } }
        },
        "loadError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to Load Terms" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unable to load terms content. Please try again later." } }
        }
      },
      "teacher": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Terms of Service" } },
        "loading": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Loading..." } },
        "buttons": {
          "agree": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Agree and Continue" } },
          "decline": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Decline" } },
          "back": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Back" } }
        },
        "toast": {
          "loadError": {
            "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to Load Terms" } },
            "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unable to load terms content. Please try again later." } }
          }
        }
      }
    },
    "popup": {
      "notice": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Notice" } },
        "confirm": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm" } }
      }
    },
    "profile": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Personal Center" } },
      "basicInfo": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Basic Information" } },
      "teacherInfo": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teacher Information" } },
      "certificates": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Certificates" } },
      "refresh": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Refresh" } },
      "preview": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Preview Image" } },
      "confirmDialog": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Sign Out" } },
        "message": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Are you sure you want to sign out?" } }
      },
      "toast": {
        "fetchError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch information" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      },
      "userInfo": {
        "username": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Username" } },
        "email": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Email" } },
        "phone": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Phone" } },
        "role": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Role" } },
        "registerTime": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Registration Date" } },
        "teacher": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teacher" } },
        "normalUser": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Normal User" } },
        "status": {
          "normal": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Active" } },
          "disabled": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Disabled" } },
          "pending": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Pending Review" } },
          "unknown": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unknown" } }
        }
      },
      "teacherInfos": {
        "teacherNo": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Teacher ID" } },
        "realName": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Full Name" } },
        "nickname": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Nickname" } },
        "gender": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Gender" } },
        "male": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Male" } },
        "female": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Female" } },
        "birthDate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Birth Date" } },
        "level": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Level" } },
        "education": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Education" } },
        "degree": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Degree" } },
        "graduatedSchool": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Graduation Background" } },
        "specialty": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Specialty" } },
        "hireDate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Hire Date" } },
        "lastTeachingTime": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Last Teaching Date" } }
      },
      "certificateInfo": {
        "certificate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Certificate" } },
        "skill": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Skill" } },
        "remark": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remark" } }
      }
    },
    "account": {
      "profile": {
        "avatar": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Avatar" } },
        "nicknameNotSet": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Nickname not set" } },
        "nicknamePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter nickname" } },
        "phoneNotSet": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Phone number not set" } },
        "save": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Save" } },
        "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancel" } }
      },
      "stats": {
        "totalLessons": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Total Lessons" } },
        "monthlyLessons": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "This Month" } },
        "lessonsUnit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "lessons" } },
        "myCards": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "My Course Cards" } },
        "availableCards": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Available cards" } },
        "timesUnit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "times" } },
        "view": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "View" } },
        "purchase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Purchase" } }
      },
      "menu": {
        "transactions": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Transaction History" } },
        "customerService": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Customer Service" } },
        "changePassword": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Change Password" } },
        "logout": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Sign Out" } }
      },
      "confirmDialog": {
        "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Confirm Sign Out" } },
        "message": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Are you sure you want to sign out?" } }
      },
      "toast": {
        "fetchError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch student information" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "fetchInfoError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch information" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "logoutSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Signed out successfully" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "You have been signed out successfully" } }
        },
        "logoutError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to sign out" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "avatarUpdateSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Avatar updated successfully" } }
        },
        "avatarUploadError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to upload avatar" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "nicknameUpdateSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Nickname updated successfully" } }
        },
        "nicknameUpdateError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to update nickname" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        }
      }
    },
    "service": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Customer Service" } },
      "status": {
        "online": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Online" } },
        "connecting": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Connecting" } },
        "offline": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Offline" } }
      },
      "buttons": {
        "newSession": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "New Session" } },
        "goLogin": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Go to Login" } },
        "cancel": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Cancel" } },
        "createSession": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Create Session" } }
      },
      "auth": {
        "loginRequired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please sign in first" } },
        "loginDescription": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "You need to sign in to use customer service" } },
        "pleaseWait": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please wait..." } }
      },
      "session": {
        "sessionList": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Session List" } },
        "noSessions": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No session records" } },
        "selectOrCreate": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Select a session or create a new one to start chatting" } },
        "newSessionTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "New Customer Service Session" } },
        "sessionTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Session Title" } },
        "sessionTitlePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter session title, e.g.: Course Inquiry" } },
        "initialMessage": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Initial Message" } },
        "initialMessagePlaceholder": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please enter your question" } },
        "closed": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Closed" } },
        "defaultTitle": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Inquiry" } }
      },
      "messages": {
        "noMessages": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No message records" } }
      },
      "toast": {
        "loginRequired": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please sign in first" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "You need to sign in to access customer service" } }
        },
        "fetchSessionsError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch session list" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "fetchMessagesError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch messages" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "incompleteInfo": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please complete all information" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Session title and initial message cannot be empty" } }
        },
        "createSessionSuccess": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Session created successfully" } }
        },
        "createSessionError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to create session" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "sendMessageError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to send message" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try again later" } }
        },
        "loginStatusAbnormal": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login status abnormal" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Abnormal login status detected, please sign in again" } }
        },
        "loginExpired": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login expired" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Your login has expired, please sign in again" } }
        },
        "fetchUserInfoError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Failed to fetch user information" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Please try to sign in again" } }
        },
        "websocketError": {
          "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "WebSocket connection error" } },
          "description": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Login has expired, please sign in again" } }
        }
      }
    },
    "transactions": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Transaction History" } },
      "card": {
        "cardNumber": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Card Number" } },
        "purchase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Purchase" } },
        "validUntil": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Valid Until" } },
        "timesUnit": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "times" } },
        "available": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "available" } },
        "usageRecords": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Usage Records" } },
        "noUsageRecords": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No usage records" } },
        "noCards": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No course card information" } },
        "courseType": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "One-on-One Course" } }
      },
      "status": {
        "expired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Expired" } },
        "active": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Active" } },
        "exhausted": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Exhausted" } },
        "refunded": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Refunded" } },
        "unknown": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unknown" } }
      }
    },
    "courseCards": {
      "title": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "My Course Cards" } },
      "buttons": {
        "purchase": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Purchase Cards" } },
        "buyNow": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Buy Now" } }
      },
      "cardType": {
        "oneToOne": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "One-on-One" } },
        "group": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Group Class" } }
      },
      "card": {
        "validUntil": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Valid Until" } },
        "remainingLessons": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Remaining Lessons" } },
        "noCards": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "No course cards" } }
      },
      "status": {
        "expired": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Expired" } },
        "active": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Active" } },
        "exhausted": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Exhausted" } },
        "refunded": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Refunded" } },
        "unknown": { "t": 0, "b": { "t": 2, "i": [{ "t": 3 }], "s": "Unknown" } }
      }
    }
  }
};

export { resource as default };
//# sourceMappingURL=en-US-ByxCvaPP.mjs.map
