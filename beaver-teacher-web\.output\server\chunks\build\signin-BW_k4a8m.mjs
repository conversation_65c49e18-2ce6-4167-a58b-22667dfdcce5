import { _ as __nuxt_component_8 } from './FormGroup-BI93kFKQ.mjs';
import { _ as __nuxt_component_9 } from './Input-DpMdbGFS.mjs';
import { u as useUI, _ as __nuxt_component_0 } from './Icon-BLi68qcp.mjs';
import { defineComponent, toRef, computed, useSSRContext, ref, mergeProps, unref, withCtx, createVNode, openBlock, createBlock, createTextVNode, toDisplayString, useId as useId$1 } from 'vue';
import { m as mergeConfig, a as appConfig, t as twMerge, b as twJoin, B as useI18n, f as useRouter, K as useAuthStore, i as useNuxtApp, M as authApi } from './server.mjs';
import { u as useFormGroup } from './useFormGroup-B3564yef.mjs';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderAttr, ssrRenderClass, ssrLooseContain, ssrGetDynamicModelProps, ssrRenderSlot } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import { _ as __nuxt_component_2 } from './Button-3EsiVOgL.mjs';
import { _ as __nuxt_component_2$1 } from './AppFooter-DaKEVHRU.mjs';
import './index-eP-xd45t.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:url';
import 'node:net';
import 'node:fs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import '@iconify/utils';
import 'consola/core';
import 'ipx';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'pinia';
import 'vue-router';
import '@vueuse/core';
import '@fortawesome/fontawesome-svg-core';
import '@fortawesome/vue-fontawesome';
import '@fortawesome/free-solid-svg-icons';
import 'axios';
import 'js-cookie';
import './nuxt-link-DAFz7xX6.mjs';

const useId = useId$1;
const checkbox = {
  wrapper: "relative flex items-start",
  container: "flex items-center h-5",
  base: "h-4 w-4 dark:checked:bg-current dark:checked:border-transparent dark:indeterminate:bg-current dark:indeterminate:border-transparent disabled:opacity-50 disabled:cursor-not-allowed focus:ring-0 focus:ring-transparent focus:ring-offset-transparent",
  form: "form-checkbox",
  rounded: "rounded",
  color: "text-{color}-500 dark:text-{color}-400",
  background: "bg-white dark:bg-gray-900",
  border: "border border-gray-300 dark:border-gray-700",
  ring: "focus-visible:ring-2 focus-visible:ring-{color}-500 dark:focus-visible:ring-{color}-400 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-gray-900",
  inner: "ms-3 flex flex-col",
  label: "text-sm font-medium text-gray-700 dark:text-gray-200",
  required: "text-sm text-red-500 dark:text-red-400",
  help: "text-sm text-gray-500 dark:text-gray-400",
  default: {
    color: "primary"
  }
};
const config = mergeConfig(appConfig.ui.strategy, appConfig.ui.checkbox, checkbox);
const _sfc_main$1 = defineComponent({
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: () => null
    },
    value: {
      type: [String, Number, Boolean, Object],
      default: null
    },
    modelValue: {
      type: [Boolean, Array],
      default: null
    },
    name: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    indeterminate: {
      type: Boolean,
      default: void 0
    },
    help: {
      type: String,
      default: null
    },
    label: {
      type: String,
      default: null
    },
    required: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: () => config.default.color,
      validator(value) {
        return appConfig.ui.colors.includes(value);
      }
    },
    inputClass: {
      type: String,
      default: ""
    },
    class: {
      type: [String, Object, Array],
      default: () => ""
    },
    ui: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["update:modelValue", "change"],
  setup(props, { emit }) {
    var _a;
    const { ui, attrs } = useUI("checkbox", toRef(props, "ui"), config, toRef(props, "class"));
    const { emitFormChange, color, name, inputId: _inputId } = useFormGroup(props);
    const inputId = (_a = _inputId.value) != null ? _a : useId("$I73VgP2LkT");
    const toggle = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        emit("update:modelValue", value);
      }
    });
    const onChange = (event) => {
      emit("change", event.target.checked);
      emitFormChange();
    };
    const inputClass = computed(() => {
      return twMerge(twJoin(
        ui.value.base,
        ui.value.form,
        ui.value.rounded,
        ui.value.background,
        ui.value.border,
        color.value && ui.value.ring.replaceAll("{color}", color.value),
        color.value && ui.value.color.replaceAll("{color}", color.value)
      ), props.inputClass);
    });
    return {
      // eslint-disable-next-line vue/no-dupe-keys
      ui,
      attrs,
      toggle,
      inputId,
      // eslint-disable-next-line vue/no-dupe-keys
      name,
      // eslint-disable-next-line vue/no-dupe-keys
      inputClass,
      onChange
    };
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  let _temp0;
  _push(`<div${ssrRenderAttrs(mergeProps({
    class: _ctx.ui.wrapper,
    "data-n-ids": _ctx.attrs["data-n-ids"]
  }, _attrs))}><div class="${ssrRenderClass(_ctx.ui.container)}"><input${ssrRenderAttrs((_temp0 = mergeProps({
    id: _ctx.inputId,
    checked: Array.isArray(_ctx.toggle) ? ssrLooseContain(_ctx.toggle, _ctx.value) : _ctx.toggle,
    name: _ctx.name,
    required: _ctx.required,
    value: _ctx.value,
    disabled: _ctx.disabled,
    indeterminate: _ctx.indeterminate,
    type: "checkbox",
    class: _ctx.inputClass
  }, _ctx.attrs), mergeProps(_temp0, ssrGetDynamicModelProps(_temp0, _ctx.toggle))))}></div>`);
  if (_ctx.label || _ctx.$slots.label) {
    _push(`<div class="${ssrRenderClass(_ctx.ui.inner)}"><label${ssrRenderAttr("for", _ctx.inputId)} class="${ssrRenderClass(_ctx.ui.label)}">`);
    ssrRenderSlot(_ctx.$slots, "label", { label: _ctx.label }, () => {
      _push(`${ssrInterpolate(_ctx.label)}`);
    }, _push, _parent);
    if (_ctx.required) {
      _push(`<span class="${ssrRenderClass(_ctx.ui.required)}">*</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</label>`);
    if (_ctx.help || _ctx.$slots.help) {
      _push(`<p class="${ssrRenderClass(_ctx.ui.help)}">`);
      ssrRenderSlot(_ctx.$slots, "help", { help: _ctx.help }, () => {
        _push(`${ssrInterpolate(_ctx.help)}`);
      }, _push, _parent);
      _push(`</p>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("../node_modules/.pnpm/@nuxt+ui@2.18.7_axios@1.7.7_magicast@0.3.5_rollup@4.24.2_vite@5.4.10_@types+node@22.8.2_sass@_o3y5ays644w627cb2ajjkrkrui/node_modules/@nuxt/ui/dist/runtime/components/forms/Checkbox.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const __nuxt_component_3 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "signin",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    const router = useRouter();
    useAuthStore();
    const form = ref({
      email: "",
      password: "",
      rememberMe: false,
      captcha: ""
    });
    const loading = ref(false);
    ({
      email: [
        { required: true, message: t("messages.signin.validation.rules.email.required") },
        { type: "email", message: t("messages.signin.validation.rules.email.invalid") }
      ],
      password: [
        { required: true, message: t("messages.signin.validation.rules.password.required") },
        { min: 6, message: t("messages.signin.validation.rules.password.minLength") }
      ],
      captcha: [
        { required: true, message: t("messages.signin.validation.rules.captcha.required") },
        { len: 4, message: t("messages.signin.validation.rules.captcha.length") }
      ]
    });
    const formState = ref({
      email: "",
      password: "",
      captcha: ""
    });
    const captchaImage = ref("");
    const captchaKey = ref("");
    useNuxtApp();
    async function getCaptcha() {
      try {
        const res = await authApi.getCaptcha();
        captchaImage.value = `data:image/png;base64,${res.image}`;
        captchaKey.value = res.key;
      } catch (error) {
        console.error("\u83B7\u53D6\u9A8C\u8BC1\u7801\u5931\u8D25:", error);
      }
    }
    function refreshCaptcha() {
      getCaptcha();
    }
    function goToSignUp() {
      router.push("/teacher/terms");
    }
    function goToForgotPassword() {
      router.push("/forgot-password");
    }
    return (_ctx, _push, _parent, _attrs) => {
      const _component_UFormGroup = __nuxt_component_8;
      const _component_UInput = __nuxt_component_9;
      const _component_UIcon = __nuxt_component_0;
      const _component_UCheckbox = __nuxt_component_3;
      const _component_UButton = __nuxt_component_2;
      const _component_AppFooter = __nuxt_component_2$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-0 bg-gray-50 flex flex-col" }, _attrs))}><div class="container mx-auto px-4 py-6 md:py-12 flex-1 safe-area"><div class="max-w-md mx-auto w-full"><div class="text-center mb-2"><h1 class="text-xl font-bold">${ssrInterpolate(unref(t)("messages.signin.title"))}</h1></div><div class="bg-white p-6 sm:p-8 rounded-xl border border-gray-200"><form class="space-y-4"><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: unref(t)("messages.signin.form.email"),
        error: unref(formState).email
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).email,
              "onUpdate:modelValue": ($event) => unref(form).email = $event,
              type: "email",
              placeholder: unref(t)("messages.signin.form.emailPlaceholder"),
              error: !!unref(formState).email,
              autocomplete: "email"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UInput, {
                modelValue: unref(form).email,
                "onUpdate:modelValue": ($event) => unref(form).email = $event,
                type: "email",
                placeholder: unref(t)("messages.signin.form.emailPlaceholder"),
                error: !!unref(formState).email,
                autocomplete: "email"
              }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: unref(t)("messages.signin.form.password"),
        error: unref(formState).password
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).password,
              "onUpdate:modelValue": ($event) => unref(form).password = $event,
              type: "password",
              placeholder: unref(t)("messages.signin.form.passwordPlaceholder"),
              error: !!unref(formState).password,
              autocomplete: "current-password"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_UInput, {
                modelValue: unref(form).password,
                "onUpdate:modelValue": ($event) => unref(form).password = $event,
                type: "password",
                placeholder: unref(t)("messages.signin.form.passwordPlaceholder"),
                error: !!unref(formState).password,
                autocomplete: "current-password"
              }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div>`);
      _push(ssrRenderComponent(_component_UFormGroup, {
        label: unref(t)("messages.signin.form.captcha"),
        error: unref(formState).captcha
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_UInput, {
              modelValue: unref(form).captcha,
              "onUpdate:modelValue": ($event) => unref(form).captcha = $event,
              placeholder: unref(t)("messages.signin.form.captchaPlaceholder"),
              error: !!unref(formState).captcha,
              autocomplete: "off",
              class: "flex-1"
            }, null, _parent2, _scopeId));
            _push2(`<div class="w-28 h-11 cursor-pointer flex items-center justify-center bg-gray-50 hover:bg-gray-100 rounded-lg ring-1 ring-gray-200" role="button"${ssrRenderAttr("aria-label", unref(t)("messages.signin.form.captcha"))}${_scopeId}>`);
            if (unref(captchaImage)) {
              _push2(`<img${ssrRenderAttr("src", unref(captchaImage))} alt="\u9A8C\u8BC1\u7801" class="w-full h-full object-contain"${_scopeId}>`);
            } else {
              _push2(ssrRenderComponent(_component_UIcon, {
                name: "i-heroicons-arrow-path",
                class: "w-5 h-5 text-gray-400"
              }, null, _parent2, _scopeId));
            }
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode(_component_UInput, {
                  modelValue: unref(form).captcha,
                  "onUpdate:modelValue": ($event) => unref(form).captcha = $event,
                  placeholder: unref(t)("messages.signin.form.captchaPlaceholder"),
                  error: !!unref(formState).captcha,
                  autocomplete: "off",
                  class: "flex-1"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "placeholder", "error"]),
                createVNode("div", {
                  class: "w-28 h-11 cursor-pointer flex items-center justify-center bg-gray-50 hover:bg-gray-100 rounded-lg ring-1 ring-gray-200",
                  role: "button",
                  "aria-label": unref(t)("messages.signin.form.captcha"),
                  onClick: refreshCaptcha
                }, [
                  unref(captchaImage) ? (openBlock(), createBlock("img", {
                    key: 0,
                    src: unref(captchaImage),
                    alt: "\u9A8C\u8BC1\u7801",
                    class: "w-full h-full object-contain"
                  }, null, 8, ["src"])) : (openBlock(), createBlock(_component_UIcon, {
                    key: 1,
                    name: "i-heroicons-arrow-path",
                    class: "w-5 h-5 text-gray-400"
                  }))
                ], 8, ["aria-label"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="flex items-center justify-between">`);
      _push(ssrRenderComponent(_component_UCheckbox, {
        modelValue: unref(form).rememberMe,
        "onUpdate:modelValue": ($event) => unref(form).rememberMe = $event,
        label: unref(t)("messages.signin.form.rememberMe")
      }, null, _parent));
      _push(ssrRenderComponent(_component_UButton, {
        variant: "link",
        color: "gray",
        onClick: goToForgotPassword
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(unref(t)("messages.signin.form.forgotPassword"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(unref(t)("messages.signin.form.forgotPassword")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      _push(ssrRenderComponent(_component_UButton, {
        type: "submit",
        block: "",
        size: "lg",
        loading: unref(loading),
        class: "mt-6",
        color: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-arrow-right-on-rectangle",
              class: "mr-2"
            }, null, _parent2, _scopeId));
            _push2(` ${ssrInterpolate(unref(t)("messages.signin.buttons.login"))}`);
          } else {
            return [
              createVNode(_component_UIcon, {
                name: "i-heroicons-arrow-right-on-rectangle",
                class: "mr-2"
              }),
              createTextVNode(" " + toDisplayString(unref(t)("messages.signin.buttons.login")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</form></div><div class="text-center mt-6"><p class="text-gray-600 mb-2">${ssrInterpolate(unref(t)("messages.signin.buttons.noAccount"))}</p>`);
      _push(ssrRenderComponent(_component_UButton, {
        block: "",
        size: "lg",
        color: "white",
        variant: "outline",
        onClick: goToSignUp
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_UIcon, {
              name: "i-heroicons-user-plus",
              class: "mr-2"
            }, null, _parent2, _scopeId));
            _push2(` ${ssrInterpolate(unref(t)("messages.signin.buttons.signup"))}`);
          } else {
            return [
              createVNode(_component_UIcon, {
                name: "i-heroicons-user-plus",
                class: "mr-2"
              }),
              createTextVNode(" " + toDisplayString(unref(t)("messages.signin.buttons.signup")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div>`);
      _push(ssrRenderComponent(_component_AppFooter, null, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/signin.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=signin-BW_k4a8m.mjs.map
